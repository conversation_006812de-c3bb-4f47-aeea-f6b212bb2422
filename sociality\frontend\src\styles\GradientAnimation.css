/* Prevent scrolling on auth page */
.auth-page-no-scroll {
  overflow: hidden !important;
  height: 100vh !important;
  position: fixed !important;
  width: 100% !important;
  top: 0;
  left: 0;
}

/* Ensure html and body have no scrolling on auth page */
html, body {
  overflow-x: hidden;
}

/* Enhanced Gradient Animation for Auth Page */
@keyframes enhancedGradientAnimation {
  0% {
    background-position: 0% 50%;
    opacity: 0.3;
  }
  25% {
    background-position: 50% 25%;
    opacity: 0.4;
  }
  50% {
    background-position: 100% 50%;
    opacity: 0.5;
  }
  75% {
    background-position: 50% 75%;
    opacity: 0.4;
  }
  100% {
    background-position: 0% 50%;
    opacity: 0.3;
  }
}

.animated-gradient-background {
  background-color: transparent; /* Changed from #080808 to transparent */
  position: fixed;
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
  z-index: -1;
  overflow: hidden;
  contain: layout style paint;
}

.animated-gradient-background::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(ellipse at center, rgba(0,204,133,0.2) 0%, rgba(0,121,185,0.2) 40%, rgba(0,0,0,0) 70%);
  opacity: 0.6; /* Increased from 0.4 to 0.6 */
  animation: enhancedGradientAnimation 25s ease-in-out infinite;
  z-index: -1;
  will-change: opacity, background-position;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Subtle accent light animation */
@keyframes accentLight {
  0% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

.accent-light {
  position: absolute;
  width: 600px;
  height: 600px;
  border-radius: 50%;
  background: radial-gradient(circle at center, rgba(0, 204, 133, 0.2) 0%, rgba(0, 121, 185, 0.15) 30%, rgba(0, 0, 0, 0) 70%);
  filter: blur(40px);
  z-index: -1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: accentLight 8s ease-in-out infinite;
  will-change: opacity, transform;
  backface-visibility: hidden;
  perspective: 1000px;
  contain: layout style paint;
}

.accent-light:nth-child(2) {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle at center, rgba(0, 121, 185, 0.15) 0%, rgba(0, 204, 133, 0.1) 40%, rgba(0, 0, 0, 0) 70%);
  animation-delay: 4s;
  animation-duration: 12s;
}

/* Floating particles animation */
@keyframes floatingParticle {
  0% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.2;
  }
  25% {
    opacity: 0.3;
  }
  50% {
    transform: translate(var(--x-distance), var(--y-distance)) rotate(var(--rotation));
    opacity: 0.4;
  }
  75% {
    opacity: 0.3;
  }
  100% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.2;
  }
}

.particle {
  position: absolute;
  background: linear-gradient(135deg, var(--start-color), var(--end-color));
  border-radius: 50%;
  filter: blur(var(--blur-amount));
  opacity: 0;
  z-index: -1;
  will-change: transform, opacity;
  animation: floatingParticle var(--duration) ease-in-out infinite;
  animation-delay: var(--delay);
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  contain: layout style paint;
}

/* Moving gradient background */
@keyframes gradientMove {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

.moving-gradient {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #050505 0%, #080808 25%, #060606 50%, #080808 75%, #050505 100%);
  background-size: 400% 400%;
  animation: gradientMove 15s ease infinite;
  z-index: -2;
  will-change: background-position;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  contain: layout style paint;
}

/* Animated lines */
@keyframes lineFloat {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0.1;
  }
  50% {
    transform: translateY(var(--y-move)) translateX(var(--x-move));
    opacity: 0.25;
  }
  100% {
    transform: translateY(0) translateX(0);
    opacity: 0.1;
  }
}

.animated-line {
  position: absolute;
  height: var(--height);
  width: var(--width);
  background: linear-gradient(90deg, transparent, var(--color), transparent);
  opacity: 0;
  border-radius: 100px;
  z-index: -1;
  animation: lineFloat var(--duration) ease-in-out infinite;
  animation-delay: var(--delay);
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  contain: layout style paint;
}

/* Glowing dots animation */
@keyframes glowPulse {
  0% {
    transform: scale(0.8);
    opacity: 0.15;
  }
  50% {
    transform: scale(1);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.15;
  }
}

.glow-dot {
  position: absolute;
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
  background: radial-gradient(circle at center, var(--color) 0%, transparent 70%);
  filter: blur(var(--blur));
  opacity: 0;
  z-index: -1;
  animation: glowPulse var(--duration) ease-in-out infinite;
  animation-delay: var(--delay);
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  contain: layout style paint;
}

/* Glass effect for the auth box */
.glass-effect {
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.05) !important;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3) !important;
}

/* Subtle border glow effect */
@keyframes borderGlow {
  0% {
    border-color: rgba(0, 204, 133, 0.1) !important;
  }
  50% {
    border-color: rgba(0, 121, 185, 0.1) !important;
  }
  100% {
    border-color: rgba(0, 204, 133, 0.1) !important;
  }
}

.glass-effect:hover {
  animation: borderGlow 4s ease infinite;
}
