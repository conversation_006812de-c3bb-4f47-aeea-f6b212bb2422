import {
	Flex,
	Box,
	FormControl,
	FormLabel,
	Input,
	InputGroup,
	HStack,
	InputRightElement,
	Stack,
	Button,
	Heading,
	Text,
	Link,
	Image,
	Center,
	VStack,
} from "@chakra-ui/react";
import { useState } from "react";
import { ViewIcon, ViewOffIcon } from "@chakra-ui/icons";
import { useSetRecoilState } from "recoil";
import { authScreenAtom, userAtom } from "../atoms";
import useShowToast from "../hooks/useShowToast";
import { setCurrentTabUser } from "../utils/api";

export default function SignupCard() {
	const [showPassword, setShowPassword] = useState(false);
	const setAuthScreen = useSetRecoilState(authScreenAtom);
	const [loading, setLoading] = useState(false);
	const [inputs, setInputs] = useState({
		name: "",
		username: "",
		email: "",
		password: "",
	});

	const showToast = useShowToast();
	const setUser = useSetRecoilState(userAtom);

	const handleSignup = async () => {
		setLoading(true);
		try {
			const res = await fetch("/api/users/signup", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(inputs),
			});
			const data = await res.json();

			if (data.error) {
				showToast("Error", data.error, "error");
				return;
			}

			// Store user data in tab-specific storage
			setCurrentTabUser(data);
			setUser(data);
		} catch (error) {
			showToast("Error", error, "error");
		} finally {
			setLoading(false);
		}
	};



	return (
		<Flex align={"center"} justify={"center"}>
			<VStack spacing={6} mx={"auto"} maxW={"lg"}>
				<Box
					rounded={"xl"}
					className="glass-effect"
					p={6}
					w={{
						base: "full",
						sm: "500px",
					}}
					transition={"all 0.3s ease"}
					_hover={{
						boxShadow: "0 8px 32px rgba(0, 204, 133, 0.15)"
					}}
					borderWidth="1px"
					borderColor="rgba(0, 204, 133, 0.2)"
					position="relative"
					_before={{
						content: '""',
						position: "absolute",
						top: 0,
						left: 0,
						right: 0,
						bottom: 0,
						borderRadius: "xl",
						background: "linear-gradient(145deg, rgba(0, 204, 133, 0.05), rgba(0, 121, 185, 0.05))",
						zIndex: -1,
					}}
				>
					<VStack spacing={4}>
						{/* Logo at the top - Responsive */}
						<Center w={"full"} mb={2}>
							<Image
								src="/icon.svg"
								alt="Sociality Logo"
								boxSize={{ base: "70px", sm: "80px" }}
								transition={"transform 0.3s ease"}
								_hover={{ transform: "scale(1.05)" }}
								animation="pulse 3s infinite"
								sx={{
									"@keyframes pulse": {
										"0%": { filter: "drop-shadow(0 0 0px rgba(0, 204, 133, 0.3))" },
										"50%": { filter: "drop-shadow(0 0 10px rgba(0, 204, 133, 0.5))" },
										"100%": { filter: "drop-shadow(0 0 0px rgba(0, 204, 133, 0.3))" }
									}
								}}
							/>
						</Center>

						<Text
							as="h1"
							lineHeight={1.3}
							fontSize={{ base: "2xl", sm: "3xl" }}
							color="#00CC85"
							fontWeight="bold"
							textAlign={"center"}
							mb={4}
							letterSpacing="wide"
							sx={{
								background: "linear-gradient(to right, #00CC85, #0079B9)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								display: "inline-block",
								width: "100%",
								paddingBottom: "4px" /* Add padding to ensure descenders are visible */
							}}
						>
							Create Sociality Account
						</Text>

						<HStack spacing={4} w={"full"}>
							<Box w={"full"}>
								<FormControl isRequired>
									<FormLabel color={"gray.300"}>Full name</FormLabel>
									<Input
										type='text'
										onChange={(e) => setInputs({ ...inputs, name: e.target.value })}
										value={inputs.name}
										bg={"rgba(0,0,0,0.2)"}
										borderColor={"rgba(255,255,255,0.1)"}
										color={"white"}
										_hover={{
											borderColor: "rgba(255,255,255,0.3)"
										}}
										_focus={{
											borderColor: "#00cc85",
											boxShadow: "0 0 0 1px #00cc85"
										}}
										fontSize={"md"}
										placeholder="Enter your full name"
									/>
								</FormControl>
							</Box>
							<Box w={"full"}>
								<FormControl isRequired>
									<FormLabel color={"gray.300"}>Username</FormLabel>
									<Input
										type='text'
										onChange={(e) => setInputs({ ...inputs, username: e.target.value })}
										value={inputs.username}
										bg={"rgba(0,0,0,0.2)"}
										borderColor={"rgba(255,255,255,0.1)"}
										color={"white"}
										_hover={{
											borderColor: "rgba(255,255,255,0.3)"
										}}
										_focus={{
											borderColor: "#00cc85",
											boxShadow: "0 0 0 1px #00cc85"
										}}
										fontSize={"md"}
										placeholder="Choose a username"
									/>
								</FormControl>
							</Box>
						</HStack>

						<FormControl isRequired mt={4}>
							<FormLabel color={"gray.300"}>Email address</FormLabel>
							<Input
								type='email'
								onChange={(e) => setInputs({ ...inputs, email: e.target.value })}
								value={inputs.email}
								bg={"rgba(0,0,0,0.2)"}
								borderColor={"rgba(255,255,255,0.1)"}
								color={"white"}
								_hover={{
									borderColor: "rgba(255,255,255,0.3)"
								}}
								_focus={{
									borderColor: "#00cc85",
									boxShadow: "0 0 0 1px #00cc85"
								}}
								fontSize={"md"}
								placeholder="Enter your email address"
							/>
						</FormControl>

						<FormControl isRequired mt={4}>
							<FormLabel color={"gray.300"}>Password</FormLabel>
							<InputGroup>
								<Input
									type={showPassword ? "text" : "password"}
									onChange={(e) => setInputs({ ...inputs, password: e.target.value })}
									value={inputs.password}
									bg={"rgba(0,0,0,0.2)"}
									borderColor={"rgba(255,255,255,0.1)"}
									color={"white"}
									_hover={{
										borderColor: "rgba(255,255,255,0.3)"
									}}
									_focus={{
										borderColor: "#00cc85",
										boxShadow: "0 0 0 1px #00cc85"
									}}
									fontSize={"md"}
									placeholder="Create a password"
								/>
								<InputRightElement h={"full"}>
									<Button
										variant={"ghost"}
										onClick={() => setShowPassword((showPassword) => !showPassword)}
										color={"gray.400"}
										_hover={{
											color: "white"
										}}
									>
										{showPassword ? <ViewIcon /> : <ViewOffIcon />}
									</Button>
								</InputRightElement>
							</InputGroup>
						</FormControl>

						<Button
							loadingText='Creating account'
							isLoading={loading}
							size='lg'
							bg={"#101010"}
							color={"white"}
							borderWidth={"1px"}
							borderColor={"rgba(0, 204, 133, 0.5)"}
							_hover={{
								bg: "#151515",
								transform: "translateY(-2px)",
								borderColor: "rgba(0, 204, 133, 0.7)",
								boxShadow: "0 4px 12px rgba(0, 204, 133, 0.3)"
							}}
							_active={{
								transform: "scale(0.98)",
								boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)"
							}}
							onClick={handleSignup}
							mt={6}
							width={"full"}
							transition={"all 0.3s ease"}
							borderRadius={"md"}
							fontWeight={"bold"}
							letterSpacing={"wide"}
							boxShadow="0 2px 6px rgba(0, 0, 0, 0.1)"
							position="relative"
							overflow="hidden"
							_before={{
								content: '""',
								position: "absolute",
								top: 0,
								left: 0,
								right: 0,
								bottom: 0,
								background: "linear-gradient(to right, rgba(0, 204, 133, 0.2), rgba(0, 121, 185, 0.2))",
								zIndex: 0
							}}
						>
							<Text
								position="relative"
								zIndex={1}
								sx={{
									background: "linear-gradient(to right,rgb(255, 255, 255),rgb(255, 255, 255))",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									display: "inline-block",
									paddingBottom: "2px" /* Add padding to ensure descenders are visible */
								}}
							>
								Sign up
							</Text>
						</Button>



						<Text align={"center"} color={"gray.400"} mt={4}>
							Already a user?{" "}
							<Text
								as="span"
								fontWeight="bold"
								onClick={() => setAuthScreen("login")}
								cursor="pointer"
								_hover={{
									textDecoration: "underline"
								}}
								sx={{
									background: "linear-gradient(to right, #00CC85, #0079B9)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									display: "inline-block",
									paddingBottom: "2px" /* Add padding to ensure descenders are visible */
								}}
							>
								Login
							</Text>
						</Text>
					</VStack>
				</Box>
			</VStack>
		</Flex>
	);
}
