/**
 * Responsive Homepage Styles
 * Mobile-first CSS with progressive enhancement for larger screens
 */

/* ===== MOBILE-FIRST BASE STYLES ===== */

/* Homepage container optimizations */
.homepage-container {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  padding: 0;
  margin: 0;
}

/* Post container responsive optimizations */
.threads-post-card {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  
  /* Mobile-first spacing */
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 12px;
  
  /* Smooth transitions */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Prevent layout shifts */
  contain: layout;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Post content responsive text */
.post-text-content {
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
}

/* Action buttons mobile optimization */
.post-action {
  width: 100%;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  justify-content: flex-start;
}

.post-action .clean-icon {
  min-width: 28px;
  min-height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;
}

/* Feed tabs mobile optimization */
.glass-tab {
  min-width: 90px;
  flex-shrink: 0;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;
}

/* Image gallery mobile optimization */
.image-container {
  width: 100%;
  max-width: 100%;
  border-radius: 12px;
  overflow: hidden;
  background-color: #101010;
  contain: layout;
  aspect-ratio: 16/9;
  min-height: 200px;
}

.post-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  will-change: opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  contain: paint;
}

/* Avatar responsive sizing */
.post-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

/* ===== SMALL MOBILE (375px+) ===== */
@media (min-width: 375px) {
  .threads-post-card {
    padding: 14px;
    margin-bottom: 14px;
    border-radius: 14px;
  }
  
  .post-text-content {
    font-size: 15px;
    line-height: 1.45;
  }
  
  .post-action {
    gap: 14px;
  }
  
  .post-action .clean-icon {
    min-width: 32px;
    min-height: 32px;
    border-radius: 8px;
  }
  
  .glass-tab {
    min-width: 100px;
    font-size: 15px;
    padding: 10px 18px;
  }
  
  .post-avatar {
    width: 36px;
    height: 36px;
  }
  
  .image-container {
    min-height: 220px;
  }
}

/* ===== LARGE MOBILE (414px+) ===== */
@media (min-width: 414px) {
  .threads-post-card {
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 16px;
  }
  
  .post-text-content {
    font-size: 16px;
    line-height: 1.5;
  }
  
  .post-action {
    gap: 16px;
  }
  
  .post-action .clean-icon {
    min-width: 36px;
    min-height: 36px;
    border-radius: 10px;
  }
  
  .glass-tab {
    min-width: 110px;
    font-size: 16px;
    padding: 12px 20px;
  }
  
  .post-avatar {
    width: 40px;
    height: 40px;
  }
  
  .image-container {
    min-height: 240px;
  }
}

/* ===== TABLET (768px+) ===== */
@media (min-width: 768px) {
  .threads-post-card {
    padding: 18px;
    margin-bottom: 18px;
    border-radius: 18px;
  }
  
  .post-text-content {
    font-size: 16px;
    line-height: 1.5;
  }
  
  .post-action {
    gap: 18px;
  }
  
  .post-action .clean-icon {
    min-width: 38px;
    min-height: 38px;
    border-radius: 12px;
  }
  
  .glass-tab {
    min-width: 120px;
    font-size: 16px;
    padding: 12px 24px;
  }
  
  .post-avatar {
    width: 44px;
    height: 44px;
  }
  
  .image-container {
    min-height: 280px;
  }
}

/* ===== DESKTOP (1024px+) ===== */
@media (min-width: 1024px) {
  .threads-post-card {
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 20px;
  }
  
  .post-action {
    gap: 20px;
  }
  
  .post-action .clean-icon {
    min-width: 40px;
    min-height: 40px;
  }
  
  .glass-tab {
    min-width: 130px;
    padding: 14px 28px;
  }
  
  .post-avatar {
    width: 48px;
    height: 48px;
  }
  
  .image-container {
    min-height: 320px;
  }
}

/* ===== TOUCH DEVICE OPTIMIZATIONS ===== */
@media (hover: none) and (pointer: coarse) {
  /* Optimize for touch devices */
  .post-action .clean-icon {
    min-width: 38px;
    min-height: 38px;
  }
  
  .glass-tab {
    min-height: 44px;
    padding: 12px 20px;
  }
  
  /* Remove hover effects on touch devices */
  .threads-post-card:hover {
    background: inherit;
    border-color: inherit;
  }
  
  .post-action .clean-icon:hover {
    background: transparent;
  }
  
  .glass-tab:hover {
    background: inherit;
    box-shadow: inherit;
  }
}

/* ===== LANDSCAPE ORIENTATION OPTIMIZATIONS ===== */
@media (max-width: 767px) and (orientation: landscape) {
  .threads-post-card {
    padding: 10px;
    margin-bottom: 10px;
  }
  
  .post-text-content {
    font-size: 14px;
    line-height: 1.3;
  }
  
  .post-action {
    gap: 10px;
  }
  
  .post-action .clean-icon {
    min-width: 32px;
    min-height: 32px;
  }
  
  .glass-tab {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  .image-container {
    min-height: 160px;
  }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  .threads-post-card,
  .post-action .clean-icon,
  .glass-tab,
  .post-image {
    transition: none;
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .threads-post-card {
    border-width: 2px;
  }
  
  .post-action .clean-icon {
    border: 1px solid currentColor;
  }
  
  .glass-tab {
    border: 1px solid currentColor;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.homepage-container * {
  box-sizing: border-box;
}

/* Optimize scrolling performance */
.homepage-container {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Optimize image loading */
.post-image {
  content-visibility: auto;
  contain-intrinsic-size: 300px;
}

/* Optimize layout calculations */
.threads-post-card {
  contain: layout style paint;
}
