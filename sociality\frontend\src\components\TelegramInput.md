# Telegram-Style Message Input

A modern, feature-rich message input component inspired by <PERSON><PERSON><PERSON>'s design, with full support for both light and dark modes.

## Features

### 🎨 **Dual Theme Support**
- **Light Mode**: Clean, bright interface with subtle shadows and borders
- **Dark Mode**: Elegant dark interface with glassmorphism effects
- **Auto-switching**: Seamlessly adapts to your app's color mode

### 📝 **Smart Text Input**
- Auto-resizing textarea (40px - 120px height)
- Enter to send, Shift+Enter for new lines
- Telegram-style placeholder and typography

### 📎 **Attachments**
- File attachment support
- Image preview with thumbnails
- Drag & drop ready (extendable)

### 😊 **Emoji Support**
- Quick emoji picker with common emojis
- Easy integration with full emoji libraries

### 🎤 **Voice Messages**
- Voice recording UI (implementation ready)
- Recording animation with pulse effect

### ⚡ **Performance**
- Memoized components for optimal re-renders
- Smooth CSS animations and transitions
- Responsive design for all screen sizes

## Theme Differences

### Light Mode
```css
.telegram-input-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.95) 100%);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
```

### Dark Mode
```css
.telegram-input-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}
```

## Usage

```jsx
import MessageInput from './components/MessageInput';

function ChatComponent() {
  const [messages, setMessages] = useState([]);

  return (
    <div className="chat-container">
      {/* Your message list */}
      <MessageInput setMessages={setMessages} />
    </div>
  );
}
```

## Customization

### Colors
The component uses Chakra UI's `useColorModeValue` hook for theme-aware colors:

```jsx
const textColor = useColorModeValue("gray.800", "white");
const containerBg = useColorModeValue("white", "#1a1a1a");
const borderColor = useColorModeValue("gray.200", "gray.700");
```

### CSS Classes
Override the CSS classes for custom styling:

- `.telegram-input-container` - Main input wrapper
- `.telegram-send-button` - Send button
- `.telegram-voice-button` - Voice recording button
- `.telegram-attach-button` - Attachment button
- `.telegram-emoji-button` - Emoji picker button

### Responsive Breakpoints
The component adapts to different screen sizes:

```jsx
<IconButton
  h="44px"          // Touch-friendly 44px minimum
  w="44px"
  size={{ base: "md", sm: "lg" }}
/>
```

## Integration with Existing Chat

The component is designed to integrate seamlessly with your existing chat system:

1. **Message Handling**: Pass your `setMessages` function
2. **User Context**: Uses Recoil atoms for user and conversation state
3. **Socket Integration**: Built-in socket support for real-time messaging
4. **Federated Messaging**: Supports cross-platform messaging

## Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## Dependencies

- `@chakra-ui/react` - UI components and theming
- `react-icons` - Icon library
- `recoil` - State management
- CSS backdrop-filter support for glassmorphism effects
