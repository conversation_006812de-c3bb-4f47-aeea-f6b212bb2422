describe('Focused User Workflow Stress Tests', () => {
  beforeEach(() => {
    cy.clearAppData();
  });

  describe('Authentication Stress Tests', () => {
    it('should handle rapid form switching', () => {
      cy.visit('/auth');
      
      // Rapidly switch between login and signup forms
      for (let i = 0; i < 10; i++) {
        cy.contains('Sign up').click();
        cy.wait(100);
        cy.contains('Login').click();
        cy.wait(100);
      }
      
      // Should remain functional
      cy.get('input[placeholder="Enter your username"]').should('be.visible');
      cy.get('input[placeholder="Enter your password"]').should('be.visible');
    });

    it('should handle network failures during auth', () => {
      // Intercept auth requests and simulate network failures
      cy.intercept('POST', '**/api/users/signup', { forceNetworkError: true }).as('signupError');
      cy.intercept('POST', '**/api/users/login', { forceNetworkError: true }).as('loginError');
      
      cy.visit('/auth');
      cy.contains('Sign up').click();
      
      // Fill valid data
      cy.get('input[placeholder="Enter your full name"]').type('Test User');
      cy.get('input[placeholder="Choose a username"]').type('testuser');
      cy.get('input[placeholder="Enter your email address"]').type('<EMAIL>');
      cy.get('input[placeholder="Create a password"]').type('password123');
      
      // Attempt signup - should handle network error gracefully
      cy.get('button').contains('Sign up').click();
      
      // Should remain on auth page
      cy.url().should('include', '/auth');
    });
  });

  describe('Memory and Performance Stress Tests', () => {
    it('should handle large amounts of localStorage data', () => {
      cy.window().then((win) => {
        const tabId = `tab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        win.sessionStorage.setItem('tabId', tabId);
        
        // Create large amounts of data
        for (let i = 0; i < 50; i++) {
          win.localStorage.setItem(`large-data-${i}`, 'A'.repeat(1000));
        }
        
        // Set user data
        const userKey = `user-threads-${tabId}`;
        win.localStorage.setItem(userKey, JSON.stringify({
          _id: 'stress-test-user',
          name: 'Stress Test User',
          email: '<EMAIL>',
          username: 'stressuser',
          bio: 'Bio with some text',
          profilePic: '',
          isProfileComplete: true
        }));
      });
      
      // Should still load without issues
      cy.visit('/');
      cy.wait(2000);
      cy.url().should('not.include', '/auth');
    });

    it('should handle rapid page reloads', () => {
      // Set up authenticated user
      cy.window().then((win) => {
        const tabId = `tab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        win.sessionStorage.setItem('tabId', tabId);
        const userKey = `user-threads-${tabId}`;
        win.localStorage.setItem(userKey, JSON.stringify({
          _id: 'reload-test-user',
          name: 'Reload Test User',
          email: '<EMAIL>',
          username: 'reloaduser',
          bio: 'Testing reloads',
          profilePic: '',
          isProfileComplete: true
        }));
      });
      
      // Mock API endpoints to prevent failures
      cy.intercept('GET', '**/api/**', { statusCode: 200, body: [] });
      
      cy.visit('/');
      
      // Rapidly reload the page multiple times
      for (let i = 0; i < 5; i++) {
        cy.reload();
        cy.wait(1000);
      }
      
      // Should handle gracefully
      cy.url().should('not.include', '/auth');
    });
  });

  describe('Edge Case Stress Tests', () => {
    it('should handle corrupted localStorage data', () => {
      // Handle uncaught exceptions that occur when parsing corrupted data
      cy.on('uncaught:exception', (err, runnable) => {
        // We expect JSON parsing errors when testing corrupted data
        if (err.message.includes('JSON') || err.message.includes('property name')) {
          return false; // Prevent Cypress from failing
        }
        return true;
      });
      
      cy.window().then((win) => {
        const tabId = `tab-corrupted-${Date.now()}`;
        win.sessionStorage.setItem('tabId', tabId);
        const userKey = `user-threads-${tabId}`;
        
        // Set corrupted JSON data
        win.localStorage.setItem(userKey, '{invalid json}');
      });
      
      // Should handle gracefully and redirect to auth
      cy.visit('/');
      cy.wait(3000);
      cy.url().should('include', '/auth');
    });

    it('should handle missing sessionStorage tabId', () => {
      cy.window().then((win) => {
        // Clear sessionStorage but set localStorage with some key
        win.sessionStorage.clear();
        win.localStorage.setItem('user-threads-unknown', JSON.stringify({
          _id: 'orphaned-user',
          name: 'Orphaned User',
          email: '<EMAIL>',
          username: 'orphaneduser',
          bio: 'Orphaned data',
          profilePic: '',
          isProfileComplete: true
        }));
      });
      
      // Should handle gracefully
      cy.visit('/');
      cy.wait(3000);
      cy.url().should('include', '/auth');
    });

    it('should handle extremely slow API responses', () => {
      // Set up authenticated user
      cy.window().then((win) => {
        const tabId = `tab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        win.sessionStorage.setItem('tabId', tabId);
        const userKey = `user-threads-${tabId}`;
        win.localStorage.setItem(userKey, JSON.stringify({
          _id: 'slow-api-user',
          name: 'Slow API User',
          email: '<EMAIL>',
          username: 'slowapiuser',
          bio: 'Testing slow APIs',
          profilePic: '',
          isProfileComplete: true
        }));
      });
      
      // Mock slow API responses
      cy.intercept('GET', '**/api/**', (req) => {
        req.reply({
          statusCode: 200,
          body: [],
          delay: 3000 // 3 second delay
        });
      });
      
      cy.visit('/');
      
      // Should eventually load (with extended timeout)
      cy.url().should('not.include', '/auth', { timeout: 20000 });
    });
  });

  describe('Complete Stress Workflow', () => {
    it('should handle end-to-end stress scenario with rapid operations', () => {
      cy.visit('/auth');
      
      // Simple stress test: rapid form switching and basic input
      for (let i = 0; i < 5; i++) {
        // Switch to signup
        cy.contains('Sign up').click();
        cy.wait(100);
        
        // Type some data rapidly
        cy.get('input[placeholder="Enter your full name"]').type('T', { delay: 0 });
        
        // Switch back to login
        cy.contains('Login').click();
        cy.wait(100);
        
        // Type some data
        cy.get('input[placeholder="Enter your username"]').type('u', { delay: 0 });
      }
      
      // Should remain functional - final check
      cy.url().should('include', '/auth');
      cy.contains('Login').should('be.visible');
    });
  });
});
