import { Avatar, Box, Button, Flex, Image, Spinner, Text, IconButton, Textarea, CloseButton, Menu, MenuButton, MenuList, MenuItem, Divider, useColorModeValue } from "@chakra-ui/react";
import { BsFillImageFill } from "react-icons/bs";
import Actions from "../components/Actions";
import { useEffect, useState, useRef } from "react";
import Comment from "../components/Comment";
import useGetUserProfile from "../hooks/useGetUserProfile";
import useShowToast from "../hooks/useShowToast";
import { useNavigate, useParams, Link, useLocation } from "react-router-dom";
import { formatDistanceToNow } from "date-fns";
import { useRecoilState, useRecoilValue } from "recoil";
import { userAtom, postsAtom } from "../atoms";
import { fetchWithSession } from "../utils/api";

const PostPage = () => {
	const { user, loading } = useGetUserProfile();
	const [posts, setPosts] = useRecoilState(postsAtom);
	const showToast = useShowToast();
	const { pid } = useParams();
	const currentUser = useRecoilValue(userAtom);
	const navigate = useNavigate();
	const location = useLocation();
	const [replyText, setReplyText] = useState("");
	const [replyImage, setReplyImage] = useState(null);
	const [imagePreview, setImagePreview] = useState(null);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [showReplies, setShowReplies] = useState(true);
	const imageRef = useRef(null);

	// Theme-aware colors
	const textColor = useColorModeValue("gray.800", "white");
	const mutedTextColor = useColorModeValue("gray.600", "gray.400");
	const cardBgColor = useColorModeValue("white", "#101010");

	// Additional theme colors for JSX elements
	const menuButtonColor = useColorModeValue("gray.600", "gray.500");
	const menuButtonHoverColor = useColorModeValue("#00CC85", "white");
	const menuListBg = useColorModeValue("white", "#101010");
	const menuListBorderColor = useColorModeValue("gray.300", "gray.700");
	const menuListColor = useColorModeValue("gray.800", "white");
	const menuListBoxShadow = useColorModeValue("0 4px 12px rgba(0, 0, 0, 0.15)", "0 4px 12px rgba(0, 0, 0, 0.2)");
	const deleteMenuItemColor = useColorModeValue("red.500", "red.400");
	const deleteMenuItemBg = useColorModeValue("white", "#101010");
	const deleteMenuItemHoverColor = useColorModeValue("red.600", "red.300");
	const copyMenuItemBg = useColorModeValue("white", "#101010");
	const copyMenuItemColor = useColorModeValue("gray.800", "white");
	const copyMenuItemHoverColor = useColorModeValue("#00CC85", "white");
	const commentInputBg = useColorModeValue("white", "#101010");
	const commentInputColor = useColorModeValue("gray.700", "gray.300");
	const imagePreviewCloseBg = useColorModeValue("rgba(255, 255, 255, 0.8)", "rgba(0, 0, 0, 0.7)");
	const imagePreviewCloseColor = useColorModeValue("black", "white");
	const commentInputBorderColor = useColorModeValue("gray.300", "gray.700");
	const replyButtonBg = useColorModeValue("gray.800", "white");
	const replyButtonColor = useColorModeValue("white", "black");
	const replyButtonHoverBg = useColorModeValue("gray.700", "gray.200");
	const repliesBorderColor = useColorModeValue("gray.300", "whiteAlpha.300");
	const repliesTextColor = useColorModeValue("black", "white");
	const repliesCountColor = useColorModeValue("gray.600", "gray.400");

	// Get the current post from the posts array
	const currentPost = posts.find((p) => p._id === pid);

	// Get highlighted reply ID from URL search params
	const searchParams = new URLSearchParams(location.search);
	const highlightReplyId = searchParams.get('highlight');

	useEffect(() => {
		const getPost = async () => {
			setPosts([]);
			try {
				const res = await fetchWithSession(`/api/posts/${pid}`);
				const data = await res.json();
				if (data.error) {
					showToast("Error", data.error, "error");
					return;
				}
				setPosts([data]);
			} catch (error) {
				showToast("Error", error.message, "error");
			}
		};
		getPost();
	}, [showToast, pid, setPosts]);

	const handleDeletePost = async () => {
		try {
			if (!window.confirm("Are you sure you want to delete this post?")) return;

			const res = await fetchWithSession(`/api/posts/${currentPost._id}`, {
				method: "DELETE",
			});
			const data = await res.json();
			if (data.error) {
				showToast("Error", data.error, "error");
				return;
			}
			showToast("Success", "Post deleted", "success");
			navigate(`/${user.username}`);
		} catch (error) {
			showToast("Error", error.message, "error");
		}
	};

	const handleImageChange = (e) => {
		const file = e.target.files[0];
		if (file && file.type.startsWith("image/")) {
			setReplyImage(file);
			const reader = new FileReader();
			reader.onload = () => {
				setImagePreview(reader.result);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleReplySubmit = async () => {
		if (!replyText.trim() && !replyImage) return;
		if (isSubmitting) return;

		setIsSubmitting(true);

		try {
			const formData = new FormData();
			formData.append("text", replyText);
			if (replyImage) {
				formData.append("img", replyImage);
			}

			const res = await fetchWithSession(`/api/posts/reply/${currentPost._id}`, {
				method: "PUT",
				body: formData,
			});

			const data = await res.json();
			if (data.error) {
				showToast("Error", data.error, "error");
				return;
			}

			console.log("Reply response data:", data);

			// Update the posts state with the new reply
			const updatedPosts = posts.map((p) => {
				if (p._id === currentPost._id) {
					const existingReplies = p.replies || [];
					return {
						...p,
						replies: [...existingReplies, data]
					};
				}
				return p;
			});

			console.log("Updated posts:", updatedPosts);
			setPosts(updatedPosts);
			setReplyText("");
			setReplyImage(null);
			setImagePreview(null);
			showToast("Success", "Reply posted successfully", "success");
		} catch (error) {
			showToast("Error", error.message, "error");
		} finally {
			setIsSubmitting(false);
		}
	};

	// Early returns after all Hooks have been called to follow Rules of Hooks
	if (!user && loading) {
		return (
			<Flex justifyContent={"center"}>
				<Spinner size={"xl"} />
			</Flex>
		);
	}

	if (!currentPost) return null;

	return (
		<>
			{/* Main Post */}
			<Box
				bg={cardBgColor}
				borderRadius="xl"
				p={4}
				mb={6}
				className="threads-post-card"
			>
				<Flex justifyContent="space-between" mb={4}>
					<Flex alignItems={"center"} gap={3}>
						<Link to={`/${user.username}`}>
							<Avatar src={user.profilePic} size={"md"} name={user.name} />
						</Link>
						<Flex flexDirection="column">
							<Link to={`/${user.username}`}>
								<Text fontSize={"sm"} fontWeight={"bold"} color={textColor} _hover={{ textDecoration: "underline" }}>
									{user.username}
								</Text>
							</Link>
							<Text fontSize="xs" color={mutedTextColor}>
								{formatDistanceToNow(new Date(currentPost.createdAt))} ago
							</Text>
						</Flex>
					</Flex>

					{/* Three dots menu */}
					<Box>
						<Menu placement="bottom-end" isLazy>
							<MenuButton
								as={IconButton}
								icon={
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="16"
										height="16"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
									>
										<circle cx="12" cy="12" r="1" />
										<circle cx="12" cy="5" r="1" />
										<circle cx="12" cy="19" r="1" />
									</svg>
								}
								variant="ghost"
								aria-label="Options"
								size="sm"
								color={menuButtonColor}
								_hover={{
									color: menuButtonHoverColor,
									bg: "rgba(0, 204, 133, 0.1)",
									borderColor: "rgba(0, 204, 133, 0.3)"
								}}
							/>
							<MenuList
								fontSize="sm"
								bg={menuListBg}
								borderColor={menuListBorderColor}
								color={menuListColor}
								minW="180px"
								p={2}
								boxShadow={menuListBoxShadow}
								className="glass-card"
							>
								{currentUser?._id === user._id ? (
									<MenuItem
										icon={
											<svg
												xmlns="http://www.w3.org/2000/svg"
												width="14"
												height="14"
												viewBox="0 0 24 24"
												fill="none"
												stroke="currentColor"
												strokeWidth="2"
												strokeLinecap="round"
												strokeLinejoin="round"
											>
												<path d="M3 6h18"></path>
												<path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
												<path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
												<line x1="10" y1="11" x2="10" y2="17"></line>
												<line x1="14" y1="11" x2="14" y2="17"></line>
											</svg>
										}
										color={deleteMenuItemColor}
										bg={deleteMenuItemBg}
										_hover={{
											bg: "rgba(229, 62, 62, 0.1)",
											color: deleteMenuItemHoverColor
										}}
										borderRadius="md"
										onClick={handleDeletePost}
									>
										Delete post
									</MenuItem>
								) : null}
								<MenuItem
									icon={
										<svg
											xmlns="http://www.w3.org/2000/svg"
											width="14"
											height="14"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="2"
											strokeLinecap="round"
											strokeLinejoin="round"
										>
											<rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
											<path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
										</svg>
									}
									bg={copyMenuItemBg}
									color={copyMenuItemColor}
									_hover={{
										bg: "rgba(0, 204, 133, 0.1)",
										color: copyMenuItemHoverColor
									}}
									borderRadius="md"
									onClick={() => {
										const currentURL = window.location.href;
										navigator.clipboard.writeText(currentURL);
										showToast("Success", "Post link copied to clipboard", "success");
									}}
								>
									Copy link
								</MenuItem>
							</MenuList>
						</Menu>
					</Box>
				</Flex>

				<Text
					my={3}
					color={textColor}
					overflowWrap="break-word"
					wordBreak="break-word"
					whiteSpace="pre-wrap"
					sx={{
						hyphens: "auto"
					}}
				>
					{currentPost.text}
				</Text>

				{currentPost.img && (
					<Box
						borderRadius="md"
						overflow={"hidden"}
						borderWidth="1px"
						borderColor="gray.700"
						mt={4}
					>
						<Image src={currentPost.img} w={"full"} />
					</Box>
				)}

				<Flex gap={3} mt={4}>
					<Actions post={currentPost} />
				</Flex>
			</Box>

			{/* Comment Input Box */}
			<Box
				bg={commentInputBg}
				borderRadius="md"
				borderWidth="1px"
				borderColor="rgba(0, 204, 133, 0.3)"
				boxShadow="0 0 0 1px rgba(0, 204, 133, 0.2)"
				p={4}
				mb={4}
				position="relative"
			>
				<Flex mt={2} mb={2} gap={3}>
					<Avatar
						src={currentUser?.profilePic}
						size="sm"
						name={currentUser?.username}
					/>
					<Flex
						direction="column"
						width="full"
					>
						<Textarea
							placeholder="Share your thoughts..."
							value={replyText}
							onChange={(e) => setReplyText(e.target.value)}
							resize="none"
							minH="60px"
							bg="transparent"
							border="none"
							p={0}
							_focus={{ border: "none", boxShadow: "none" }}
							fontSize="sm"
							color={commentInputColor}
						/>

						{/* Image Preview */}
						{imagePreview && (
							<Box
								position="relative"
								mt={2}
								mb={2}
								borderRadius="md"
								overflow="hidden"
								maxW="300px"
							>
								<Image
									src={imagePreview}
									maxH="150px"
									objectFit="cover"
								/>
								<IconButton
									icon={<CloseButton />}
									aria-label="Remove image"
									size="xs"
									position="absolute"
									top={1}
									right={1}
									bg={imagePreviewCloseBg}
									color={imagePreviewCloseColor}
									borderRadius="full"
									onClick={() => {
										setReplyImage(null);
										setImagePreview(null);
									}}
								/>
							</Box>
						)}

						<Flex justify="space-between" align="center" mt={2} borderTop="1px solid" borderColor={commentInputBorderColor} pt={2}>
							<Box>
								<input
									type="file"
									hidden
									ref={imageRef}
									onChange={handleImageChange}
								/>
								<IconButton
									aria-label="Add image"
									icon={<BsFillImageFill />}
									variant="ghost"
									colorScheme="gray"
									size="sm"
									onClick={() => imageRef.current.click()}
								/>
							</Box>

							<Button
								size="sm"
								bg={replyButtonBg}
								color={replyButtonColor}
								borderRadius="full"
								px={5}
								fontWeight="bold"
								_hover={{ bg: replyButtonHoverBg }}
								isDisabled={!replyText.trim() && !replyImage}
								isLoading={isSubmitting}
								onClick={handleReplySubmit}
							>
								Reply
							</Button>
						</Flex>
					</Flex>
				</Flex>
			</Box>

			{/* Replies Section */}
			{currentPost.replies?.length > 0 && (
				<Box
					borderTop={"1px solid"}
					borderBottom={"1px solid"}
					borderColor={repliesBorderColor}
					mt={4}
					py={2}
				>
					<Flex justifyContent="space-between" alignItems="center" mb={4}>
						<Text fontWeight="bold" color={repliesTextColor}>
							Replies
						</Text>
						<Flex alignItems="center" gap={4}>
							<Text color={repliesCountColor} fontSize="sm">
								{currentPost.replies.length}
							</Text>
							<Button
								variant="ghost"
								color={repliesCountColor}
								size="sm"
								onClick={() => setShowReplies(!showReplies)}
							>
								{showReplies ? "Hide" : "Show"}
							</Button>
						</Flex>
					</Flex>

					{showReplies && (
						<>
							{/* Render only top-level replies */}
							{(() => {
								// Filter out any undefined or invalid replies
								const validReplies = currentPost.replies?.filter(reply => reply && reply._id) || [];

								// Get only top-level replies (those without parentReplyId)
								let topLevelReplies = validReplies.filter(reply => !reply.parentReplyId);

								// Sort by popularity (likes count and replies count)
								topLevelReplies = topLevelReplies.sort((a, b) => {
									// Calculate engagement score based on likes and replies
									const aLikes = a.likes?.length || 0;
									const bLikes = b.likes?.length || 0;

									// Count replies to this comment
									const aReplies = validReplies.filter(r => r.parentReplyId === a._id).length;
									const bReplies = validReplies.filter(r => r.parentReplyId === b._id).length;

									// Total engagement score
									const aScore = aLikes + aReplies;
									const bScore = bLikes + bReplies;

									// If scores are equal, sort by newest first
									if (bScore === aScore) {
										return new Date(b.createdAt) - new Date(a.createdAt);
									}

									// Sort by engagement score (highest first)
									return bScore - aScore;
								});

								// If there's a highlighted reply, move it to the top
								if (highlightReplyId) {
									const highlightedReply = topLevelReplies.find(r => r._id === highlightReplyId);
									if (highlightedReply) {
										topLevelReplies = topLevelReplies.filter(r => r._id !== highlightReplyId);
										topLevelReplies.unshift(highlightedReply);
									}
								}

								return topLevelReplies.map((reply, index) => (
									<Box key={reply._id} mb={4}>
										{/* Add separator for highlighted reply */}
										{index === 0 && reply._id === highlightReplyId && (
											<>
												<Box
													mb={4}
													position="relative"
													_after={{
														content: '"Your Recent Reply"',
														position: "absolute",
														top: "50%",
														left: "50%",
														transform: "translate(-50%, -50%)",
														bg: "#101010",
														px: "12px",
														py: "4px",
														fontSize: "xs",
														fontWeight: "bold",
														color: "green.400",
														borderRadius: "full",
														border: "1px solid rgba(0, 204, 133, 0.3)"
													}}
												>
													<Divider borderColor="rgba(0, 204, 133, 0.3)" />
												</Box>
												{topLevelReplies.length > 1 && (
													<Box
														mb={4}
														position="relative"
														_after={{
															content: '"Other Replies"',
															position: "absolute",
															top: "50%",
															left: "50%",
															transform: "translate(-50%, -50%)",
															bg: "#101010",
															px: "12px",
															py: "4px",
															fontSize: "xs",
															color: "gray.500",
															borderRadius: "full"
														}}
													>
														<Divider borderColor="gray.700" />
													</Box>
												)}
											</>
										)}
										<Comment
											reply={reply}
											postId={currentPost._id}
											lastReply={index === topLevelReplies.length - 1}
											onReplyAdded={(newReply) => {
												const updatedPosts = posts.map((p) => {
													if (p._id === currentPost._id) {
														return {
															...p,
															replies: [...(p.replies || []), newReply],
														};
													}
													return p;
												});
												setPosts(updatedPosts);
											}}
											childReplies={validReplies.filter(r => r.parentReplyId === reply._id)}
											allReplies={validReplies}
											highlightId={highlightReplyId}
										/>
									</Box>
								));
							})()}
						</>
					)}
				</Box>
			)}
		</>
	);
};

export default PostPage;
