/**
 * Focused Security Tests
 * 
 * This test suite focuses on security aspects that are actually implemented 
 * in the current Sociality app, ensuring tests are realistic and actionable.
 */

describe('Focused Security Tests', () => {
  beforeEach(() => {
    cy.clearAppData();
  });

  describe('Authentication Security', () => {
    it('should not expose sensitive information in localStorage', () => {
      // Mock successful login
      cy.intercept('POST', '/api/users/login', {
        statusCode: 200,
        body: {
          _id: 'user123',
          name: 'Test User',
          email: '<EMAIL>',
          username: 'testuser',
          bio: 'Test bio',
          profilePic: '',
          isProfileComplete: true
        }
      }).as('loginRequest');

      cy.visit('/auth');
      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();
      cy.wait('@loginRequest');

      // Verify sensitive data is not stored in localStorage
      cy.window().then((win) => {
        const userData = win.localStorage.getItem('user-threads');
        if (userData) {
          const parsedData = JSON.parse(userData);
          expect(parsedData).to.not.have.property('password');
          expect(parsedData).to.not.have.property('token');
          expect(parsedData).to.not.have.property('refreshToken');
          expect(parsedData).to.not.have.property('sessionToken');
        }
      });
    });

    it('should prevent unauthorized access to protected routes', () => {
      // Try to access homepage without being logged in
      cy.visit('/');
      cy.url().should('include', '/auth');

      // Try to access profile page without being logged in
      cy.visit('/profile');
      cy.url().should('include', '/auth');

      // Try to access search page without being logged in
      cy.visit('/search');
      cy.url().should('include', '/auth');
    });

    it('should handle authentication state correctly', () => {
      // Login successfully
      cy.intercept('POST', '/api/users/login', {
        statusCode: 200,
        body: {
          _id: 'user123',
          name: 'Test User',
          username: 'testuser',
          isProfileComplete: true
        }
      }).as('loginRequest');

      cy.visit('/auth');
      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();
      cy.wait('@loginRequest');

      // Should redirect to homepage
      cy.url().should('eq', Cypress.config().baseUrl + '/');

      // Verify user state is set
      cy.window().then((win) => {
        const userData = win.localStorage.getItem('user-threads');
        expect(userData).to.exist;
        const parsedData = JSON.parse(userData);
        expect(parsedData.username).to.equal('testuser');
      });
    });
  });

  describe('Input Validation Security', () => {
    beforeEach(() => {
      // Login before testing protected features
      cy.intercept('POST', '/api/users/login', {
        statusCode: 200,
        body: {
          _id: 'user123',
          name: 'Test User',
          username: 'testuser',
          isProfileComplete: true
        }
      }).as('loginRequest');

      cy.visit('/auth');
      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();
      cy.wait('@loginRequest');
    });

    it('should handle malicious input in search', () => {
      // Mock search endpoint
      cy.intercept('GET', '/api/users/search*', {
        statusCode: 200,
        body: []
      }).as('searchUsers');

      cy.visit('/search');
      
      // Test various potentially malicious inputs
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        '"; DROP TABLE users; --',
        '../../../etc/passwd',
        'javascript:alert(1)',
        '{{7*7}}'
      ];

      maliciousInputs.forEach(input => {
        cy.get('input[placeholder*="Search users"]').clear().type(input);
        cy.wait('@searchUsers');
        
        // Verify the input doesn't cause any script execution
        cy.window().then((win) => {
          // Should not have any script-injected global variables
          expect(win.xssTest).to.be.undefined;
          expect(win.alert).to.be.a('function'); // Alert should still be the native function
        });
      });
    });

    it('should validate profile update inputs', () => {
      // Mock profile update endpoint
      cy.intercept('PUT', '/api/users/update', {
        statusCode: 200,
        body: {
          _id: 'user123',
          name: 'Updated Name',
          username: 'testuser',
          bio: 'Updated bio'
        }
      }).as('updateProfile');

      cy.visit('/update');

      // Test XSS in profile fields
      const xssPayload = '<script>alert("xss")</script>';
      
      cy.get('input[placeholder="John Doe"]').clear().type(xssPayload);
      cy.get('textarea[placeholder*="Tell us about yourself"]').clear().type(xssPayload);
      
      cy.contains('button', 'Update').click();
      cy.wait('@updateProfile');

      // Verify the malicious content doesn't execute
      cy.window().then((win) => {
        expect(win.xssExecuted).to.be.undefined;
      });
    });
  });

  describe('Data Privacy', () => {
    it('should not expose user data in network requests', () => {
      // Mock login with realistic response
      cy.intercept('POST', '/api/users/login', {
        statusCode: 200,
        body: {
          _id: 'user123',
          name: 'Test User',
          username: 'testuser',
          email: '<EMAIL>', // This should be filtered out in some contexts
          isProfileComplete: true
        }
      }).as('loginRequest');

      cy.visit('/auth');
      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();
      cy.wait('@loginRequest');

      // Check that the response doesn't contain sensitive information
      cy.get('@loginRequest').then((interception) => {
        const responseBody = interception.response.body;
        expect(responseBody).to.not.have.property('password');
        expect(responseBody).to.not.have.property('passwordHash');
        expect(responseBody).to.not.have.property('salt');
      });
    });

    it('should handle error messages safely', () => {
      // Mock login failure
      cy.intercept('POST', '/api/users/login', {
        statusCode: 400,
        body: {
          error: 'Invalid credentials'
        }
      }).as('loginFailure');

      cy.visit('/auth');
      cy.get('input[placeholder="Enter your username"]').type('nonexistent');
      cy.get('input[placeholder="Enter your password"]').type('wrongpassword');
      cy.contains('button', 'Login').click();
      cy.wait('@loginFailure');

      // Verify error message doesn't leak sensitive information
      cy.contains('Invalid credentials').should('be.visible');
      cy.contains('user not found').should('not.exist');
      cy.contains('password incorrect').should('not.exist');
      cy.contains('database error').should('not.exist');
    });
  });

  describe('Content Security Policy', () => {
    it('should have proper CSP headers', () => {
      cy.visit('/auth');
      
      cy.document().then((doc) => {
        // Check for CSP meta tag or verify through network tab
        const cspMeta = doc.querySelector('meta[http-equiv="Content-Security-Policy"]');
        if (cspMeta) {
          const cspContent = cspMeta.getAttribute('content');
          expect(cspContent).to.include("default-src 'self'");
        }
      });

      // Alternatively, check headers through network interception
      cy.intercept('GET', '/', (req) => {
        req.continue((res) => {
          // Check if CSP headers are present
          const cspHeader = res.headers['content-security-policy'];
          if (cspHeader) {
            expect(cspHeader).to.include("default-src");
          }
        });
      });
    });
  });

  describe('Session Management', () => {
    it('should regenerate session context after login', () => {
      // Get initial state
      cy.visit('/auth');
      cy.window().then((win) => {
        const initialStorage = win.localStorage.getItem('user-threads');
        expect(initialStorage).to.be.null;
      });

      // Login
      cy.intercept('POST', '/api/auth/login', {
        statusCode: 200,
        body: {
          _id: 'user123',
          name: 'Test User',
          username: 'testuser',
          isProfileComplete: true
        }
      }).as('loginRequest');

      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();
      cy.wait('@loginRequest');

      // Verify new session context
      cy.window().then((win) => {
        const newStorage = win.localStorage.getItem('user-threads');
        expect(newStorage).to.not.be.null;
        const userData = JSON.parse(newStorage);
        expect(userData._id).to.equal('user123');
      });
    });

    it('should clear session data on logout', () => {
      // Setup logged in state
      cy.window().then((win) => {
        win.localStorage.setItem('user-threads', JSON.stringify({
          _id: 'user123',
          name: 'Test User',
          username: 'testuser',
          isProfileComplete: true
        }));
      });

      cy.visit('/');
      
      // Simulate logout by clearing localStorage (since no logout UI exists yet)
      cy.window().then((win) => {
        win.localStorage.removeItem('user-threads');
      });

      // Refresh to see if auth redirect happens
      cy.reload();

      // Should redirect to auth page since no user data exists
      cy.url().should('include', '/auth');
    });
  });

  describe('WebSocket Security', () => {
    beforeEach(() => {
      // Login for WebSocket tests
      cy.intercept('POST', '/api/users/login', {
        statusCode: 200,
        body: {
          _id: 'user123',
          name: 'Test User',
          username: 'testuser',
          isProfileComplete: true
        }
      }).as('loginRequest');

      cy.visit('/auth');
      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();
      cy.wait('@loginRequest');
    });

    it('should authenticate WebSocket connections', () => {
      // Mock WebSocket connection
      cy.window().then((win) => {
        // Simulate WebSocket authentication check
        const mockWs = {
          readyState: 1,
          send: cy.stub(),
          close: cy.stub()
        };

        // Verify that authentication data is sent with WebSocket connection
        expect(win.localStorage.getItem('user-threads')).to.not.be.null;
      });
    });

    it('should validate WebSocket message origins', () => {
      cy.visit('/chat');
      
      cy.window().then((win) => {
        // Check that WebSocket messages are validated
        const userData = win.localStorage.getItem('user-threads');
        expect(userData).to.not.be.null;
        
        // In a real test, you would verify that incoming WebSocket messages
        // are properly validated for origin and format
      });
    });
  });

  describe('Rate Limiting Simulation', () => {
    it('should handle rapid login attempts gracefully', () => {
      cy.visit('/auth');

      // Simulate multiple rapid login attempts
      for (let i = 0; i < 3; i++) {
        cy.intercept('POST', '/api/users/login', {
          statusCode: 429,
          body: { error: 'Too many attempts. Please try again later.' }
        }).as(`loginAttempt${i}`);

        cy.get('input[placeholder="Enter your username"]').clear().type('testuser');
        cy.get('input[placeholder="Enter your password"]').clear().type('wrongpassword');
        cy.contains('button', 'Login').click();
        cy.wait(`@loginAttempt${i}`);
      }

      // Verify rate limiting message is shown
      cy.contains('Too many attempts').should('be.visible');
    });
  });

  describe('Password Security', () => {
    it('should mask password input', () => {
      cy.visit('/auth');
      
      const password = 'mysecretpassword';
      cy.get('input[placeholder="Enter your password"]')
        .type(password)
        .should('have.attr', 'type', 'password');

      // Test password visibility toggle using sibling approach
      cy.get('input[placeholder="Enter your password"]')
        .siblings('div')
        .find('button')
        .click();
        
      cy.get('input[placeholder="Enter your password"]')
        .should('have.attr', 'type', 'text')
        .should('have.value', password);

      cy.get('input[placeholder="Enter your password"]')
        .siblings('div')
        .find('button')
        .click();
        
      cy.get('input[placeholder="Enter your password"]')
        .should('have.attr', 'type', 'password');
    });

    it('should validate password strength on signup', () => {
      cy.visit('/auth');
      cy.contains('Sign up').click();

      // Test weak password
      cy.get('input[placeholder="Create a password"]').type('123');
      cy.contains('button', 'Sign Up').click();

      // Should show validation error or stay on same page
      cy.url().should('include', '/auth');
    });
  });
});
