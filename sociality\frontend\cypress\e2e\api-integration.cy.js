describe('API Integration Tests', () => {
  const baseApiUrl = Cypress.env('apiUrl');
  
  beforeEach(() => {
    cy.clearAppData();
    
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: 'api-test-user',
        name: 'API Test User',
        email: '<EMAIL>',
        username: 'apitestuser',
        bio: 'Testing API integration',
        profilePic: '',
        isProfileComplete: true
      }));
    });
  });

  describe('User API Integration', () => {
    it('should handle user profile operations', () => {
      // Test getting user profile
      cy.intercept('GET', `${baseApiUrl}/users/profile/apitestuser`, {
        statusCode: 200,
        body: {
          _id: 'api-test-user',
          name: 'API Test User',
          username: 'apitestuser',
          bio: 'Testing API integration',
          followers: [],
          following: [],
          posts: []
        }
      }).as('getUserProfile');

      cy.visit('/apitestuser');
      cy.wait('@getUserProfile');

      // Test updating user profile
      cy.intercept('PUT', `${baseApiUrl}/users/update/api-test-user`, {
        statusCode: 200,
        body: {
          _id: 'api-test-user',
          name: 'Updated API Test User',
          username: 'apitestuser',
          bio: 'Updated bio for testing',
          profilePic: ''
        }
      }).as('updateUserProfile');

      cy.get('[data-testid="edit-profile-button"]').click();
      cy.get('input[name="name"]').clear().type('Updated API Test User');
      cy.get('textarea[name="bio"]').clear().type('Updated bio for testing');
      cy.get('[data-testid="save-profile-button"]').click();

      cy.wait('@updateUserProfile');
      cy.contains('Updated API Test User').should('be.visible');
    });

    it('should handle follow/unfollow operations', () => {
      // Test following a user
      cy.intercept('POST', `${baseApiUrl}/users/follow/*`, {
        statusCode: 200,
        body: { message: 'User followed successfully' }
      }).as('followUser');

      cy.intercept('GET', `${baseApiUrl}/users/profile/john_doe`, {
        statusCode: 200,
        body: {
          _id: 'john-doe-id',
          name: 'John Doe',
          username: 'john_doe',
          bio: 'Software developer',
          followers: [],
          following: [],
          posts: []
        }
      }).as('getJohnProfile');

      cy.visit('/john_doe');
      cy.wait('@getJohnProfile');

      cy.get('[data-testid="follow-button"]').click();
      cy.wait('@followUser');

      // Test unfollowing
      cy.intercept('DELETE', `${baseApiUrl}/users/follow/*`, {
        statusCode: 200,
        body: { message: 'User unfollowed successfully' }
      }).as('unfollowUser');

      cy.get('[data-testid="unfollow-button"]').click();
      cy.wait('@unfollowUser');
    });

    it('should handle user search', () => {
      cy.intercept('GET', `${baseApiUrl}/users/search?q=john`, {
        statusCode: 200,
        body: [
          {
            _id: 'john-1',
            username: 'john_doe',
            name: 'John Doe',
            profilePic: ''
          },
          {
            _id: 'john-2',
            username: 'johnny',
            name: 'Johnny Smith',
            profilePic: ''
          }
        ]
      }).as('searchUsers');

      cy.visit('/search');
      cy.get('[data-testid="search-input"]').type('john');
      cy.wait('@searchUsers');

      cy.contains('John Doe').should('be.visible');
      cy.contains('Johnny Smith').should('be.visible');
    });
  });

  describe('Posts API Integration', () => {
    it('should handle post creation and management', () => {
      // Test creating a post
      cy.intercept('POST', `${baseApiUrl}/posts/create`, {
        statusCode: 201,
        body: {
          _id: 'new-post-id',
          text: 'API test post content',
          postedBy: {
            _id: 'api-test-user',
            username: 'apitestuser',
            name: 'API Test User'
          },
          likes: [],
          replies: [],
          createdAt: new Date().toISOString()
        }
      }).as('createPost');

      cy.intercept('GET', `${baseApiUrl}/posts/feed`, {
        statusCode: 200,
        body: []
      }).as('getFeed');

      cy.visit('/');
      cy.wait('@getFeed');

      cy.get('[data-testid="create-post-input"]').type('API test post content');
      cy.get('[data-testid="create-post-button"]').click();
      cy.wait('@createPost');

      // Test deleting a post
      cy.intercept('DELETE', `${baseApiUrl}/posts/new-post-id`, {
        statusCode: 200,
        body: { message: 'Post deleted successfully' }
      }).as('deletePost');

      cy.get('[data-testid="post-menu-button"]').click();
      cy.get('[data-testid="delete-post-button"]').click();
      cy.get('[data-testid="confirm-delete-button"]').click();
      cy.wait('@deletePost');
    });

    it('should handle post interactions', () => {
      cy.intercept('GET', `${baseApiUrl}/posts/feed`, { fixture: 'posts.json' }).as('getFeed');

      cy.visit('/');
      cy.wait('@getFeed');

      // Test liking a post
      cy.intercept('POST', `${baseApiUrl}/posts/like/1`, {
        statusCode: 200,
        body: { message: 'Post liked successfully' }
      }).as('likePost');

      cy.get('[data-testid="like-button"]').first().click();
      cy.wait('@likePost');

      // Test commenting on a post
      cy.intercept('POST', `${baseApiUrl}/posts/reply/1`, {
        statusCode: 201,
        body: {
          _id: 'comment-id',
          text: 'API test comment',
          userId: 'api-test-user',
          username: 'apitestuser',
          createdAt: new Date().toISOString()
        }
      }).as('commentPost');

      cy.get('[data-testid="comment-button"]').first().click();
      cy.get('[data-testid="comment-input"]').type('API test comment');
      cy.get('[data-testid="submit-comment"]').click();
      cy.wait('@commentPost');

      // Test reposting
      cy.intercept('POST', `${baseApiUrl}/posts/repost/1`, {
        statusCode: 200,
        body: { message: 'Post reposted successfully' }
      }).as('repostPost');

      cy.get('[data-testid="repost-button"]').first().click();
      cy.wait('@repostPost');
    });

    it('should handle trending posts', () => {
      cy.intercept('GET', `${baseApiUrl}/posts/trending`, {
        statusCode: 200,
        body: [
          {
            _id: 'trending-1',
            text: 'This is trending!',
            postedBy: { username: 'trenduser', name: 'Trend User' },
            likes: 100,
            replies: 25,
            createdAt: new Date().toISOString()
          }
        ]
      }).as('getTrendingPosts');

      cy.visit('/');
      cy.get('[data-testid="trending-tab"]').click();
      cy.wait('@getTrendingPosts');

      cy.contains('This is trending!').should('be.visible');
    });
  });

  describe('Messages API Integration', () => {
    it('should handle conversations and messaging', () => {
      // Test getting conversations
      cy.intercept('GET', `${baseApiUrl}/messages/conversations`, {
        statusCode: 200,
        body: [
          {
            _id: 'conversation-1',
            participants: [
              { _id: 'api-test-user', username: 'apitestuser' },
              { _id: 'other-user', username: 'otheruser', name: 'Other User' }
            ],
            lastMessage: {
              text: 'Hello there!',
              sender: 'other-user',
              createdAt: new Date().toISOString()
            }
          }
        ]
      }).as('getConversations');

      cy.visit('/chat');
      cy.wait('@getConversations');

      cy.contains('Other User').should('be.visible');
      cy.contains('Hello there!').should('be.visible');

      // Test getting messages in a conversation
      cy.intercept('GET', `${baseApiUrl}/messages/other-user`, {
        statusCode: 200,
        body: [
          {
            _id: 'message-1',
            text: 'Hello there!',
            sender: 'other-user',
            createdAt: new Date().toISOString()
          },
          {
            _id: 'message-2',
            text: 'Hi! How are you?',
            sender: 'api-test-user',
            createdAt: new Date().toISOString()
          }
        ]
      }).as('getMessages');

      cy.contains('Other User').click();
      cy.wait('@getMessages');

      cy.contains('Hello there!').should('be.visible');
      cy.contains('Hi! How are you?').should('be.visible');

      // Test sending a message
      cy.intercept('POST', `${baseApiUrl}/messages`, {
        statusCode: 201,
        body: {
          _id: 'new-message-id',
          text: 'New API test message',
          sender: 'api-test-user',
          createdAt: new Date().toISOString()
        }
      }).as('sendMessage');

      cy.get('[data-testid="message-input"]').type('New API test message');
      cy.get('[data-testid="send-message-button"]').click();
      cy.wait('@sendMessage');

      cy.contains('New API test message').should('be.visible');
    });
  });

  describe('Notifications API Integration', () => {
    it('should handle notifications', () => {
      cy.intercept('GET', `${baseApiUrl}/notifications`, {
        statusCode: 200,
        body: [
          {
            _id: 'notification-1',
            type: 'like',
            from: { username: 'liker', name: 'Liker User' },
            post: { _id: 'post-1', text: 'My post' },
            read: false,
            createdAt: new Date().toISOString()
          },
          {
            _id: 'notification-2',
            type: 'follow',
            from: { username: 'follower', name: 'Follower User' },
            read: true,
            createdAt: new Date().toISOString()
          }
        ]
      }).as('getNotifications');

      cy.visit('/notifications');
      cy.wait('@getNotifications');

      cy.contains('Liker User liked your post').should('be.visible');
      cy.contains('Follower User started following you').should('be.visible');

      // Test marking notification as read
      cy.intercept('PUT', `${baseApiUrl}/notifications/read/notification-1`, {
        statusCode: 200,
        body: { message: 'Notification marked as read' }
      }).as('markAsRead');

      cy.get('[data-testid="notification-item"]').first().click();
      cy.wait('@markAsRead');

      // Test deleting notification
      cy.intercept('DELETE', `${baseApiUrl}/notifications/notification-1`, {
        statusCode: 200,
        body: { message: 'Notification deleted' }
      }).as('deleteNotification');

      cy.get('[data-testid="delete-notification-button"]').first().click();
      cy.wait('@deleteNotification');
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', () => {
      // Test 404 error
      cy.intercept('GET', `${baseApiUrl}/posts/feed`, {
        statusCode: 404,
        body: { error: 'Feed not found' }
      }).as('feedNotFound');

      cy.visit('/');
      cy.wait('@feedNotFound');
      cy.contains('Feed not found').should('be.visible');

      // Test 500 error
      cy.intercept('POST', `${baseApiUrl}/posts/create`, {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('serverError');

      cy.get('[data-testid="create-post-input"]').type('Test post');
      cy.get('[data-testid="create-post-button"]').click();
      cy.wait('@serverError');
      cy.contains('Internal server error').should('be.visible');

      // Test network error
      cy.intercept('GET', `${baseApiUrl}/users/suggested`, { forceNetworkError: true }).as('networkError');
      
      cy.reload();
      cy.wait('@networkError');
      cy.contains('Network error').should('be.visible');
    });

    it('should handle authentication errors', () => {
      // Test 401 unauthorized
      cy.intercept('GET', `${baseApiUrl}/posts/feed`, {
        statusCode: 401,
        body: { error: 'Unauthorized' }
      }).as('unauthorized');

      cy.visit('/');
      cy.wait('@unauthorized');
      
      // Should redirect to auth page
      cy.url().should('include', '/auth');
    });

    it('should handle rate limiting', () => {
      // Test 429 rate limit
      cy.intercept('POST', `${baseApiUrl}/posts/create`, {
        statusCode: 429,
        body: { error: 'Rate limit exceeded. Please try again later.' }
      }).as('rateLimited');

      cy.visit('/');
      cy.get('[data-testid="create-post-input"]').type('Rate limit test');
      cy.get('[data-testid="create-post-button"]').click();
      cy.wait('@rateLimited');
      
      cy.contains('Rate limit exceeded').should('be.visible');
    });
  });
});
