/* Pokemon Empty State Animations */

.pokemon-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 200px;
}

/* Psyduck main animation - floating and swaying */
.pokemon-psyduck {
  animation: pokemonFloat 3s ease-in-out infinite;
  transform-origin: center bottom;
}

.psyduck-svg {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* Individual body part animations */
.psyduck-head {
  animation: headSway 2s ease-in-out infinite;
  transform-origin: center;
}

.psyduck-body {
  animation: bodyBreathe 2.5s ease-in-out infinite;
}

.psyduck-wing-left {
  animation: wingFlutter 1.5s ease-in-out infinite;
  transform-origin: center;
}

.psyduck-wing-right {
  animation: wingFlutter 1.5s ease-in-out infinite 0.3s;
  transform-origin: center;
}

.psyduck-eyes {
  animation: eyesTwitch 4s ease-in-out infinite;
}

.psyduck-crest {
  animation: crestWiggle 2.2s ease-in-out infinite;
  transform-origin: center bottom;
}

/* Floating stars animation */
.floating-stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.star {
  position: absolute;
  font-size: 16px;
  animation: starFloat 4s ease-in-out infinite;
  opacity: 0.7;
}

.star-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.star-2 {
  top: 30%;
  right: 15%;
  animation-delay: 1s;
  font-size: 14px;
}

.star-3 {
  top: 60%;
  left: 5%;
  animation-delay: 2s;
  font-size: 18px;
}

.star-4 {
  top: 70%;
  right: 10%;
  animation-delay: 3s;
  font-size: 12px;
}

.star-5 {
  top: 10%;
  right: 30%;
  animation-delay: 1.5s;
  font-size: 15px;
}

/* Swirl effect for dizziness */
.swirl-effect {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
}

.swirl {
  position: absolute;
  border: 2px solid rgba(255, 215, 0, 0.6);
  border-radius: 50%;
  border-top-color: transparent;
  border-right-color: transparent;
  animation: swirlRotate 2s linear infinite;
}

.swirl-1 {
  width: 20px;
  height: 20px;
  top: 20px;
  left: 20px;
}

.swirl-2 {
  width: 30px;
  height: 30px;
  top: 15px;
  left: 15px;
  animation-delay: 0.3s;
}

.swirl-3 {
  width: 40px;
  height: 40px;
  top: 10px;
  left: 10px;
  animation-delay: 0.6s;
}

/* Message container animation */
.message-container {
  animation: messageGlow 3s ease-in-out infinite;
}

.empty-message {
  animation: textPulse 2s ease-in-out infinite;
}

/* Keyframe animations */
@keyframes pokemonFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-8px) rotate(-1deg);
  }
  50% {
    transform: translateY(-5px) rotate(0deg);
  }
  75% {
    transform: translateY(-10px) rotate(1deg);
  }
}

@keyframes headSway {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-2deg);
  }
  75% {
    transform: rotate(2deg);
  }
}

@keyframes bodyBreathe {
  0%, 100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(1.05);
  }
}

@keyframes wingFlutter {
  0%, 100% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(-10deg);
  }
}

@keyframes eyesTwitch {
  0%, 90%, 100% {
    opacity: 1;
  }
  95% {
    opacity: 0.3;
  }
}

@keyframes crestWiggle {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-3deg);
  }
  75% {
    transform: rotate(3deg);
  }
}

@keyframes starFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-15px) rotate(90deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
    opacity: 0.9;
  }
}

@keyframes swirlRotate {
  0% {
    transform: rotate(0deg);
    opacity: 0.8;
  }
  100% {
    transform: rotate(360deg);
    opacity: 0.3;
  }
}

@keyframes messageGlow {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 20px rgba(0, 204, 133, 0.1);
  }
}

@keyframes textPulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pokemon-container {
    width: 150px;
    height: 150px;
  }
  
  .psyduck-svg {
    width: 90px;
    height: 90px;
  }
  
  .star {
    font-size: 12px;
  }
  
  .swirl-effect {
    width: 45px;
    height: 45px;
  }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .pokemon-psyduck,
  .psyduck-head,
  .psyduck-body,
  .psyduck-wing-left,
  .psyduck-wing-right,
  .psyduck-eyes,
  .psyduck-crest,
  .star,
  .swirl,
  .message-container,
  .empty-message {
    animation: none;
  }
  
  .pokemon-psyduck {
    transform: translateY(-5px);
  }
}
