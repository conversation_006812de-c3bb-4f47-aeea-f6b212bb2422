describe('Accessibility Tests', () => {
  beforeEach(() => {
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    // Mock API calls
    cy.intercept('GET', '/api/posts/feed', { fixture: 'posts.json' }).as('getFeed');
    cy.intercept('GET', '/api/users/suggested', { fixture: 'users.json' }).as('getSuggestedUsers');
  });

  describe('Keyboard Navigation', () => {
    it('should navigate through main elements with Tab key', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Test tab navigation through header elements
      cy.get('body').tab();
      cy.focused().should('contain', 'Home'); // Assuming first nav item

      cy.focused().tab();
      cy.focused().should('contain', 'Search');

      cy.focused().tab();
      cy.focused().should('contain', 'Notifications');

      cy.focused().tab();
      cy.focused().should('contain', 'Chat');
    });

    it('should navigate posts with arrow keys', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Focus on first post
      cy.get('[data-testid="post-item"]').first().focus();

      // Navigate down to next post
      cy.focused().type('{downarrow}');
      cy.get('[data-testid="post-item"]').eq(1).should('be.focused');

      // Navigate back up
      cy.focused().type('{uparrow}');
      cy.get('[data-testid="post-item"]').first().should('be.focused');
    });

    it('should activate buttons with Enter and Space keys', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Mock like post API
      cy.intercept('PUT', '/api/posts/like/*', {
        statusCode: 200,
        body: { message: 'Post liked successfully' }
      }).as('likePost');

      // Focus on like button and activate with Enter
      cy.get('[data-testid="like-button"]').first().focus();
      cy.focused().type('{enter}');
      cy.wait('@likePost');

      // Focus on another like button and activate with Space
      cy.get('[data-testid="like-button"]').eq(1).focus();
      cy.focused().type(' ');
    });

    it('should escape from modals with Escape key', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Open a modal (e.g., user profile)
      cy.get('[data-testid="user-menu"]').click();
      cy.get('[data-testid="modal"]').should('be.visible');

      // Press Escape to close
      cy.get('body').type('{esc}');
      cy.get('[data-testid="modal"]').should('not.exist');
    });

    it('should trap focus within modals', () => {
      cy.visit('/settings');

      // Open delete account modal
      cy.contains('Delete Account').click();

      // Tab should cycle within modal
      cy.get('[data-testid="modal"] input').first().focus();
      cy.focused().tab();
      cy.focused().should('be.within', '[data-testid="modal"]');

      // Shift+Tab should also stay within modal
      cy.focused().tab({ shift: true });
      cy.focused().should('be.within', '[data-testid="modal"]');
    });
  });

  describe('Screen Reader Support', () => {
    it('should have proper heading hierarchy', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Check for proper h1, h2, h3 structure
      cy.get('h1').should('exist');
      cy.get('h1').should('contain', 'Home'); // Main page heading

      cy.get('h2').should('exist'); // Section headings
      cy.get('h3').should('exist'); // Subsection headings
    });

    it('should have descriptive alt text for images', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Check profile pictures have alt text
      cy.get('img[src*="profilePic"]').each(($img) => {
        cy.wrap($img).should('have.attr', 'alt');
        cy.wrap($img).invoke('attr', 'alt').should('not.be.empty');
      });

      // Check post images have alt text
      cy.get('img[src*="post"]').each(($img) => {
        cy.wrap($img).should('have.attr', 'alt');
        cy.wrap($img).invoke('attr', 'alt').should('not.be.empty');
      });
    });

    it('should have ARIA labels for interactive elements', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Check like buttons have aria-label
      cy.get('[data-testid="like-button"]').each(($btn) => {
        cy.wrap($btn).should('have.attr', 'aria-label');
      });

      // Check navigation buttons have aria-label
      cy.get('[data-testid="nav-button"]').each(($btn) => {
        cy.wrap($btn).should('have.attr', 'aria-label');
      });

      // Check menu buttons have aria-label
      cy.get('[data-testid="menu-button"]').each(($btn) => {
        cy.wrap($btn).should('have.attr', 'aria-label');
      });
    });

    it('should have proper form labels', () => {
      cy.visit('/auth');

      // Check login form labels
      cy.get('input[placeholder="Enter your username"]').should('have.attr', 'aria-label');
      cy.get('input[placeholder="Enter your password"]').should('have.attr', 'aria-label');

      // Switch to signup form
      cy.contains('Sign up').click();

      // Check signup form labels
      cy.get('input[placeholder="Enter your full name"]').should('have.attr', 'aria-label');
      cy.get('input[placeholder="Choose a username"]').should('have.attr', 'aria-label');
      cy.get('input[placeholder="Enter your email"]').should('have.attr', 'aria-label');
    });

    it('should announce dynamic content changes', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Check for aria-live regions
      cy.get('[aria-live="polite"]').should('exist');
      cy.get('[aria-live="assertive"]').should('exist');

      // Mock like action and check announcement
      cy.intercept('PUT', '/api/posts/like/*', {
        statusCode: 200,
        body: { message: 'Post liked successfully' }
      }).as('likePost');

      cy.get('[data-testid="like-button"]').first().click();
      cy.wait('@likePost');

      // Should announce the like action
      cy.get('[aria-live="polite"]').should('contain', 'Post liked');
    });

    it('should have proper ARIA roles', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Check navigation has proper role
      cy.get('[role="navigation"]').should('exist');

      // Check main content has proper role
      cy.get('[role="main"]').should('exist');

      // Check buttons have proper role
      cy.get('button').each(($btn) => {
        cy.wrap($btn).should('have.attr', 'role', 'button');
      });

      // Check links have proper role
      cy.get('a').each(($link) => {
        cy.wrap($link).should('have.attr', 'role', 'link');
      });
    });
  });

  describe('Color Contrast and Visual Accessibility', () => {
    it('should maintain contrast in dark mode', () => {
      cy.visit('/');
      
      // Switch to dark mode
      cy.get('[data-testid="theme-toggle"]').click();

      // Check text contrast (this would need a contrast checking library)
      cy.get('body').should('have.class', 'dark-mode');
      
      // Visual regression test for dark mode
      cy.percySnapshot('Dark Mode - Home Page');
    });

    it('should maintain contrast in light mode', () => {
      cy.visit('/');
      
      // Ensure light mode
      cy.get('body').should('have.class', 'light-mode');
      
      // Visual regression test for light mode
      cy.percySnapshot('Light Mode - Home Page');
    });

    it('should not rely solely on color for information', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Check that interactive states have more than just color changes
      cy.get('[data-testid="like-button"]').first().as('likeBtn');

      // Button should have text or icon, not just color
      cy.get('@likeBtn').should('contain.text', 'Like').or('contain', 'svg');

      // When liked, should have different text/icon, not just color
      cy.get('@likeBtn').click();
      cy.get('@likeBtn').should('contain.text', 'Liked').or('contain', 'svg[data-liked="true"]');
    });

    it('should support high contrast mode', () => {
      // Simulate high contrast mode
      cy.visit('/', {
        onBeforeLoad(win) {
          win.matchMedia = cy.stub().returns({
            matches: true,
            addListener: cy.stub(),
            removeListener: cy.stub()
          });
        }
      });

      // Should apply high contrast styles
      cy.get('body').should('have.class', 'high-contrast');
    });
  });

  describe('Focus Management', () => {
    it('should have visible focus indicators', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Tab to first focusable element
      cy.get('body').tab();

      // Should have visible focus ring
      cy.focused().should('have.css', 'outline').and('not.eq', 'none');
    });

    it('should manage focus when navigating between pages', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Navigate to profile page
      cy.get('[data-testid="profile-link"]').click();

      // Focus should be on main heading or skip link
      cy.focused().should('contain', 'Profile').or('contain', 'Skip to main content');
    });

    it('should restore focus after modal closes', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Focus on button that opens modal
      cy.get('[data-testid="open-modal-btn"]').focus().click();

      // Modal should be open
      cy.get('[data-testid="modal"]').should('be.visible');

      // Close modal
      cy.get('[data-testid="close-modal"]').click();

      // Focus should return to the button that opened modal
      cy.get('[data-testid="open-modal-btn"]').should('be.focused');
    });

    it('should skip to main content', () => {
      cy.visit('/');

      // Tab to skip link (usually first focusable element)
      cy.get('body').tab();

      // Should be skip link
      cy.focused().should('contain', 'Skip to main content');

      // Activate skip link
      cy.focused().type('{enter}');

      // Should focus on main content
      cy.get('[role="main"]').should('be.focused');
    });
  });

  describe('Form Accessibility', () => {
    it('should associate labels with form controls', () => {
      cy.visit('/auth');

      // Each input should have associated label
      cy.get('input').each(($input) => {
        const id = $input.attr('id');
        const ariaLabelledBy = $input.attr('aria-labelledby');
        const ariaLabel = $input.attr('aria-label');

        // Should have at least one way to be labeled
        expect(id || ariaLabelledBy || ariaLabel).to.exist;

        if (id) {
          cy.get(`label[for="${id}"]`).should('exist');
        }
      });
    });

    it('should show validation errors accessibly', () => {
      cy.visit('/auth');

      // Submit form with invalid data
      cy.contains('button', 'Login').click();

      // Error messages should be associated with fields
      cy.get('input[aria-invalid="true"]').should('exist');
      cy.get('[role="alert"]').should('exist');

      // Error message should be announced to screen readers
      cy.get('[aria-live="assertive"]').should('contain', 'required');
    });

    it('should provide helpful instructions', () => {
      cy.visit('/auth');
      cy.contains('Sign up').click();

      // Password field should have instructions
      cy.get('input[type="password"]').should('have.attr', 'aria-describedby');

      const describedBy = cy.get('input[type="password"]').invoke('attr', 'aria-describedby');
      cy.get(`#${describedBy}`).should('contain', 'at least 6 characters');
    });

    it('should group related form controls', () => {
      cy.visit('/settings');

      // Notification settings should be in fieldset
      cy.contains('Notifications').click();
      cy.get('fieldset').should('contain', 'Notification Preferences');
      cy.get('legend').should('contain', 'Choose which notifications you want to receive');
    });
  });

  describe('Mobile Accessibility', () => {
    it('should be accessible on mobile devices', () => {
      cy.viewport(375, 667);
      cy.visit('/');
      cy.wait('@getFeed');

      // Touch targets should be at least 44px
      cy.get('button').each(($btn) => {
        cy.wrap($btn).then(($el) => {
          const rect = $el[0].getBoundingClientRect();
          expect(Math.max(rect.width, rect.height)).to.be.at.least(44);
        });
      });
    });

    it('should support swipe gestures with keyboard alternatives', () => {
      cy.viewport(375, 667);
      cy.visit('/chat');

      // Swipe gestures should have keyboard equivalents
      cy.get('[data-testid="swipe-action"]').should('have.attr', 'tabindex', '0');
      cy.get('[data-testid="swipe-action"]').should('have.attr', 'role', 'button');
    });

    it('should zoom properly on mobile', () => {
      cy.viewport(375, 667);
      cy.visit('/');

      // Viewport should allow zooming
      cy.get('meta[name="viewport"]').should('have.attr', 'content')
        .and('not.contain', 'user-scalable=no')
        .and('not.contain', 'maximum-scale=1');
    });
  });

  describe('Error Handling Accessibility', () => {
    it('should announce errors to screen readers', () => {
      cy.intercept('GET', '/api/posts/feed', {
        statusCode: 500,
        body: { error: 'Server error' }
      }).as('getFeedError');

      cy.visit('/');
      cy.wait('@getFeedError');

      // Error should be announced
      cy.get('[role="alert"]').should('contain', 'Server error');
      cy.get('[aria-live="assertive"]').should('contain', 'error');
    });

    it('should provide clear error recovery options', () => {
      cy.intercept('GET', '/api/posts/feed', { forceNetworkError: true }).as('networkError');

      cy.visit('/');
      cy.wait('@networkError');

      // Should provide retry button with clear label
      cy.get('button').should('contain', 'Try again').and('have.attr', 'aria-label');
      cy.get('[role="alert"]').should('contain', 'connection');
    });
  });

  describe('Progressive Enhancement', () => {
    it('should work without JavaScript', () => {
      cy.visit('/', {
        onBeforeLoad(win) {
          // Disable JavaScript
          win.eval = undefined;
        }
      });

      // Basic content should still be accessible
      cy.contains('Login').should('be.visible');
      cy.get('form').should('exist');
    });

    it('should work with reduced motion preferences', () => {
      cy.visit('/', {
        onBeforeLoad(win) {
          // Mock reduced motion preference
          Object.defineProperty(win, 'matchMedia', {
            writable: true,
            value: cy.stub().returns({
              matches: true,
              addListener: cy.stub(),
              removeListener: cy.stub()
            })
          });
        }
      });

      // Animations should be reduced or disabled
      cy.get('body').should('have.class', 'reduced-motion');
    });
  });

  describe('Language and Internationalization', () => {
    it('should have proper language attributes', () => {
      cy.visit('/');

      // HTML should have lang attribute
      cy.get('html').should('have.attr', 'lang', 'en');
    });

    it('should handle right-to-left languages', () => {
      // Mock RTL language preference
      cy.visit('/', {
        onBeforeLoad(win) {
          win.document.documentElement.dir = 'rtl';
          win.document.documentElement.lang = 'ar';
        }
      });

      // Layout should adapt to RTL
      cy.get('body').should('have.css', 'direction', 'rtl');
    });

    it('should provide text alternatives for icon fonts', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Icon fonts should have text alternatives
      cy.get('[class*="icon"]').each(($icon) => {
        cy.wrap($icon).should('have.attr', 'aria-label')
          .or('have.attr', 'aria-hidden', 'true');
      });
    });
  });
});
