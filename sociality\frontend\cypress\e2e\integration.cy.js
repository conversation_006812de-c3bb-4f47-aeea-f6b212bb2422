describe('Cross-Platform Integration', () => {
  beforeEach(() => {
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    // Mock platform status
    cy.intercept('GET', '/api/platforms/status', {
      statusCode: 200,
      body: {
        telegram: true,
        discord: true,
        federation: true
      }
    }).as('getPlatformStatus');

    cy.visit('/chat');
    cy.wait('@getPlatformStatus');
  });

  describe('Platform Status', () => {
    it('should display platform connection status', () => {
      cy.get('[data-testid="cross-platform-toggle"]').click();
      
      cy.contains('Telegram').should('be.visible');
      cy.contains('Discord').should('be.visible');
      cy.contains('Federation').should('be.visible');
      
      cy.get('[data-testid="telegram-status"]').should('contain', 'Connected');
      cy.get('[data-testid="discord-status"]').should('contain', 'Connected');
      cy.get('[data-testid="federation-status"]').should('contain', 'Connected');
    });

    it('should handle disconnected platforms', () => {
      cy.intercept('GET', '/api/platforms/status', {
        statusCode: 200,
        body: {
          telegram: false,
          discord: false,
          federation: true
        }
      }).as('getDisconnectedStatus');

      cy.reload();
      cy.wait('@getDisconnectedStatus');
      
      cy.get('[data-testid="cross-platform-toggle"]').click();
      
      cy.get('[data-testid="telegram-status"]').should('contain', 'Disconnected');
      cy.get('[data-testid="discord-status"]').should('contain', 'Disconnected');
      cy.get('[data-testid="federation-status"]').should('contain', 'Connected');
    });

    it('should show platform setup instructions', () => {
      cy.intercept('GET', '/api/platforms/status', {
        statusCode: 200,
        body: {
          telegram: false,
          discord: false,
          federation: false
        }
      }).as('getNoConnectionStatus');

      cy.reload();
      cy.wait('@getNoConnectionStatus');
      
      cy.get('[data-testid="cross-platform-toggle"]').click();
      
      cy.contains('Connect your platforms').should('be.visible');
      cy.contains('Setup Telegram').should('be.visible');
      cy.contains('Setup Discord').should('be.visible');
      cy.contains('Join Federation').should('be.visible');
    });
  });

  describe('Federated Rooms', () => {
    beforeEach(() => {
      cy.get('[data-testid="cross-platform-toggle"]').click();
      
      // Mock federated rooms
      cy.intercept('GET', '/api/federation/rooms', {
        statusCode: 200,
        body: [
          {
            _id: 'room1',
            name: 'General Chat',
            description: 'Main discussion room',
            participants: 15,
            isPublic: true,
            isJoined: true,
            lastActivity: new Date().toISOString()
          },
          {
            _id: 'room2',
            name: 'Tech Talk',
            description: 'Technology discussions',
            participants: 8,
            isPublic: true,
            isJoined: false,
            lastActivity: new Date(Date.now() - 3600000).toISOString()
          },
          {
            _id: 'room3',
            name: 'Private Room',
            description: 'Invite only',
            participants: 3,
            isPublic: false,
            isJoined: true,
            lastActivity: new Date(Date.now() - 1800000).toISOString()
          }
        ]
      }).as('getFederatedRooms');

      cy.wait('@getFederatedRooms');
    });

    it('should display federated rooms list', () => {
      cy.contains('General Chat').should('be.visible');
      cy.contains('15 participants').should('be.visible');
      cy.contains('Tech Talk').should('be.visible');
      cy.contains('8 participants').should('be.visible');
      cy.contains('Private Room').should('be.visible');
      cy.contains('3 participants').should('be.visible');
    });

    it('should show room status indicators', () => {
      // Joined rooms should have different indicator
      cy.get('[data-testid="room-item-room1"]').should('have.class', 'joined');
      cy.get('[data-testid="room-item-room3"]').should('have.class', 'joined');
      
      // Not joined rooms should show join option
      cy.get('[data-testid="room-item-room2"]').should('not.have.class', 'joined');
      cy.get('[data-testid="room-item-room2"]').contains('Join').should('be.visible');
    });

    it('should filter rooms by status', () => {
      cy.get('[data-testid="filter-joined"]').click();
      
      cy.contains('General Chat').should('be.visible');
      cy.contains('Private Room').should('be.visible');
      cy.contains('Tech Talk').should('not.exist');
      
      cy.get('[data-testid="filter-all"]').click();
      cy.contains('Tech Talk').should('be.visible');
    });

    it('should create new federated room', () => {
      cy.intercept('POST', '/api/federation/rooms', {
        statusCode: 200,
        body: {
          _id: 'new-room',
          name: 'Test Room',
          description: 'A test room',
          participants: 1,
          isPublic: true,
          isJoined: true
        }
      }).as('createRoom');

      cy.get('[data-testid="create-room-button"]').click();
      
      cy.get('[data-testid="room-name-input"]').type('Test Room');
      cy.get('[data-testid="room-description-input"]').type('A test room');
      cy.get('[data-testid="room-public-toggle"]').check();
      
      cy.get('[data-testid="create-room-submit"]').click();
      
      cy.wait('@createRoom');
      cy.contains('Test Room').should('be.visible');
      cy.contains('Room created successfully').should('be.visible');
    });

    it('should join existing room', () => {
      cy.intercept('POST', '/api/federation/rooms/room2/join', {
        statusCode: 200,
        body: { message: 'Joined room successfully' }
      }).as('joinRoom');

      cy.get('[data-testid="room-item-room2"]').contains('Join').click();
      
      cy.wait('@joinRoom');
      cy.contains('Joined room successfully').should('be.visible');
      cy.get('[data-testid="room-item-room2"]').should('have.class', 'joined');
    });

    it('should leave room', () => {
      cy.intercept('POST', '/api/federation/rooms/room1/leave', {
        statusCode: 200,
        body: { message: 'Left room successfully' }
      }).as('leaveRoom');

      cy.get('[data-testid="room-item-room1"]').find('[data-testid="room-menu"]').click();
      cy.contains('Leave Room').click();
      cy.contains('Confirm Leave').click();
      
      cy.wait('@leaveRoom');
      cy.contains('Left room successfully').should('be.visible');
      cy.get('[data-testid="room-item-room1"]').should('not.have.class', 'joined');
    });

    it('should join room by ID', () => {
      cy.intercept('GET', '/api/federation/rooms/room456/info', {
        statusCode: 200,
        body: {
          _id: 'room456',
          name: 'External Room',
          description: 'Room from another server',
          participants: 12,
          isPublic: true
        }
      }).as('getRoomInfo');

      cy.intercept('POST', '/api/federation/rooms/room456/join', {
        statusCode: 200,
        body: { message: 'Joined room successfully' }
      }).as('joinExternalRoom');

      cy.get('[data-testid="join-room-button"]').click();
      cy.get('[data-testid="room-id-input"]').type('room456');
      cy.get('[data-testid="join-room-submit"]').click();
      
      cy.wait('@getRoomInfo');
      cy.contains('External Room').should('be.visible');
      cy.contains('12 participants').should('be.visible');
      
      cy.contains('Join Room').click();
      cy.wait('@joinExternalRoom');
      
      cy.contains('Joined room successfully').should('be.visible');
    });
  });

  describe('Cross-Platform Messaging', () => {
    beforeEach(() => {
      cy.get('[data-testid="cross-platform-toggle"]').click();
      
      // Mock room messages
      cy.intercept('GET', '/api/federation/rooms/room1/messages', {
        statusCode: 200,
        body: [
          {
            _id: 'msg1',
            text: 'Hello from web!',
            sender: {
              _id: '1',
              username: 'testuser',
              name: 'Test User',
              platform: 'web'
            },
            roomId: 'room1',
            createdAt: new Date(Date.now() - 1800000).toISOString()
          },
          {
            _id: 'msg2',
            text: 'Hi from Telegram!',
            sender: {
              _id: 'tg_123',
              username: 'telegram_user',
              name: 'Telegram User',
              platform: 'telegram'
            },
            roomId: 'room1',
            createdAt: new Date(Date.now() - 1200000).toISOString()
          },
          {
            _id: 'msg3',
            text: 'Discord user here!',
            sender: {
              _id: 'dc_456',
              username: 'discord_user',
              name: 'Discord User',
              platform: 'discord'
            },
            roomId: 'room1',
            createdAt: new Date(Date.now() - 600000).toISOString()
          }
        ]
      }).as('getRoomMessages');

      // Click on a room to open it
      cy.contains('General Chat').click();
      cy.wait('@getRoomMessages');
    });

    it('should display messages from different platforms', () => {
      cy.contains('Hello from web!').should('be.visible');
      cy.contains('Hi from Telegram!').should('be.visible');
      cy.contains('Discord user here!').should('be.visible');
      
      // Should show platform indicators
      cy.get('[data-testid="platform-indicator-web"]').should('be.visible');
      cy.get('[data-testid="platform-indicator-telegram"]').should('be.visible');
      cy.get('[data-testid="platform-indicator-discord"]').should('be.visible');
    });

    it('should send message to federated room', () => {
      const messageText = 'Test message from web client';
      
      cy.intercept('POST', '/api/federation/rooms/room1/messages', {
        statusCode: 200,
        body: {
          _id: 'new-msg',
          text: messageText,
          sender: {
            _id: '1',
            username: 'testuser',
            name: 'Test User',
            platform: 'web'
          },
          roomId: 'room1',
          createdAt: new Date().toISOString()
        }
      }).as('sendRoomMessage');

      cy.get('[data-testid="room-message-input"]').type(messageText);
      cy.get('[data-testid="send-room-message"]').click();
      
      cy.wait('@sendRoomMessage');
      cy.contains(messageText).should('be.visible');
    });

    it('should show message delivery status', () => {
      const messageText = 'Message with delivery status';
      
      cy.intercept('POST', '/api/federation/rooms/room1/messages', {
        statusCode: 200,
        body: {
          _id: 'status-msg',
          text: messageText,
          sender: {
            _id: '1',
            username: 'testuser',
            name: 'Test User',
            platform: 'web'
          },
          roomId: 'room1',
          deliveryStatus: {
            telegram: 'sent',
            discord: 'delivered',
            web: 'delivered'
          },
          createdAt: new Date().toISOString()
        }
      }).as('sendMessageWithStatus');

      cy.get('[data-testid="room-message-input"]').type(messageText);
      cy.get('[data-testid="send-room-message"]').click();
      
      cy.wait('@sendMessageWithStatus');
      
      // Should show delivery status indicators
      cy.get('[data-testid="delivery-status-telegram"]').should('contain', 'sent');
      cy.get('[data-testid="delivery-status-discord"]').should('contain', 'delivered');
      cy.get('[data-testid="delivery-status-web"]').should('contain', 'delivered');
    });

    it('should handle message translation', () => {
      cy.intercept('POST', '/api/federation/translate', {
        statusCode: 200,
        body: {
          translatedText: 'Hello from Telegram! (translated from Russian)',
          originalLanguage: 'ru',
          targetLanguage: 'en'
        }
      }).as('translateMessage');

      // Right-click on foreign language message (mock)
      cy.get('[data-testid="message-item"]').contains('Привет из Telegram!').rightclick();
      cy.contains('Translate').click();
      
      cy.wait('@translateMessage');
      cy.contains('Hello from Telegram! (translated from Russian)').should('be.visible');
    });
  });

  describe('Platform Binding', () => {
    it('should bind Telegram account', () => {
      cy.intercept('GET', '/api/platforms/telegram/auth-url', {
        statusCode: 200,
        body: {
          authUrl: 'https://t.me/bot?start=auth_123456',
          authCode: 'auth_123456'
        }
      }).as('getTelegramAuth');

      cy.intercept('POST', '/api/platforms/telegram/bind', {
        statusCode: 200,
        body: {
          message: 'Telegram account bound successfully',
          telegramUser: {
            id: 'tg_789',
            username: 'test_telegram',
            firstName: 'Test',
            lastName: 'User'
          }
        }
      }).as('bindTelegram');

      cy.visit('/settings');
      cy.contains('Platform Integrations').click();
      cy.contains('Connect Telegram').click();
      
      cy.wait('@getTelegramAuth');
      
      // Should show auth instructions
      cy.contains('Open Telegram and send').should('be.visible');
      cy.contains('/start auth_123456').should('be.visible');
      
      // Mock successful binding
      cy.window().then((win) => {
        win.postMessage({
          type: 'TELEGRAM_AUTH_SUCCESS',
          data: { userId: 'tg_789' }
        }, '*');
      });
      
      cy.wait('@bindTelegram');
      cy.contains('Telegram account bound successfully').should('be.visible');
    });

    it('should bind Discord account', () => {
      cy.intercept('GET', '/api/platforms/discord/auth-url', {
        statusCode: 200,
        body: {
          authUrl: 'https://discord.com/oauth2/authorize?client_id=123&redirect_uri=callback'
        }
      }).as('getDiscordAuth');

      cy.visit('/settings');
      cy.contains('Platform Integrations').click();
      cy.contains('Connect Discord').click();
      
      cy.wait('@getDiscordAuth');
      
      // Should open Discord OAuth in new window
      cy.window().then((win) => {
        cy.stub(win, 'open').as('windowOpen');
      });
      
      cy.get('@windowOpen').should('have.been.calledWith', 
        'https://discord.com/oauth2/authorize?client_id=123&redirect_uri=callback');
    });

    it('should unbind platform accounts', () => {
      cy.intercept('DELETE', '/api/platforms/telegram/unbind', {
        statusCode: 200,
        body: { message: 'Telegram account unbound successfully' }
      }).as('unbindTelegram');

      cy.visit('/settings');
      cy.contains('Platform Integrations').click();
      
      // Should show connected Telegram account
      cy.contains('Connected as @test_telegram').should('be.visible');
      
      cy.contains('Disconnect Telegram').click();
      cy.contains('Confirm Disconnect').click();
      
      cy.wait('@unbindTelegram');
      cy.contains('Telegram account unbound successfully').should('be.visible');
      cy.contains('Connect Telegram').should('be.visible');
    });
  });

  describe('Federation Server Discovery', () => {
    it('should discover federation servers', () => {
      cy.intercept('GET', '/api/federation/discover', {
        statusCode: 200,
        body: [
          {
            id: 'server1.social',
            name: 'Social Hub',
            description: 'Main social network',
            users: 1500,
            rooms: 25,
            status: 'online'
          },
          {
            id: 'tech.server',
            name: 'Tech Community',
            description: 'Technology focused server',
            users: 800,
            rooms: 15,
            status: 'online'
          }
        ]
      }).as('discoverServers');

      cy.get('[data-testid="cross-platform-toggle"]').click();
      cy.contains('Discover Servers').click();
      
      cy.wait('@discoverServers');
      
      cy.contains('Social Hub').should('be.visible');
      cy.contains('1,500 users').should('be.visible');
      cy.contains('25 rooms').should('be.visible');
      
      cy.contains('Tech Community').should('be.visible');
      cy.contains('800 users').should('be.visible');
      cy.contains('15 rooms').should('be.visible');
    });

    it('should connect to federation server', () => {
      cy.intercept('POST', '/api/federation/connect', {
        statusCode: 200,
        body: { message: 'Connected to server successfully' }
      }).as('connectToServer');

      cy.get('[data-testid="cross-platform-toggle"]').click();
      cy.contains('Connect to Server').click();
      
      cy.get('[data-testid="server-address-input"]').type('social.example.com');
      cy.get('[data-testid="connect-server-submit"]').click();
      
      cy.wait('@connectToServer');
      cy.contains('Connected to server successfully').should('be.visible');
    });
  });

  describe('Cross-Platform Notifications', () => {
    it('should show notifications from all platforms', () => {
      cy.intercept('GET', '/api/notifications/cross-platform', {
        statusCode: 200,
        body: [
          {
            _id: 'notif1',
            type: 'message',
            platform: 'telegram',
            from: {
              username: 'telegram_user',
              name: 'Telegram User'
            },
            content: 'New message in General Chat',
            createdAt: new Date().toISOString()
          },
          {
            _id: 'notif2',
            type: 'mention',
            platform: 'discord',
            from: {
              username: 'discord_user',
              name: 'Discord User'
            },
            content: 'You were mentioned in Tech Talk',
            createdAt: new Date(Date.now() - 1800000).toISOString()
          }
        ]
      }).as('getCrossPlatformNotifications');

      cy.visit('/notifications');
      cy.wait('@getCrossPlatformNotifications');
      
      cy.contains('New message in General Chat').should('be.visible');
      cy.get('[data-testid="platform-badge-telegram"]').should('be.visible');
      
      cy.contains('You were mentioned in Tech Talk').should('be.visible');
      cy.get('[data-testid="platform-badge-discord"]').should('be.visible');
    });

    it('should configure cross-platform notification settings', () => {
      cy.intercept('PUT', '/api/notifications/cross-platform/settings', {
        statusCode: 200,
        body: { message: 'Cross-platform notification settings updated' }
      }).as('updateCrossPlatformSettings');

      cy.visit('/settings');
      cy.contains('Notifications').click();
      
      cy.get('[data-testid="telegram-notifications"]').click();
      cy.get('[data-testid="discord-notifications"]').click();
      cy.get('[data-testid="federation-notifications"]').click();
      
      cy.contains('Save Settings').click();
      cy.wait('@updateCrossPlatformSettings');
      
      cy.contains('Cross-platform notification settings updated').should('be.visible');
    });
  });

  describe('Error Handling', () => {
    it('should handle platform connection errors', () => {
      cy.intercept('GET', '/api/platforms/status', {
        statusCode: 500,
        body: { error: 'Platform service unavailable' }
      }).as('getPlatformStatusError');

      cy.reload();
      cy.wait('@getPlatformStatusError');
      
      cy.contains('Platform service unavailable').should('be.visible');
      cy.contains('Cross-platform features are currently unavailable').should('be.visible');
    });

    it('should handle room join failures', () => {
      cy.intercept('POST', '/api/federation/rooms/room2/join', {
        statusCode: 403,
        body: { error: 'Room is private' }
      }).as('joinRoomError');

      cy.get('[data-testid="cross-platform-toggle"]').click();
      cy.get('[data-testid="room-item-room2"]').contains('Join').click();
      
      cy.wait('@joinRoomError');
      cy.contains('Room is private').should('be.visible');
    });

    it('should handle message send failures', () => {
      cy.intercept('POST', '/api/federation/rooms/room1/messages', {
        statusCode: 500,
        body: { error: 'Failed to send message' }
      }).as('sendMessageError');

      cy.get('[data-testid="cross-platform-toggle"]').click();
      cy.contains('General Chat').click();
      
      cy.get('[data-testid="room-message-input"]').type('This will fail');
      cy.get('[data-testid="send-room-message"]').click();
      
      cy.wait('@sendMessageError');
      cy.contains('Failed to send message').should('be.visible');
      cy.get('[data-testid="retry-send"]').should('be.visible');
    });
  });
});
