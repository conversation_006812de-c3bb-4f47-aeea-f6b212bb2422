{"scripts": {"dev": "concurrently \"cd sociality/backend && npm run dev\" \"cd sociality/frontend && npm run dev\"", "dev:tunnel": "concurrently \"cd sociality/backend && npm run dev\" \"cd sociality/frontend && npm run dev:tunnel\"", "tunnel:all": "ngrok start --all --config=sociality/ngrok.yml", "tunnel:frontend": "ngrok http 7100", "tunnel:backend": "ngrok http 5000", "tunnel:federation": "ngrok http 7300"}, "dependencies": {"axios": "^1.10.0", "dotenv": "^16.4.7", "emoji-picker-react": "^4.12.2", "express-session": "^1.18.1", "lucide-react": "^0.487.0", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "phosphor-react": "^1.4.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1"}, "devDependencies": {"concurrently": "^9.2.0"}}