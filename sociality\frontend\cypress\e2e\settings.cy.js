describe('Settings Page', () => {
  beforeEach(() => {
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    cy.visit('/settings');
  });

  describe('Profile Settings', () => {
    it('should display current user information', () => {
      cy.contains('Test User').should('be.visible');
      cy.contains('<EMAIL>').should('be.visible');
      cy.contains('@testuser').should('be.visible');
      cy.contains('Test bio').should('be.visible');
    });

    it('should update profile information', () => {
      cy.intercept('PUT', '/api/users/update', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'Updated Name',
          email: '<EMAIL>',
          username: 'testuser',
          bio: 'Updated bio',
          profilePic: ''
        }
      }).as('updateProfile');

      // Update name
      cy.get('input[name="name"]').clear().type('Updated Name');
      
      // Update bio
      cy.get('textarea[name="bio"]').clear().type('Updated bio');
      
      // Save changes
      cy.contains('Save Changes').click();
      
      cy.wait('@updateProfile');
      cy.contains('Profile updated successfully').should('be.visible');
    });

    it('should upload profile picture', () => {
      cy.intercept('POST', '/api/upload', {
        statusCode: 200,
        body: { url: 'https://example.com/profile-pic.jpg' }
      }).as('uploadImage');

      cy.intercept('PUT', '/api/users/update', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          username: 'testuser',
          bio: 'Test bio',
          profilePic: 'https://example.com/profile-pic.jpg'
        }
      }).as('updateProfile');

      // Upload profile picture
      cy.get('input[type="file"]').selectFile('cypress/fixtures/profile-pic.jpg', { force: true });
      cy.wait('@uploadImage');
      
      cy.contains('Save Changes').click();
      cy.wait('@updateProfile');
      
      cy.get('img[src="https://example.com/profile-pic.jpg"]').should('be.visible');
    });

    it('should remove profile picture', () => {
      cy.intercept('PUT', '/api/users/update', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          username: 'testuser',
          bio: 'Test bio',
          profilePic: ''
        }
      }).as('updateProfile');

      cy.get('[data-testid="remove-profile-pic"]').click();
      cy.contains('Save Changes').click();
      
      cy.wait('@updateProfile');
      cy.contains('Profile updated successfully').should('be.visible');
    });

    it('should validate profile information', () => {
      // Try to save with empty name
      cy.get('input[name="name"]').clear();
      cy.contains('Save Changes').click();
      
      cy.contains('Name is required').should('be.visible');
      
      // Try to save with bio too long
      cy.get('input[name="name"]').type('Test User');
      cy.get('textarea[name="bio"]').clear().type('a'.repeat(161)); // Assuming 160 char limit
      cy.contains('Save Changes').click();
      
      cy.contains('Bio must be 160 characters or less').should('be.visible');
    });
  });

  describe('Account Settings', () => {
    it('should change password', () => {
      cy.intercept('PUT', '/api/users/change-password', {
        statusCode: 200,
        body: { message: 'Password changed successfully' }
      }).as('changePassword');

      // Navigate to password change section
      cy.contains('Change Password').click();
      
      // Fill password form
      cy.get('input[name="currentPassword"]').type('oldpassword');
      cy.get('input[name="newPassword"]').type('newpassword123');
      cy.get('input[name="confirmPassword"]').type('newpassword123');
      
      cy.contains('Update Password').click();
      
      cy.wait('@changePassword');
      cy.contains('Password changed successfully').should('be.visible');
    });

    it('should validate password change', () => {
      cy.contains('Change Password').click();
      
      // Test password mismatch
      cy.get('input[name="currentPassword"]').type('oldpassword');
      cy.get('input[name="newPassword"]').type('newpassword123');
      cy.get('input[name="confirmPassword"]').type('differentpassword');
      
      cy.contains('Update Password').click();
      cy.contains('Passwords do not match').should('be.visible');
      
      // Test weak password
      cy.get('input[name="confirmPassword"]').clear().type('123');
      cy.contains('Update Password').click();
      cy.contains('Password must be at least 6 characters').should('be.visible');
    });

    it('should handle incorrect current password', () => {
      cy.intercept('PUT', '/api/users/change-password', {
        statusCode: 400,
        body: { error: 'Current password is incorrect' }
      }).as('changePasswordError');

      cy.contains('Change Password').click();
      
      cy.get('input[name="currentPassword"]').type('wrongpassword');
      cy.get('input[name="newPassword"]').type('newpassword123');
      cy.get('input[name="confirmPassword"]').type('newpassword123');
      
      cy.contains('Update Password').click();
      
      cy.wait('@changePasswordError');
      cy.contains('Current password is incorrect').should('be.visible');
    });

    it('should change email address', () => {
      cy.intercept('PUT', '/api/users/change-email', {
        statusCode: 200,
        body: { message: 'Email verification sent to new address' }
      }).as('changeEmail');

      cy.contains('Change Email').click();
      
      cy.get('input[name="newEmail"]').type('<EMAIL>');
      cy.get('input[name="password"]').type('currentpassword');
      
      cy.contains('Send Verification').click();
      
      cy.wait('@changeEmail');
      cy.contains('Email verification sent').should('be.visible');
    });

    it('should validate email change', () => {
      cy.contains('Change Email').click();
      
      // Test invalid email format
      cy.get('input[name="newEmail"]').type('invalid-email');
      cy.get('input[name="password"]').type('password');
      
      cy.contains('Send Verification').click();
      cy.contains('Please enter a valid email').should('be.visible');
      
      // Test same email
      cy.get('input[name="newEmail"]').clear().type('<EMAIL>');
      cy.contains('Send Verification').click();
      cy.contains('This is your current email').should('be.visible');
    });
  });

  describe('Privacy Settings', () => {
    it('should toggle profile privacy', () => {
      cy.intercept('PUT', '/api/users/privacy', {
        statusCode: 200,
        body: { message: 'Privacy settings updated' }
      }).as('updatePrivacy');

      // Toggle private profile
      cy.get('[data-testid="private-profile-toggle"]').click();
      
      cy.wait('@updatePrivacy');
      cy.contains('Privacy settings updated').should('be.visible');
    });

    it('should manage blocked users', () => {
      cy.intercept('GET', '/api/users/blocked', {
        statusCode: 200,
        body: [
          { _id: '2', username: 'blocked_user', name: 'Blocked User' }
        ]
      }).as('getBlockedUsers');

      cy.contains('Blocked Users').click();
      cy.wait('@getBlockedUsers');
      
      cy.contains('Blocked User').should('be.visible');
      cy.contains('@blocked_user').should('be.visible');
    });

    it('should unblock user', () => {
      cy.intercept('GET', '/api/users/blocked', {
        statusCode: 200,
        body: [
          { _id: '2', username: 'blocked_user', name: 'Blocked User' }
        ]
      }).as('getBlockedUsers');

      cy.intercept('POST', '/api/users/unblock/2', {
        statusCode: 200,
        body: { message: 'User unblocked successfully' }
      }).as('unblockUser');

      cy.contains('Blocked Users').click();
      cy.wait('@getBlockedUsers');
      
      cy.get('[data-testid="unblock-button"]').click();
      cy.contains('Confirm Unblock').click();
      
      cy.wait('@unblockUser');
      cy.contains('User unblocked successfully').should('be.visible');
    });

    it('should configure who can message you', () => {
      cy.intercept('PUT', '/api/users/message-settings', {
        statusCode: 200,
        body: { message: 'Message settings updated' }
      }).as('updateMessageSettings');

      cy.contains('Message Settings').click();
      
      // Select who can message
      cy.get('select[name="messagePrivacy"]').select('followers');
      
      cy.contains('Save Settings').click();
      cy.wait('@updateMessageSettings');
      
      cy.contains('Message settings updated').should('be.visible');
    });
  });

  describe('Notification Settings', () => {
    it('should configure notification preferences', () => {
      cy.intercept('PUT', '/api/users/notifications', {
        statusCode: 200,
        body: { message: 'Notification settings updated' }
      }).as('updateNotifications');

      cy.contains('Notifications').click();
      
      // Toggle various notification types
      cy.get('[data-testid="likes-notifications"]').click();
      cy.get('[data-testid="comments-notifications"]').click();
      cy.get('[data-testid="follows-notifications"]').click();
      cy.get('[data-testid="messages-notifications"]').click();
      
      cy.contains('Save Notification Settings').click();
      cy.wait('@updateNotifications');
      
      cy.contains('Notification settings updated').should('be.visible');
    });

    it('should configure email notifications', () => {
      cy.intercept('PUT', '/api/users/email-notifications', {
        statusCode: 200,
        body: { message: 'Email notification settings updated' }
      }).as('updateEmailNotifications');

      cy.contains('Email Notifications').click();
      
      cy.get('[data-testid="email-digest"]').click();
      cy.get('[data-testid="email-mentions"]').click();
      
      cy.contains('Save Email Settings').click();
      cy.wait('@updateEmailNotifications');
      
      cy.contains('Email notification settings updated').should('be.visible');
    });
  });

  describe('Platform Integrations', () => {
    it('should connect to Telegram', () => {
      cy.intercept('GET', '/api/platforms/telegram/auth-url', {
        statusCode: 200,
        body: { authUrl: 'https://telegram.org/auth' }
      }).as('getTelegramAuth');

      cy.intercept('POST', '/api/platforms/telegram/connect', {
        statusCode: 200,
        body: { message: 'Telegram connected successfully' }
      }).as('connectTelegram');

      cy.contains('Platform Integrations').click();
      cy.contains('Connect Telegram').click();
      
      cy.wait('@getTelegramAuth');
      
      // Mock successful connection
      cy.window().then((win) => {
        win.postMessage({ type: 'TELEGRAM_AUTH_SUCCESS', data: { userId: 'tg123' } }, '*');
      });
      
      cy.wait('@connectTelegram');
      cy.contains('Telegram connected successfully').should('be.visible');
    });

    it('should disconnect from Telegram', () => {
      cy.intercept('DELETE', '/api/platforms/telegram/disconnect', {
        statusCode: 200,
        body: { message: 'Telegram disconnected successfully' }
      }).as('disconnectTelegram');

      cy.contains('Platform Integrations').click();
      cy.contains('Disconnect Telegram').click();
      cy.contains('Confirm Disconnect').click();
      
      cy.wait('@disconnectTelegram');
      cy.contains('Telegram disconnected successfully').should('be.visible');
    });

    it('should connect to Discord', () => {
      cy.intercept('GET', '/api/platforms/discord/auth-url', {
        statusCode: 200,
        body: { authUrl: 'https://discord.com/oauth2/authorize' }
      }).as('getDiscordAuth');

      cy.contains('Platform Integrations').click();
      cy.contains('Connect Discord').click();
      
      cy.wait('@getDiscordAuth');
      
      // Should open Discord OAuth in new window
      cy.window().then((win) => {
        cy.stub(win, 'open').as('windowOpen');
      });
      
      cy.get('@windowOpen').should('have.been.calledWith', 'https://discord.com/oauth2/authorize');
    });
  });

  describe('Data Management', () => {
    it('should export user data', () => {
      cy.intercept('POST', '/api/users/export-data', {
        statusCode: 200,
        body: { downloadUrl: 'https://example.com/export.zip' }
      }).as('exportData');

      cy.contains('Data Export').click();
      cy.contains('Export My Data').click();
      
      cy.wait('@exportData');
      cy.contains('Data export ready').should('be.visible');
      cy.get('a[href="https://example.com/export.zip"]').should('be.visible');
    });

    it('should show data export progress', () => {
      cy.intercept('POST', '/api/users/export-data', {
        statusCode: 202,
        body: { message: 'Export in progress' }
      }).as('exportDataProgress');

      cy.contains('Data Export').click();
      cy.contains('Export My Data').click();
      
      cy.wait('@exportDataProgress');
      cy.contains('Preparing your data export').should('be.visible');
      cy.get('[data-testid="export-progress"]').should('be.visible');
    });

    it('should delete account', () => {
      cy.intercept('DELETE', '/api/users/delete-account', {
        statusCode: 200,
        body: { message: 'Account deleted successfully' }
      }).as('deleteAccount');

      cy.contains('Delete Account').click();
      
      // Should show warning modal
      cy.contains('This action cannot be undone').should('be.visible');
      
      // Type confirmation
      cy.get('input[placeholder="Type DELETE to confirm"]').type('DELETE');
      cy.get('input[name="password"]').type('currentpassword');
      
      cy.contains('Delete My Account').click();
      
      cy.wait('@deleteAccount');
      cy.url().should('include', '/auth');
    });

    it('should validate account deletion', () => {
      cy.contains('Delete Account').click();
      
      // Try without typing DELETE
      cy.get('input[name="password"]').type('password');
      cy.contains('Delete My Account').should('be.disabled');
      
      // Type wrong confirmation
      cy.get('input[placeholder="Type DELETE to confirm"]').type('delete');
      cy.contains('Delete My Account').should('be.disabled');
      
      // Type correct confirmation
      cy.get('input[placeholder="Type DELETE to confirm"]').clear().type('DELETE');
      cy.contains('Delete My Account').should('not.be.disabled');
    });
  });

  describe('Theme Settings', () => {
    it('should toggle dark mode', () => {
      cy.get('[data-testid="theme-toggle"]').click();
      
      // Should apply dark theme
      cy.get('body').should('have.class', 'chakra-ui-dark');
      
      // Toggle back to light mode
      cy.get('[data-testid="theme-toggle"]').click();
      cy.get('body').should('have.class', 'chakra-ui-light');
    });

    it('should persist theme preference', () => {
      cy.get('[data-testid="theme-toggle"]').click();
      
      cy.reload();
      
      // Should maintain dark theme after reload
      cy.get('body').should('have.class', 'chakra-ui-dark');
    });
  });

  describe('Settings Navigation', () => {
    it('should navigate between settings sections', () => {
      const sections = [
        'Profile',
        'Account',
        'Privacy',
        'Notifications',
        'Platform Integrations',
        'Data Management'
      ];

      sections.forEach(section => {
        cy.contains(section).click();
        cy.get(`[data-testid="${section.toLowerCase()}-section"]`).should('be.visible');
      });
    });

    it('should show unsaved changes warning', () => {
      // Make a change without saving
      cy.get('input[name="name"]').clear().type('Changed Name');
      
      // Try to navigate away
      cy.contains('Account').click();
      
      // Should show warning
      cy.contains('You have unsaved changes').should('be.visible');
      cy.contains('Stay on Page').should('be.visible');
      cy.contains('Leave Anyway').should('be.visible');
    });

    it('should save changes before navigating', () => {
      cy.intercept('PUT', '/api/users/update', {
        statusCode: 200,
        body: { message: 'Profile updated successfully' }
      }).as('updateProfile');

      // Make a change
      cy.get('input[name="name"]').clear().type('Changed Name');
      
      // Try to navigate away
      cy.contains('Account').click();
      
      // Save changes
      cy.contains('Save & Continue').click();
      cy.wait('@updateProfile');
      
      // Should navigate to account section
      cy.get('[data-testid="account-section"]').should('be.visible');
    });
  });

  describe('Responsive Settings', () => {
    it('should display correctly on mobile', () => {
      cy.viewport(375, 667);
      
      // Settings should be in mobile layout
      cy.get('[data-testid="mobile-settings-menu"]').should('be.visible');
      
      // Navigate to profile section
      cy.contains('Profile').click();
      cy.get('input[name="name"]').should('be.visible');
    });

    it('should display correctly on tablet', () => {
      cy.viewport(768, 1024);
      
      // Should show sidebar navigation
      cy.get('[data-testid="settings-sidebar"]').should('be.visible');
      cy.get('[data-testid="settings-content"]').should('be.visible');
    });
  });
});
