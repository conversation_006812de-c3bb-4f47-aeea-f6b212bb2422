import { Button, Flex, Image, Box, useColorModeValue } from "@chakra-ui/react";
import { useRecoilValue } from "recoil";
import { userAtom } from "../atoms";
import { Link as RouterLink, useLocation } from "react-router-dom";
import { House, User, Chat, Gear, MagnifyingGlass, Bell } from "phosphor-react";
import { useDynamicHeader } from "../hooks/useDynamicHeader";

const Header = () => {
	const user = useRecoilValue(userAtom);
	const location = useLocation();

	// Use dynamic header hook for responsive behavior
	const {
		deviceCategory,
		isMobile,
		responsive,
		getLogoStyles
	} = useDynamicHeader();

	// Theme-aware colors for logo and navigation
	const logoFilter = useColorModeValue("none", "none");
	const logoBgColor = useColorModeValue("rgba(247, 250, 252, 0.9)", "rgba(8, 8, 8, 0.7)");
	const logoDropShadow = useColorModeValue(
		"drop-shadow(0 0 2px rgba(0, 0, 0, 0.3))",
		"drop-shadow(0 0 2px rgba(0, 0, 0, 0.5))"
	);
	const navIconColor = useColorModeValue("#4a5568", "#616161");

	// Dynamic header handles scroll behavior automatically

	// Function to check if a path is active
	const isActive = (path) => {
		if (path === "/") {
			return location.pathname === "/";
		}
		if (path.startsWith("/:username")) {
			// For user profile page, check if we're on a user page but not on a specific post
			return location.pathname.match(/^\/[^/]+$/) && location.pathname !== "/search" &&
				location.pathname !== "/notifications" && location.pathname !== "/chat" &&
				location.pathname !== "/settings" && location.pathname !== "/auth";
		}
		return location.pathname.startsWith(path);
	};

	return (
		<Flex justifyContent="space-between" align="center" mt={{ base: 4, sm: 5, xl: 6 }} mb={{ base: 8, sm: 10, xl: 12 }}>
			{/* App Logo with dynamic responsive positioning */}
			<Flex
				position="fixed"
				top={responsive.logoPosition.base.top}
				left={responsive.logoPosition.base.left}
				transform={responsive.logoPosition.base.transform}
				opacity={responsive.logoOpacity.base}
				transition={deviceCategory === 'xs' ? 'all 0.2s ease-in-out' : 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'}
				zIndex={1000}
				className="mobile-logo-scroll dynamic-mobile-header"
			>
				<Box position="relative" display="inline-block">
					{/* Background glow effect */}
					<Box
						position="absolute"
						top="50%"
						left="50%"
						transform="translate(-50%, -50%)"
						width="40px"
						height="40px"
						borderRadius="full"
						filter="blur(15px)"
						bg="linear-gradient(45deg, rgba(0,204,133,0.3), rgba(0,121,185,0.3))"
						opacity="0.6"
						zIndex="-1"
					/>
					{/* Semi-transparent background circle for better visibility */}
					<Box
						position="absolute"
						top="50%"
						left="50%"
						transform="translate(-50%, -50%)"
						width="32px"
						height="32px"
						borderRadius="full"
						bg={logoBgColor}
						zIndex="1"
					/>
					<Image
						alt="logo"
						w={`${getLogoStyles().width}`}
						h={`${getLogoStyles().height}`}
						src="/icon.svg"
						cursor="pointer"
						onClick={() => window.location.href = "/"}
						transition="transform 0.3s ease"
						_hover={{ transform: "scale(1.1)" }}
						position="relative"
						zIndex="2"
						filter={logoFilter}
						style={{
							filter: logoDropShadow
						}}
					/>
				</Box>
			</Flex>

			{/* Dynamic responsive navigation */}
			{user && (
				<Flex
					direction={responsive.navDirection.base}
					align="center"
					position="fixed"
					left={responsive.navPosition.base.left}
					right={responsive.navPosition.base.right}
					bottom={responsive.navPosition.base.bottom}
					top={responsive.navPosition.base.top}
					transform={responsive.navPosition.base.transform}
					width={responsive.navPosition.base.width}
					gap={responsive.navPosition.base.gap || `${responsive.spacing.base}px`}
					className={isMobile ? "glass-navbar visible dynamic-mobile-header" : "glass-navbar visible"}
					px={responsive.navPosition.base.padding?.split(' ')[1] || `${responsive.spacing.base * 1.5}px`}
					py={responsive.navPosition.base.padding?.split(' ')[0] || `${responsive.spacing.base}px`}
					paddingBottom={responsive.navPosition.base.paddingBottom}
					borderRadius={responsive.navPosition.base.borderRadius || (isMobile ? "0" : "12px")}
					justifyContent={responsive.navPosition.base.justifyContent || "center"}
					zIndex={100}
					data-testid={isMobile ? "mobile-nav" : "desktop-nav"}
				>
					{/* Home Button */}
					<Button
						as={RouterLink}
						to="/"
						variant="ghost"
						minH={responsive.buttonSize.base}
						minW={responsive.buttonSize.base}
						bg={isActive("/") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
						data-testid="nav-home-button"
						aria-label="Home"
					>
						<House
							size={responsive.iconSize.base}
							weight={isActive("/") ? "fill" : "bold"}
							color={isActive("/") ? "#00CC85" : navIconColor}
						/>
						{isActive("/") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>

					{/* Search Button */}
					<Button
						as={RouterLink}
						to="/search"
						variant="ghost"
						minH={responsive.buttonSize.base}
						minW={responsive.buttonSize.base}
						bg={isActive("/search") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
						data-testid="nav-search-button"
						aria-label="Search"
					>
						<MagnifyingGlass
							size={responsive.iconSize.base}
							weight={isActive("/search") ? "fill" : "bold"}
							color={isActive("/search") ? "#00CC85" : navIconColor}
						/>
						{isActive("/search") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>

					{/* Profile Button */}
					<Button
						as={RouterLink}
						to={`/${user.username}`}
						variant="ghost"
						minH={responsive.buttonSize.base}
						minW={responsive.buttonSize.base}
						bg={isActive("/:username") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
					>
						<User
							size={responsive.iconSize.base}
							weight="fill"
							color={isActive("/:username") ? "#00CC85" : "#616161"}
						/>
						{isActive("/:username") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>

					{/* Notifications Button */}
					<Button
						as={RouterLink}
						to="/notifications"
						variant="ghost"
						minH={responsive.buttonSize.base}
						minW={responsive.buttonSize.base}
						bg={isActive("/notifications") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
					>
						<Bell
							size={responsive.iconSize.base}
							weight={isActive("/notifications") ? "fill" : "bold"}
							color={isActive("/notifications") ? "#00CC85" : "#616161"}
						/>
						{isActive("/notifications") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>

					{/* Chat Button */}
					<Button
						as={RouterLink}
						to="/chat"
						variant="ghost"
						minH={responsive.buttonSize.base}
						minW={responsive.buttonSize.base}
						bg={isActive("/chat") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
					>
						<Chat
							size={responsive.iconSize.base}
							weight="fill"
							color={isActive("/chat") ? "#00CC85" : "#616161"}
						/>
						{isActive("/chat") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>




					{/* Settings Button */}
					<Button
						as={RouterLink}
						to="/settings"
						variant="ghost"
						minH={responsive.buttonSize.base}
						minW={responsive.buttonSize.base}
						bg={isActive("/settings") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
					>
						<Gear
							size={responsive.iconSize.base}
							weight={isActive("/settings") ? "fill" : "bold"}
							color={isActive("/settings") ? "#00CC85" : "#616161"}
						/>
						{isActive("/settings") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>
				</Flex>
			)}
		</Flex>
	);
};

export default Header;
