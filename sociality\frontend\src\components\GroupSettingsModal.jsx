import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Button,
  VStack,
  HStack,
  Text,
  Input,
  Avatar,
  IconButton,
  Divider,
  useColorModeValue,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Box,
  Flex,
  Badge,
  Tooltip,
  Spinner,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Icon,
  SimpleGrid
} from '@chakra-ui/react';
import {
  FaShare,
  FaTrash,
  FaEdit,
  FaCamera,
  FaCopy,
  FaGlobe,
  FaTelegram,
  FaDiscord,
  FaUsers,
  FaImage
} from 'react-icons/fa';
import { BsCheck2All } from 'react-icons/bs';
import { useRecoilValue } from 'recoil';
import { userAtom } from '../atoms';
import { fetchWithSession } from '../utils/api';
import useShowToast from '../hooks/ui/useShowToast';

const GroupSettingsModal = ({ 
  isOpen, 
  onClose, 
  selectedConversation, 
  onUpdateGroup,
  onDeleteGroup 
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [groupName, setGroupName] = useState(selectedConversation?.name || '');
  const [groupPhoto, setGroupPhoto] = useState(selectedConversation?.groupPhoto || '');
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [copied, setCopied] = useState(false);
  const [roomDetails, setRoomDetails] = useState(null);
  const [loadingRoomDetails, setLoadingRoomDetails] = useState(false);
  const [participants, setParticipants] = useState([]);
  const [loadingParticipants, setLoadingParticipants] = useState(false);
  const [participantsSummary, setParticipantsSummary] = useState(null);
  
  const fileInputRef = useRef(null);
  const cancelRef = useRef();
  const currentUser = useRecoilValue(userAtom);
  const showToast = useShowToast();
  const toast = useToast();

  // Theme-aware colors
  const modalBgColor = useColorModeValue("white", "#1a1a1a");
  const modalTextColor = useColorModeValue("gray.800", "white");
  const modalBorderColor = useColorModeValue("gray.200", "gray.700");
  const sectionBgColor = useColorModeValue("gray.50", "#252525");
  const hoverBgColor = useColorModeValue("gray.100", "#2d2d2d");
  const mutedTextColor = useColorModeValue("gray.600", "gray.400");

  // Check if current user is the creator/admin
  const isCreator = selectedConversation?.creator?._id === currentUser?._id ||
                   selectedConversation?.creator === currentUser?._id;
  const isAdmin = selectedConversation?.participants?.find(
    p => (p.user?._id === currentUser?._id || p.user === currentUser?._id)
  )?.role === 'admin';
  const canEdit = isCreator || isAdmin;

  // Get participant count (use room details if available, fallback to conversation data)
  const participantCount = roomDetails?.participantCount || selectedConversation?.participants?.length || 0;

  // Handle photo upload
  const handlePhotoUpload = useCallback(async (file) => {
    if (!file) return;

    // Check file size (5MB limit for group photos)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      showToast("Error", "Image size must be less than 5MB", "error");
      return;
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
      showToast("Error", "Please select an image file", "error");
      return;
    }

    setIsUploading(true);
    try {
      // Convert to base64
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          console.log('Uploading photo for room:', selectedConversation._id);
          console.log('Photo data size:', e.target.result.length);
          console.log('Photo data preview:', e.target.result.substring(0, 50) + '...');

          const response = await fetchWithSession(`/api/cross-platform/rooms/${selectedConversation._id}/photo`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              photo: e.target.result
            })
          });

          const data = await response.json();
          console.log('Photo upload response:', { status: response.status, data });

          if (response.ok && data.success) {
            setGroupPhoto(data.groupPhoto);
            if (onUpdateGroup) {
              onUpdateGroup({ ...selectedConversation, groupPhoto: data.groupPhoto });
            }
            showToast("Success", "Group photo updated successfully", "success");
          } else {
            console.error('Photo upload failed:', { status: response.status, data });
            throw new Error(data.error || `Failed to update group photo (${response.status})`);
          }
        } catch (error) {
          console.error('Error updating group photo:', error);
          showToast("Error", error.message || "Failed to update group photo", "error");
        } finally {
          setIsUploading(false);
        }
      };
      reader.onerror = () => {
        showToast("Error", "Failed to read image file", "error");
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error processing image:', error);
      showToast("Error", "Failed to process image", "error");
      setIsUploading(false);
    }
  }, [selectedConversation, onUpdateGroup, showToast]);

  // Handle file input change
  const handleFileChange = useCallback((e) => {
    const file = e.target.files[0];
    if (file) {
      handlePhotoUpload(file);
    }
  }, [handlePhotoUpload]);



  // Fetch room details from backend
  const fetchRoomDetails = useCallback(async () => {
    if (!selectedConversation?._id) return;

    setLoadingRoomDetails(true);
    try {
      const response = await fetchWithSession(`/api/cross-platform/rooms/${selectedConversation._id}/details`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setRoomDetails(data.room);
        }
      }
    } catch (error) {
      console.error('Error fetching room details:', error);
    } finally {
      setLoadingRoomDetails(false);
    }
  }, [selectedConversation?._id]);

  // Fetch room details when modal opens
  React.useEffect(() => {
    if (isOpen && selectedConversation?._id) {
      fetchRoomDetails();
    }
  }, [isOpen, selectedConversation?._id, fetchRoomDetails]);

  // Handle group name update
  const handleUpdateGroupName = useCallback(async () => {

    if (!groupName.trim()) {
      showToast("Error", "Group name cannot be empty", "error");
      return;
    }

    if (groupName.trim() === selectedConversation?.name) {
      setIsEditing(false);
      return;
    }

    try {
      console.log('Updating group name:', {
        roomId: selectedConversation._id,
        newName: groupName,
        currentName: selectedConversation.name
      });
      const response = await fetchWithSession(`/api/cross-platform/rooms/${selectedConversation._id}/name`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: groupName.trim()
        })
      });

      const data = await response.json();
      console.log('Name update response:', { status: response.status, data });

      if (response.ok && data.success) {
        if (onUpdateGroup) {
          onUpdateGroup({ ...selectedConversation, name: data.name });
        }
        setIsEditing(false);
        showToast("Success", "Group name updated successfully", "success");
      } else {
        console.error('Name update failed:', { status: response.status, data });
        throw new Error(data.error || `Failed to update group name (${response.status})`);
      }
    } catch (error) {
      console.error('Error updating group name:', error);
      showToast("Error", error.message || "Failed to update group name", "error");
    }
  }, [groupName, selectedConversation, onUpdateGroup, showToast]);

  // Handle copy room ID
  const handleCopyRoomId = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(selectedConversation._id);
      setCopied(true);
      toast({
        title: "Room ID Copied!",
        description: "Share this ID with others to invite them to the group",
        status: "success",
        duration: 2000,
        isClosable: true,
        position: "top"
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      showToast("Error", "Failed to copy Room ID", "error");
    }
  }, [selectedConversation, toast, showToast]);

  // Handle delete group
  const handleDeleteGroup = useCallback(async () => {
    setIsDeleting(true);
    try {
      const response = await fetchWithSession(`/api/cross-platform/rooms/${selectedConversation._id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        if (onDeleteGroup) {
          onDeleteGroup(selectedConversation);
        }
        onClose();
        showToast("Success", "Group deleted successfully", "success");
      } else {
        throw new Error('Failed to delete group');
      }
    } catch (error) {
      console.error('Error deleting group:', error);
      showToast("Error", "Failed to delete group", "error");
    } finally {
      setIsDeleting(false);
      setShowDeleteAlert(false);
    }
  }, [selectedConversation, onDeleteGroup, onClose, showToast]);

  // Fetch comprehensive participants from all platforms
  const fetchParticipants = useCallback(async () => {
    if (!selectedConversation?._id) return;

    setLoadingParticipants(true);
    try {
      const response = await fetchWithSession(`/api/cross-platform/rooms/${selectedConversation._id}/participants`);

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setParticipants(data.participants);
          setParticipantsSummary(data.summary);
        } else {
          throw new Error(data.error || 'Failed to fetch participants');
        }
      } else {
        throw new Error('Failed to fetch participants');
      }
    } catch (error) {
      console.error('Error fetching participants:', error);
      showToast("Error", "Failed to load participants", "error");
    } finally {
      setLoadingParticipants(false);
    }
  }, [selectedConversation, showToast]);

  // Handle share room
  const handleShareRoom = useCallback(() => {
    const shareText = `Join our cross-platform group "${selectedConversation?.name || 'Group Chat'}"!\n\nRoom ID: ${selectedConversation._id}\n\nYou can join from Sociality, Telegram, or Discord!`;
    
    if (navigator.share) {
      navigator.share({
        title: `Join ${selectedConversation?.name || 'Group Chat'}`,
        text: shareText,
      }).catch(console.error);
    } else {
      // Fallback to copying to clipboard
      navigator.clipboard.writeText(shareText).then(() => {
        toast({
          title: "Share Link Copied!",
          description: "Share this with others to invite them to the group",
          status: "success",
          duration: 3000,
          isClosable: true,
          position: "top"
        });
      }).catch(() => {
        showToast("Error", "Failed to copy share link", "error");
      });
    }
  }, [selectedConversation, toast, showToast]);



  // Fetch participants when modal opens
  useEffect(() => {
    if (isOpen && selectedConversation?._id) {
      fetchParticipants();
    }
  }, [isOpen, selectedConversation, fetchParticipants]);

  if (!selectedConversation) return null;

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} isCentered size="md">
        <ModalOverlay bg="blackAlpha.800" />
        <ModalContent
          bg={modalBgColor}
          color={modalTextColor}
          border={`1px solid ${modalBorderColor}`}
          borderRadius="xl"
          maxW="400px"
        >
          <ModalHeader>
            <Flex alignItems="center" gap={3}>
              <Box
                bg="rgba(0, 204, 133, 0.1)"
                p={2}
                borderRadius="lg"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <FaUsers size={16} color="#00CC85" />
              </Box>
              <Text>Group Settings</Text>
            </Flex>
          </ModalHeader>
          <ModalCloseButton />
          
          <ModalBody pb={6}>
            <Tabs variant="soft-rounded" colorScheme="green">
              <TabList mb={4}>
                <Tab>Settings</Tab>
                <Tab>
                  <Flex alignItems="center" gap={2}>
                    <FaUsers size={14} />
                    <Text>Members</Text>
                    {participantsSummary && (
                      <Badge colorScheme="green" fontSize="xs">
                        {participantsSummary.total}
                      </Badge>
                    )}
                  </Flex>
                </Tab>
              </TabList>

              <TabPanels>
                <TabPanel p={0}>
                  <VStack spacing={4} align="stretch">
              {/* Group Photo Section */}
              <Box bg={sectionBgColor} p={4} borderRadius="lg">
                <Text fontSize="sm" fontWeight="medium" mb={3} color={mutedTextColor}>
                  GROUP PHOTO
                </Text>
                <Flex alignItems="center" gap={4}>
                  <Box position="relative">
                    <Avatar
                      src={groupPhoto || selectedConversation.groupPhoto}
                      size="lg"
                      name={selectedConversation.name}
                      bg="rgba(0, 204, 133, 0.1)"
                      color="#00CC85"
                    />
                    <IconButton
                      icon={isUploading ? <Spinner size="sm" /> : <FaCamera />}
                      size="sm"
                      borderRadius="full"
                      position="absolute"
                      bottom="-2px"
                      right="-2px"
                      bg="rgba(0, 204, 133, 0.9)"
                      color="white"
                      _hover={{ bg: "rgba(0, 204, 133, 1)" }}
                      onClick={() => fileInputRef.current?.click()}
                      isDisabled={isUploading}
                      aria-label="Change group photo"
                    />
                  </Box>
                  <Box flex="1">
                    <Text fontSize="sm" color={mutedTextColor}>
                      Click the camera icon to change the group photo
                    </Text>
                  </Box>
                </Flex>
                <Input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept="image/*"
                  display="none"
                />
              </Box>

              {/* Group Name Section */}
              <Box bg={sectionBgColor} p={4} borderRadius="lg">
                <Flex justifyContent="space-between" alignItems="center" mb={3}>
                  <Text fontSize="sm" fontWeight="medium" color={mutedTextColor}>
                    GROUP NAME
                  </Text>
                  {!isEditing && (
                    <IconButton
                      icon={<FaEdit />}
                      size="xs"
                      variant="ghost"
                      onClick={() => setIsEditing(true)}
                      aria-label="Edit group name"
                    />
                  )}
                </Flex>
                {isEditing ? (
                  <HStack>
                    <Input
                      value={groupName}
                      onChange={(e) => setGroupName(e.target.value)}
                      placeholder="Enter group name"
                      size="sm"
                      maxLength={50}
                    />
                    <IconButton
                      icon={<BsCheck2All />}
                      size="sm"
                      colorScheme="green"
                      onClick={handleUpdateGroupName}
                      aria-label="Save group name"
                    />
                  </HStack>
                ) : (
                  <Text fontSize="md" fontWeight="medium">
                    {selectedConversation.name || 'Unnamed Group'}
                  </Text>
                )}
              </Box>

              {/* Room Info Section */}
              <Box bg={sectionBgColor} p={4} borderRadius="lg">
                <Text fontSize="sm" fontWeight="medium" mb={3} color={mutedTextColor}>
                  ROOM INFORMATION
                </Text>
                <VStack spacing={3} align="stretch">
                  <Flex justifyContent="space-between" alignItems="center">
                    <Text fontSize="sm">Room ID</Text>
                    <HStack>
                      <Text fontSize="xs" fontFamily="mono" color={mutedTextColor}>
                        {selectedConversation._id?.slice(0, 8)}...
                      </Text>
                      <IconButton
                        icon={copied ? <BsCheck2All /> : <FaCopy />}
                        size="xs"
                        variant="ghost"
                        onClick={handleCopyRoomId}
                        color={copied ? "green.400" : mutedTextColor}
                        aria-label="Copy room ID"
                      />
                    </HStack>
                  </Flex>
                  
                  <Flex justifyContent="space-between" alignItems="center">
                    <Text fontSize="sm">Platforms</Text>
                    <HStack spacing={1}>
                      <Badge colorScheme="green" variant="subtle">
                        <FaGlobe size={10} style={{ marginRight: '4px' }} />
                        Sociality
                      </Badge>
                      <Badge colorScheme="blue" variant="subtle">
                        <FaTelegram size={10} style={{ marginRight: '4px' }} />
                        Telegram
                      </Badge>
                      <Badge colorScheme="purple" variant="subtle">
                        <FaDiscord size={10} style={{ marginRight: '4px' }} />
                        Discord
                      </Badge>
                    </HStack>
                  </Flex>

                  <Flex justifyContent="space-between" alignItems="center">
                    <Text fontSize="sm">Members</Text>
                    <HStack>
                      <FaUsers size={12} color={mutedTextColor} />
                      {loadingRoomDetails ? (
                        <Spinner size="xs" color={mutedTextColor} />
                      ) : (
                        <Text fontSize="sm" color={mutedTextColor}>
                          {participantCount} {participantCount === 1 ? 'member' : 'members'}
                        </Text>
                      )}
                    </HStack>
                  </Flex>
                </VStack>
              </Box>

              <Divider />

              {/* Action Buttons */}
              <VStack spacing={2} align="stretch">
                <Button
                  leftIcon={<FaShare />}
                  variant="ghost"
                  justifyContent="flex-start"
                  onClick={handleShareRoom}
                  _hover={{ bg: hoverBgColor }}
                >
                  Share Room
                </Button>

                <Button
                  leftIcon={<FaTrash />}
                  variant="ghost"
                  justifyContent="flex-start"
                  color="red.400"
                  _hover={{ bg: "rgba(255, 0, 0, 0.1)", color: "red.300" }}
                  onClick={() => setShowDeleteAlert(true)}
                >
                  Delete Group
                </Button>
              </VStack>
                  </VStack>
                </TabPanel>

                <TabPanel p={0}>
                  {/* Participants Tab */}
                  <VStack spacing={4} align="stretch">
                    {loadingParticipants ? (
                      <Flex justify="center" py={8}>
                        <Spinner size="lg" color="green.500" />
                      </Flex>
                    ) : (
                      <>
                        {/* Platform Summary */}
                        {participantsSummary && (
                          <Box bg={sectionBgColor} p={4} borderRadius="lg">
                            <Text fontSize="sm" fontWeight="medium" mb={3} color={mutedTextColor}>
                              PLATFORM BREAKDOWN
                            </Text>
                            <SimpleGrid columns={2} spacing={3}>
                              <Flex alignItems="center" gap={2}>
                                <Icon as={FaGlobe} color="#00CC85" />
                                <Text fontSize="sm">Sociality: {participantsSummary.sociality}</Text>
                              </Flex>
                              <Flex alignItems="center" gap={2}>
                                <Icon as={FaTelegram} color="#0088cc" />
                                <Text fontSize="sm">Telegram: {participantsSummary.telegram}</Text>
                              </Flex>
                              <Flex alignItems="center" gap={2}>
                                <Icon as={FaDiscord} color="#5865F2" />
                                <Text fontSize="sm">Discord: {participantsSummary.discord}</Text>
                              </Flex>
                              {participantsSummary.other > 0 && (
                                <Flex alignItems="center" gap={2}>
                                  <Icon as={FaUsers} color="gray.500" />
                                  <Text fontSize="sm">Other: {participantsSummary.other}</Text>
                                </Flex>
                              )}
                            </SimpleGrid>
                          </Box>
                        )}

                        {/* Participants List */}
                        <Box bg={sectionBgColor} p={4} borderRadius="lg">
                          <Text fontSize="sm" fontWeight="medium" mb={3} color={mutedTextColor}>
                            ALL MEMBERS ({participants.length})
                          </Text>
                          <VStack spacing={3} align="stretch" maxH="300px" overflowY="auto">
                            {participants.map((participant) => {
                              // Debug profile picture loading
                              console.log('Participant:', participant.username, 'Platform:', participant.platform, 'ProfilePic:', participant.profilePic);

                              return (
                                <Flex key={`${participant.platform}-${participant.id}`} alignItems="center" gap={3}>
                                  <Avatar
                                    src={participant.profilePic || ''}
                                    name={participant.name || participant.username}
                                    size="sm"
                                    bg={participant.platform === 'sociality' ? "rgba(0, 204, 133, 0.1)" : "gray.100"}
                                    color={participant.platform === 'sociality' ? "#00CC85" : "gray.500"}
                                    onError={(e) => {
                                      console.log('Avatar failed to load for:', participant.username, 'URL:', participant.profilePic);
                                      e.target.src = ''; // Clear broken image
                                    }}
                                  />
                                <Box flex="1">
                                  <Flex alignItems="center" gap={2}>
                                    <Text fontSize="sm" fontWeight="medium">
                                      {participant.name || participant.username}
                                    </Text>
                                    {participant.platform === 'sociality' ? (
                                      <Icon as={FaGlobe} color="#00CC85" size="12px" />
                                    ) : participant.platform === 'telegram' ? (
                                      <Icon as={FaTelegram} color="#0088cc" size="12px" />
                                    ) : participant.platform === 'discord' ? (
                                      <Icon as={FaDiscord} color="#5865F2" size="12px" />
                                    ) : (
                                      <Badge size="xs" colorScheme="gray">{participant.platform}</Badge>
                                    )}
                                    {participant.role === 'admin' && (
                                      <Badge size="xs" colorScheme="green">Admin</Badge>
                                    )}
                                  </Flex>
                                  <Text fontSize="xs" color={mutedTextColor}>
                                    {participant.platform === 'sociality'
                                      ? `@${participant.username}`
                                      : `${participant.messageCount || 0} messages`
                                    }
                                  </Text>
                                </Box>
                              </Flex>
                              );
                            })}
                            {participants.length === 0 && (
                              <Text fontSize="sm" color={mutedTextColor} textAlign="center" py={4}>
                                No participants found
                              </Text>
                            )}
                          </VStack>
                        </Box>
                      </>
                    )}
                  </VStack>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Alert */}
      <AlertDialog
        isOpen={showDeleteAlert}
        leastDestructiveRef={cancelRef}
        onClose={() => setShowDeleteAlert(false)}
        isCentered
      >
        <AlertDialogOverlay>
          <AlertDialogContent bg={modalBgColor} color={modalTextColor}>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Delete Group
            </AlertDialogHeader>

            <AlertDialogBody>
              Are you sure you want to delete "{selectedConversation.name}"? This action cannot be undone and all messages will be lost.
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={() => setShowDeleteAlert(false)}>
                Cancel
              </Button>
              <Button
                colorScheme="red"
                onClick={handleDeleteGroup}
                ml={3}
                isLoading={isDeleting}
                loadingText="Deleting..."
              >
                Delete
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </>
  );
};

export default GroupSettingsModal;
