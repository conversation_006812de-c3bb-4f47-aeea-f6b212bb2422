describe('User Workflow Stress Tests', () => {
  beforeEach(() => {
    cy.clearAppData();
  });

  describe('Authentication Stress Tests', () => {
    it('should handle rapid form switching', () => {
      cy.visit('/auth');
      
      // Rapidly switch between signup and login forms multiple times
      for (let i = 0; i < 10; i++) {
        cy.contains('Sign up').click();
        cy.get('input[placeholder="Enter your full name"]').should('be.visible');
        cy.contains('Login').click();
        cy.get('input[placeholder="Enter your username"]').should('be.visible');
      }
      
      // Ensure final state is correct
      cy.contains('Login').click();
      cy.get('input[placeholder="Enter your username"]').should('be.visible');
      cy.get('input[placeholder="Enter your password"]').should('be.visible');
    });

    it('should handle invalid input gracefully', () => {
      cy.visit('/auth');
      cy.contains('Sign up').click();
      
      // Test with various invalid inputs (excluding empty strings which Cypress can't type)
      const invalidInputs = [
        'a', // too short
        'a'.repeat(1000), // extremely long
        '<script>alert("xss")</script>', // XSS attempt
        '测试中文', // Unicode characters
        '🚀🎉💻', // Emojis
        'test@', // malformed email
        '<EMAIL>', // double dots
      ];
      
      invalidInputs.forEach((input) => {
        // Clear first, then type the input (but handle cases where field might get disabled)
        cy.get('input[placeholder="Enter your full name"]').clear();
        if (input.length < 100) { // Only type shorter inputs to avoid disabling
          cy.get('input[placeholder="Enter your full name"]').type(input, { delay: 0 });
        }
        
        cy.get('input[placeholder="Choose a username"]').clear();
        if (input.length < 100) {
          cy.get('input[placeholder="Choose a username"]').type(input, { delay: 0 });
        }
        
        cy.get('input[placeholder="Enter your email address"]').clear();
        if (input.length < 100) {
          cy.get('input[placeholder="Enter your email address"]').type(input, { delay: 0 });
        }
        
        cy.get('input[placeholder="Create a password"]').clear();
        if (input.length < 100) {
          cy.get('input[placeholder="Create a password"]').type(input, { delay: 0 });
        }
        
        // Try to submit - should handle gracefully
        cy.get('button').contains('Sign up').click();
        
        // Should still be on auth page (not crash)
        cy.url().should('include', '/auth');
      });
    });

    it('should handle network failures during auth', () => {
      // Intercept auth requests and simulate network failures
      cy.intercept('POST', '**/api/users/signup', { forceNetworkError: true }).as('signupError');
      cy.intercept('POST', '**/api/users/login', { forceNetworkError: true }).as('loginError');
      
      cy.visit('/auth');
      cy.contains('Sign up').click();
      
      // Fill valid data
      cy.get('input[placeholder="Enter your full name"]').type('Test User');
      cy.get('input[placeholder="Choose a username"]').type('testuser');
      cy.get('input[placeholder="Enter your email address"]').type('<EMAIL>');
      cy.get('input[placeholder="Create a password"]').type('password123');
      
      // Attempt signup - should handle network error gracefully
      cy.contains('button', 'Sign up').click();
      
      // Should remain on auth page
      cy.url().should('include', '/auth');
      
      // Try login with network error
      cy.contains('Login').click();
      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();
      
      // Should still be on auth page
      cy.url().should('include', '/auth');
    });
  });

  describe('Profile Setup Stress Tests', () => {
    beforeEach(() => {
      // Set up authenticated user with incomplete profile
      cy.window().then((win) => {
        const tabId = `tab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        win.sessionStorage.setItem('tabId', tabId);
        const userKey = `user-threads-${tabId}`;
        win.localStorage.setItem(userKey, JSON.stringify({
          _id: 'stress-test-user',
          name: 'Stress Test User',
          email: '<EMAIL>',
          username: 'stressuser',
          bio: '',
          profilePic: '',
          isProfileComplete: false
        }));
      });
    });

    it('should handle extremely long bio text', () => {
      cy.visit('/profile-setup');
      cy.url().should('include', '/profile-setup');
      
      // Test with various text lengths
      const longTexts = [
        'A'.repeat(500),   // 500 chars
        'B'.repeat(1000),  // 1000 chars  
        'C'.repeat(5000),  // 5000 chars (very long)
        'Lorem ipsum '.repeat(1000), // Repeated text
        '🎉'.repeat(500),  // Emojis
        '测试中文字符'.repeat(200), // Unicode
      ];
      
      longTexts.forEach((text, index) => {
        cy.get('textarea[placeholder="Tell us about yourself (optional)"]')
          .clear()
          .type(text.substring(0, 1000), { delay: 0 }); // Limit to 1000 chars for performance
        
        // Should handle gracefully
        cy.get('textarea[placeholder="Tell us about yourself (optional)"]').should('be.visible');
      });
    });

    it('should handle rapid form submissions', () => {
      cy.visit('/profile-setup');
      
      // Mock API to handle rapid requests
      let requestCount = 0;
      cy.intercept('POST', '**/api/users/complete-profile', (req) => {
        requestCount++;
        req.reply({
          statusCode: 200,
          body: {
            _id: 'stress-test-user',
            name: 'Stress Test User',
            email: '<EMAIL>',
            username: 'stressuser',
            bio: `Request ${requestCount}`,
            profilePic: '',
            isProfileComplete: true
          },
          delay: Math.random() * 1000 // Random delay to simulate real conditions
        });
      }).as('completeProfile');
      
      // Fill required fields
      cy.get('input[placeholder="Enter your display name"]').type('Test User');
      cy.get('input[placeholder="Choose a unique username"]').type('testuser');
      cy.get('textarea[placeholder="Tell us about yourself (optional)"]').type('Bio text');
      
      // Rapidly click submit button multiple times
      for (let i = 0; i < 5; i++) {
        cy.get('button').contains('Complete Setup').click();
        cy.wait(100); // Small delay between clicks
      }
      
      // Should eventually complete successfully
      cy.url().should('not.include', '/profile-setup', { timeout: 15000 });
    });

    it('should handle concurrent tab sessions', () => {
      // Simulate multiple tabs by setting different tab IDs
      const tabIds = ['tab-1', 'tab-2', 'tab-3'];
      
      tabIds.forEach((tabId, index) => {
        cy.window().then((win) => {
          win.sessionStorage.setItem('tabId', tabId);
          const userKey = `user-threads-${tabId}`;
          win.localStorage.setItem(userKey, JSON.stringify({
            _id: `stress-test-user-${index}`,
            name: `Stress Test User ${index}`,
            email: `stress${index}@example.com`,
            username: `stressuser${index}`,
            bio: '',
            profilePic: '',
            isProfileComplete: false
          }));
        });
      });
      
      // Visit profile setup with the last tab ID
      cy.visit('/profile-setup');
      cy.get('textarea[placeholder="Tell us about yourself (optional)"]').should('be.visible');
    });
  });

  describe('Navigation Stress Tests', () => {
    beforeEach(() => {
      // Set up authenticated user with COMPLETE profile for navigation tests
      cy.window().then((win) => {
        const tabId = `tab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        win.sessionStorage.setItem('tabId', tabId);
        const userKey = `user-threads-${tabId}`;
        win.localStorage.setItem(userKey, JSON.stringify({
          _id: 'stress-nav-user',
          name: 'Stress Nav User',
          email: '<EMAIL>',
          username: 'stressnavuser',
          bio: 'Stress testing navigation',
          profilePic: '',
          isProfileComplete: true
        }));
      });
      
      // Mock APIs with random delays to simulate real conditions - CRITICAL: Mock profile completion first
      cy.intercept('GET', '**/api/users/profile-completion', {
        statusCode: 200,
        body: { isProfileComplete: true },
        delay: 100
      }).as('profileCompletion');
      
      cy.intercept('GET', '**/api/posts/feed', {
        statusCode: 200,
        body: [],
        delay: Math.random() * 300
      });
      cy.intercept('GET', '**/api/users/notifications', {
        statusCode: 200,
        body: [],
        delay: Math.random() * 300
      });
      
      // Mock all other API endpoints
      cy.intercept('GET', '**/api/**', {
        statusCode: 200,
        body: [],
        delay: Math.random() * 200
      });
    });

    it('should handle rapid navigation between pages', () => {
      cy.visit('/');
      cy.wait(2000); // Wait for initial load
      
      const pages = ['/', '/notifications', '/settings'];
      
      // Rapidly navigate between pages multiple times
      for (let round = 0; round < 3; round++) {
        pages.forEach((page) => {
          cy.visit(page);
          cy.wait(500); // Brief wait to allow page to start loading
          cy.url().should('include', page === '/' ? '' : page);
        });
      }
    });

    it('should handle browser back/forward stress', () => {
      cy.visit('/');
      cy.wait(2000);
      
      // Navigate to different pages to build history
      cy.visit('/notifications');
      cy.wait(1000);
      cy.visit('/settings');
      cy.wait(1000);
      
      // Rapidly use browser back/forward
      for (let i = 0; i < 5; i++) {
        cy.go('back');
        cy.wait(200);
        cy.go('forward');
        cy.wait(200);
      }
      
      // Should handle gracefully
      cy.url().should('include', '/settings');
    });

    it('should handle API failures during navigation', () => {
      // Simulate intermittent API failures
      let failureCount = 0;
      cy.intercept('GET', '**/api/**', (req) => {
        failureCount++;
        if (failureCount % 3 === 0) {
          // Fail every 3rd request
          req.reply({ forceNetworkError: true });
        } else {
          req.reply({ statusCode: 200, body: [] });
        }
      });
      
      cy.visit('/');
      cy.wait(2000);
      
      // Navigate despite API failures
      cy.visit('/notifications');
      cy.wait(1000);
      cy.visit('/settings');
      cy.wait(1000);
      
      // Should still be able to navigate
      cy.url().should('include', '/settings');
    });
  });

  describe('Memory and Performance Stress Tests', () => {
    it('should handle large amounts of localStorage data', () => {
      cy.window().then((win) => {
        // Create large amounts of localStorage data
        for (let i = 0; i < 100; i++) {
          const tabId = `tab-stress-${i}`;
          const userKey = `user-threads-${tabId}`;
          win.localStorage.setItem(userKey, JSON.stringify({
            _id: `user-${i}`,
            name: `User ${i}`,
            email: `user${i}@example.com`,
            username: `user${i}`,
            bio: 'A'.repeat(1000), // Large bio
            profilePic: '',
            isProfileComplete: true,
            extraData: 'B'.repeat(1000) // Extra large data
          }));
        }
        
        // Set current user
        const currentTabId = `tab-current-${Date.now()}`;
        win.sessionStorage.setItem('tabId', currentTabId);
        const currentUserKey = `user-threads-${currentTabId}`;
        win.localStorage.setItem(currentUserKey, JSON.stringify({
          _id: 'current-user',
          name: 'Current User',
          email: '<EMAIL>',
          username: 'currentuser',
          bio: 'Current user bio',
          profilePic: '',
          isProfileComplete: true
        }));
      });
      
      // Mock API
      cy.intercept('GET', '**/api/users/profile-completion', { 
        statusCode: 200, 
        body: { isProfileComplete: true } 
      });
      cy.intercept('GET', '**/api/**', { statusCode: 200, body: [] });
      
      // Should still work with large localStorage
      cy.visit('/');
      cy.wait(3000);
      cy.url().should('not.include', '/auth');
    });

    it('should handle rapid page reloads', () => {
      // Set up user
      cy.window().then((win) => {
        const tabId = `tab-${Date.now()}`;
        win.sessionStorage.setItem('tabId', tabId);
        const userKey = `user-threads-${tabId}`;
        win.localStorage.setItem(userKey, JSON.stringify({
          _id: 'reload-user',
          name: 'Reload User',
          email: '<EMAIL>',
          username: 'reloaduser',
          bio: 'Testing reloads',
          profilePic: '',
          isProfileComplete: true
        }));
      });
      
      cy.intercept('GET', '**/api/users/profile-completion', { 
        statusCode: 200, 
        body: { isProfileComplete: true } 
      });
      cy.intercept('GET', '**/api/**', { statusCode: 200, body: [] });
      
      // Rapid reloads
      for (let i = 0; i < 5; i++) {
        cy.visit('/');
        cy.wait(1000);
        cy.reload();
        cy.wait(1000);
      }
      
      // Should still work
      cy.url().should('not.include', '/auth');
    });
  });

  describe('Edge Case Stress Tests', () => {
    it('should handle corrupted localStorage data', () => {
      // Handle uncaught exceptions that occur when parsing corrupted data
      cy.on('uncaught:exception', (err, runnable) => {
        // We expect JSON parsing errors when testing corrupted data
        if (err.message.includes('JSON') || err.message.includes('property name')) {
          return false; // Prevent Cypress from failing
        }
        return true;
      });
      
      cy.window().then((win) => {
        const tabId = `tab-corrupted-${Date.now()}`;
        win.sessionStorage.setItem('tabId', tabId);
        const userKey = `user-threads-${tabId}`;
        
        // Set corrupted JSON data
        win.localStorage.setItem(userKey, '{invalid json}');
      });
      
      // Should handle gracefully and redirect to auth
      cy.visit('/');
      cy.wait(3000);
      cy.url().should('include', '/auth');
    });

    it('should handle missing sessionStorage tabId', () => {
      cy.window().then((win) => {
        // Clear sessionStorage but set localStorage with some key
        win.sessionStorage.clear();
        win.localStorage.setItem('user-threads-unknown', JSON.stringify({
          _id: 'orphaned-user',
          name: 'Orphaned User',
          email: '<EMAIL>',
          username: 'orphaneduser',
          bio: 'Orphaned data',
          profilePic: '',
          isProfileComplete: true
        }));
      });
      
      // Should handle gracefully
      cy.visit('/');
      cy.wait(3000);
      cy.url().should('include', '/auth');
    });

    it('should handle extremely slow API responses', () => {
      // Set up user
      cy.window().then((win) => {
        const tabId = `tab-slow-${Date.now()}`;
        win.sessionStorage.setItem('tabId', tabId);
        const userKey = `user-threads-${tabId}`;
        win.localStorage.setItem(userKey, JSON.stringify({
          _id: 'slow-user',
          name: 'Slow User',
          email: '<EMAIL>',
          username: 'slowuser',
          bio: 'Testing slow APIs',
          profilePic: '',
          isProfileComplete: true
        }));
      });
      
      // Mock very slow API responses
      cy.intercept('GET', '**/api/users/profile-completion', (req) => {
        req.reply({
          statusCode: 200,
          body: { isProfileComplete: true },
          delay: 5000 // 5 second delay
        });
      });
      cy.intercept('GET', '**/api/**', (req) => {
        req.reply({
          statusCode: 200,
          body: [],
          delay: 3000 // 3 second delay
        });
      });
      
      cy.visit('/');
      
      // Should eventually load (with extended timeout)
      cy.url().should('not.include', '/auth', { timeout: 20000 });
    });

    it('should handle authentication token expiration scenarios', () => {
      // Set up user with potentially expired auth
      cy.window().then((win) => {
        const tabId = `tab-expired-${Date.now()}`;
        win.sessionStorage.setItem('tabId', tabId);
        const userKey = `user-threads-${tabId}`;
        win.localStorage.setItem(userKey, JSON.stringify({
          _id: 'expired-user',
          name: 'Expired User',
          email: '<EMAIL>',
          username: 'expireduser',
          bio: 'Testing expired auth',
          profilePic: '',
          isProfileComplete: true,
          sessionPath: 'expired-session-path'
        }));
      });
      
      // Mock 401 responses to simulate expired tokens - be more specific
      cy.intercept('GET', '**/api/users/profile-completion', { statusCode: 401, body: { error: 'Unauthorized' } });
      cy.intercept('GET', '**/api/posts/**', { statusCode: 401, body: { error: 'Unauthorized' } });
      cy.intercept('GET', '**/api/users/**', { statusCode: 401, body: { error: 'Unauthorized' } });
      
      cy.visit('/');
      cy.wait(2000); // Give time for auth checks
      
      // Should redirect to auth when tokens are expired
      cy.url().should('include', '/auth', { timeout: 15000 });
    });
  });
});
