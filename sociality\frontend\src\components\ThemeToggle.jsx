import {
  Box,
  Flex,
  Text,
  Switch,
  useColorModeValue
} from "@chakra-ui/react";
import { Sun, Moon } from "phosphor-react";
import useTheme from "../hooks/useTheme";

const ThemeToggle = () => {
  const { theme, toggleTheme, isDark } = useTheme();
  
  // Theme-aware colors
  const bgColor = useColorModeValue("rgba(255, 255, 255, 0.25)", "#1a1a1a");
  const borderColor = useColorModeValue("rgba(255, 255, 255, 0.2)", "rgba(255, 255, 255, 0.08)");
  const textColor = useColorModeValue("gray.800", "white");
  const descriptionColor = useColorModeValue("gray.600", "gray.400");
  const iconColor = useColorModeValue("#f6ad55", "#ffd700");
  const glassProps = useColorModeValue({
    backdropFilter: "blur(10px)",
    border: "1px solid rgba(255, 255, 255, 0.2)",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)"
  }, {});

  return (
    <Box
      width="100%"
      p={6}
      borderWidth="1px"
      borderRadius="xl"
      borderColor={borderColor}
      bg={bgColor}
      boxShadow={useColorModeValue("0 4px 12px rgba(0, 0, 0, 0.1)", "0 4px 12px rgba(0, 0, 0, 0.2)")}
      className="threads-post-card glass-message-bubble"
      {...glassProps}
    >
      <Text fontSize="xl" fontWeight="bold" mb={5} color={textColor}>
        Appearance
      </Text>

      <Flex justify="space-between" align="center">
        <Box>
          <Flex align="center" mb={1}>
            {isDark ? (
              <Moon
                size={20}
                color={iconColor}
                weight="fill"
                style={{ marginRight: '8px' }}
              />
            ) : (
              <Sun
                size={20}
                color={iconColor}
                weight="fill"
                style={{ marginRight: '8px' }}
              />
            )}
            <Text fontWeight="bold" color={textColor}>
              {isDark ? "Dark Mode" : "Light Mode"}
            </Text>
          </Flex>
          <Text fontSize="sm" color={descriptionColor}>
            {isDark 
              ? "Switch to light mode for a brighter interface" 
              : "Switch to dark mode for a darker interface"
            }
          </Text>
        </Box>
        
        <Switch
          size="lg"
          isChecked={isDark}
          onChange={toggleTheme}
          colorScheme="green"
          sx={{
            '& .chakra-switch__track': {
              bg: isDark ? '#00CC85' : 'gray.300',
              _checked: {
                bg: '#00CC85',
              },
            },
            '& .chakra-switch__thumb': {
              bg: 'white',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
            },
          }}
        />
      </Flex>
    </Box>
  );
};

export default ThemeToggle;
