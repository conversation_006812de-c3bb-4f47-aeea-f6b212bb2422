import LoginCard from '../../src/components/LoginCard'

describe('LoginCard Component', () => {
  beforeEach(() => {
    // Mock the authScreenAtom to show login screen
    cy.window().then((win) => {
      // Clear any existing user data
      win.localStorage.clear();
      win.sessionStorage.clear();
    });
  });

  it('should render login form', () => {
    cy.mount(<LoginCard />);
    
    cy.contains('Welcome back').should('be.visible');
    cy.get('input[placeholder="Enter your username"]').should('be.visible');
    cy.get('input[placeholder="Enter your password"]').should('be.visible');
    cy.contains('button', 'Login').should('be.visible');
    cy.contains('Continue with Google').should('be.visible');
  });

  it('should toggle password visibility', () => {
    cy.mount(<LoginCard />);
    
    const passwordInput = cy.get('input[placeholder="Enter your password"]');
    
    // Initially password should be hidden
    passwordInput.should('have.attr', 'type', 'password');
    
    // Click the eye icon to show password
    cy.get('button').contains('svg').click();
    passwordInput.should('have.attr', 'type', 'text');
    
    // Click again to hide password
    cy.get('button').contains('svg').click();
    passwordInput.should('have.attr', 'type', 'password');
  });

  it('should update input values when typing', () => {
    cy.mount(<LoginCard />);
    
    const usernameInput = cy.get('input[placeholder="Enter your username"]');
    const passwordInput = cy.get('input[placeholder="Enter your password"]');
    
    usernameInput.type('testuser');
    usernameInput.should('have.value', 'testuser');
    
    passwordInput.type('password123');
    passwordInput.should('have.value', 'password123');
  });

  it('should show loading state when submitting', () => {
    // Mock the login API to delay response
    cy.intercept('POST', '/api/users/login', (req) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            statusCode: 200,
            body: {
              _id: '1',
              name: 'Test User',
              email: '<EMAIL>',
              username: 'testuser',
              bio: 'Test bio',
              profilePic: ''
            }
          });
        }, 1000);
      });
    }).as('loginRequest');

    cy.mount(<LoginCard />);
    
    cy.get('input[placeholder="Enter your username"]').type('testuser');
    cy.get('input[placeholder="Enter your password"]').type('password123');
    cy.contains('button', 'Login').click();
    
    // Should show loading state
    cy.contains('Logging in').should('be.visible');
    cy.get('button').should('be.disabled');
  });

  it('should handle login error', () => {
    // Mock login API to return error
    cy.intercept('POST', '/api/users/login', {
      statusCode: 400,
      body: { error: 'Invalid username or password' }
    }).as('loginError');

    cy.mount(<LoginCard />);
    
    cy.get('input[placeholder="Enter your username"]').type('wronguser');
    cy.get('input[placeholder="Enter your password"]').type('wrongpassword');
    cy.contains('button', 'Login').click();
    
    cy.wait('@loginError');
    // Should show error message (adjust based on your toast implementation)
    cy.contains('Invalid username or password').should('be.visible');
  });

  it('should have proper accessibility attributes', () => {
    cy.mount(<LoginCard />);
    
    // Check for proper form labels
    cy.get('label').contains('Username').should('be.visible');
    cy.get('label').contains('Password').should('be.visible');
    
    // Check for required attributes
    cy.get('input[placeholder="Enter your username"]').should('have.attr', 'required');
    cy.get('input[placeholder="Enter your password"]').should('have.attr', 'required');
  });

  it('should switch to signup form when link is clicked', () => {
    cy.mount(<LoginCard />);
    
    cy.contains('Sign up').click();
    // This would trigger the authScreenAtom change
    // In a real test, you'd verify the state change or navigation
  });

  it('should handle Google OAuth button click', () => {
    cy.mount(<LoginCard />);
    
    // Mock window.open for OAuth popup
    cy.window().then((win) => {
      cy.stub(win, 'open').as('windowOpen');
    });
    
    cy.contains('Continue with Google').click();
    
    // Should attempt to open OAuth popup
    cy.get('@windowOpen').should('have.been.called');
  });

  it('should be responsive', () => {
    // Test mobile view
    cy.viewport(375, 667);
    cy.mount(<LoginCard />);
    cy.contains('Welcome back').should('be.visible');
    
    // Test tablet view
    cy.viewport(768, 1024);
    cy.contains('Welcome back').should('be.visible');
    
    // Test desktop view
    cy.viewport(1280, 720);
    cy.contains('Welcome back').should('be.visible');
  });
});
