describe('Authentication Flow', () => {
  beforeEach(() => {
    // Clear any existing user data
    cy.clearAppData();
    cy.visit('/auth');
  });

  describe('Login', () => {
    it('should display login form by default', () => {
      cy.contains('Login to Sociality').should('be.visible');
      cy.get('input[placeholder="Enter your username"]').should('be.visible');
      cy.get('input[placeholder="Enter your password"]').should('be.visible');
      cy.contains('button', 'Login').should('be.visible');
    });

    it('should switch to signup form', () => {
      cy.contains('Sign up').click();
      cy.contains('Create Sociality Account').should('be.visible');
      cy.get('input[placeholder="Enter your full name"]').should('be.visible');
      cy.get('input[placeholder="Choose a username"]').should('be.visible');
      cy.get('input[placeholder="Enter your email address"]').should('be.visible');
      cy.get('input[placeholder="Create a password"]').should('be.visible');
    });

    it('should show validation errors for empty fields', () => {
      cy.contains('button', 'Login').click();
      // The form should not submit and stay on the same page
      cy.url().should('include', '/auth');
    });

    it('should show error for invalid credentials', () => {
      cy.intercept('POST', '/api/users/login', {
        statusCode: 400,
        body: { error: 'Invalid username or password' }
      }).as('loginError');

      cy.get('input[placeholder="Enter your username"]').type('invaliduser');
      cy.get('input[placeholder="Enter your password"]').type('wrongpassword');
      cy.contains('button', 'Login').click();
      
      cy.wait('@loginError');
      // For now, just verify the network call was made and form doesn't redirect
      cy.url().should('include', '/auth');
    });

    it('should toggle password visibility', () => {
      // Type something in the password field first so we can verify the change
      cy.get('input[placeholder="Enter your password"]').type('testpass');
      
      // Initially password should be hidden
      cy.get('input[placeholder="Enter your password"]').should('have.attr', 'type', 'password');
      
      // Look for the eye icon button - it should be adjacent to the password input
      cy.get('input[placeholder="Enter your password"]')
        .siblings('div') // Look for sibling div (InputRightElement)
        .find('button')
        .click();
        
      // Verify password is now visible
      cy.get('input[placeholder="Enter your password"]').should('have.attr', 'type', 'text');
      
      // Click again to hide password
      cy.get('input[placeholder="Enter your password"]')
        .siblings('div')
        .find('button')
        .click();
        
      cy.get('input[placeholder="Enter your password"]').should('have.attr', 'type', 'password');
    });

    it('should login with valid credentials', () => {
      // Mock the login API call
      cy.intercept('POST', '/api/users/login', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          username: 'testuser',
          bio: 'Test bio',
          profilePic: '',
          isProfileComplete: true
        }
      }).as('loginRequest');

      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();

      cy.wait('@loginRequest');
      cy.url().should('not.include', '/auth');
      cy.url().should('eq', Cypress.config().baseUrl + '/');
      
      // Verify user is logged in by checking localStorage - use a more flexible approach
      cy.window().then((win) => {
        // Check for any user data in localStorage
        const localStorage = win.localStorage;
        let userFound = false;
        
        // Check for tab-specific user key or general user key
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.startsWith('user-threads') || key === 'user-threads')) {
            const userData = localStorage.getItem(key);
            if (userData) {
              userFound = true;
              break;
            }
          }
        }
        
        expect(userFound).to.be.true;
      });
    });

    it('should handle server errors gracefully', () => {
      cy.intercept('POST', '/api/users/login', {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('loginError');

      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();

      cy.wait('@loginError');
      // Verify form doesn't redirect on error
      cy.url().should('include', '/auth');
    });

    it('should handle network errors', () => {
      cy.intercept('POST', '/api/users/login', { forceNetworkError: true }).as('networkError');

      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();

      cy.wait('@networkError');
      // Verify form doesn't redirect on network error
      cy.url().should('include', '/auth');
    });
  });

  describe('Signup', () => {
    beforeEach(() => {
      cy.contains('Sign up').click();
    });

    it('should display signup form', () => {
      cy.contains('Create Sociality Account').should('be.visible');
      cy.get('input[placeholder="Enter your full name"]').should('be.visible');
      cy.get('input[placeholder="Choose a username"]').should('be.visible');
      cy.get('input[placeholder="Enter your email address"]').should('be.visible');
      cy.get('input[placeholder="Create a password"]').should('be.visible');
      cy.contains('button', 'Sign up').should('be.visible');
    });

    it('should switch back to login form', () => {
      cy.contains('Login').click();
      cy.contains('Login to Sociality').should('be.visible');
    });

    it('should signup with valid data', () => {
      // Mock the signup API call
      cy.intercept('POST', '/api/users/signup', {
        statusCode: 200,
        body: {
          _id: '1',
          name: 'New User',
          email: '<EMAIL>',
          username: 'newuser',
          bio: '',
          profilePic: ''
        }
      }).as('signupRequest');

      cy.get('input[placeholder="Enter your full name"]').type('New User');
      cy.get('input[placeholder="Choose a username"]').type('newuser');
      cy.get('input[placeholder="Enter your email address"]').type('<EMAIL>');
      cy.get('input[placeholder="Create a password"]').type('password123');
      cy.contains('button', 'Sign up').click();

      cy.wait('@signupRequest');
      cy.url().should('not.include', '/auth');
    });

    it('should show error for duplicate username', () => {
      cy.intercept('POST', '/api/users/signup', {
        statusCode: 400,
        body: { error: 'Username already exists' }
      }).as('signupError');

      cy.get('input[placeholder="Enter your full name"]').type('Test User');
      cy.get('input[placeholder="Choose a username"]').type('existinguser');
      cy.get('input[placeholder="Enter your email address"]').type('<EMAIL>');
      cy.get('input[placeholder="Create a password"]').type('password123');
      cy.contains('button', 'Sign up').click();

      cy.wait('@signupError');
      // Verify form doesn't redirect on error
      cy.url().should('include', '/auth');
    });
  });

  describe('Google OAuth', () => {
    it('should display Google login button', () => {
      cy.contains('Continue with Google').should('be.visible');
    });

    it('should display Google signup button in signup form', () => {
      cy.contains('Sign up').click();
      cy.contains('Sign up with Google').should('be.visible');
    });

    // Note: Testing actual OAuth flow requires special setup
    // This is a placeholder for OAuth testing
    it('should handle Google OAuth flow', () => {
      // Mock OAuth success by setting localStorage directly
      cy.window().then((win) => {
        const tabId = win.sessionStorage.getItem('tabId') || '1';
        const userKey = `user-threads-${tabId}`;
        win.localStorage.setItem(userKey, JSON.stringify({
          _id: '1',
          name: 'Google User',
          email: '<EMAIL>',
          username: 'googleuser',
          isGoogleUser: true,
          isProfileComplete: true
        }));
      });

      cy.reload();
      cy.url().should('not.include', '/auth');
    });
  });
});
