# File Sharing Troubleshooting Guide

## Overview
This guide helps troubleshoot "File temporarily unavailable" and "File could not be downloaded" errors when sending files from Sociality to Telegram and Discord.

## Common Issues and Solutions

### 1. Cloudinary Configuration Issues

**Problem**: Files upload but are not accessible externally
**Symptoms**: 
- Files upload successfully to Cloudinary
- "File temporarily unavailable" messages in Telegram/Discord
- 403 Forbidden or 404 Not Found errors when accessing URLs

**Solutions**:
- Verify Cloudinary credentials in environment variables
- Ensure `access_mode: 'public'` is set during upload
- Check Cloudinary account settings for public access restrictions

### 2. Network Timeout Issues

**Problem**: File downloads timeout during transmission
**Symptoms**:
- Large files fail to send
- Timeout errors in logs
- Intermittent failures

**Solutions**:
- Increased timeout from 30s to 60s in axios requests
- Added retry logic with exponential backoff
- Implemented fallback to direct URL sharing

### 3. File Format/Size Issues

**Problem**: Certain file types or sizes are rejected
**Symptoms**:
- Specific file types always fail
- Large files consistently fail
- Format-related errors in logs

**Solutions**:
- Check platform-specific file size limits (Telegram: 50MB, Discord: 8MB for free users)
- Verify file format support on target platforms
- Implement file compression for large files

### 4. URL Access Issues

**Problem**: Cloudinary URLs are not accessible from external services
**Symptoms**:
- URLs work in browser but fail in bots
- CORS or authentication errors
- User-Agent restrictions

**Solutions**:
- Added proper User-Agent headers in requests
- Implemented URL verification before sending
- Added fallback to direct URL sharing

## Diagnostic Steps

### Step 1: Test File Upload
```bash
cd sociality/backend
node scripts/testFileSharing.js
```

### Step 2: Check Cloudinary Settings
1. Log into Cloudinary dashboard
2. Go to Settings > Security
3. Verify "Restrict media access" is disabled for public files
4. Check "Allowed fetch domains" settings

### Step 3: Test File Accessibility
Use the verification endpoint:
```bash
curl -X POST http://localhost:5000/api/cross-platform/files/verify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"url": "YOUR_CLOUDINARY_URL"}'
```

### Step 4: Check Bot Permissions
- **Telegram**: Ensure bot has permission to send documents in the chat
- **Discord**: Verify bot has "Attach Files" permission in the channel

## Enhanced Error Handling

The updated implementation includes:

1. **Multiple Fallback Methods**:
   - Direct URL sending (fastest)
   - File download and re-upload (most reliable)
   - Link sharing as text (always works)

2. **Better Error Logging**:
   - Detailed error messages with context
   - File metadata logging
   - Accessibility verification results

3. **Improved Timeout Handling**:
   - Increased timeouts for large files
   - Proper error categorization
   - Graceful degradation

## Monitoring and Alerts

### Key Metrics to Monitor
- File upload success rate
- File accessibility verification rate
- Cross-platform delivery success rate
- Average file processing time

### Log Patterns to Watch
- `❌ Failed to send file:` - File transmission failures
- `📥 Downloading file from Cloudinary:` - Download attempts
- `✅ File accessibility verified:` - Successful verifications
- `⚠️ File accessibility check failed:` - Verification failures

## Environment Variables

Ensure these are properly set:
```env
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
```

## Platform-Specific Considerations

### Telegram
- Maximum file size: 50MB
- Supports most file formats
- Bot API handles file downloads automatically
- Fallback: Send as clickable link

### Discord
- Maximum file size: 8MB (free), 50MB (Nitro)
- Supports most file formats
- Requires file download and re-upload as attachment
- Fallback: Send as embed with download link

## Testing Checklist

- [ ] Environment variables configured
- [ ] Cloudinary account accessible
- [ ] Test file upload script passes
- [ ] File verification endpoint works
- [ ] Bot permissions configured
- [ ] Cross-platform message sending works
- [ ] Fallback mechanisms tested
- [ ] Error logging is comprehensive

## Support

If issues persist after following this guide:
1. Check the application logs for detailed error messages
2. Verify network connectivity to Cloudinary
3. Test with different file types and sizes
4. Contact platform support if bot-specific issues occur
