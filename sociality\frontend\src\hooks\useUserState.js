import { useState, useCallback, useEffect } from 'react';
import { fetchWithSession } from '../utils/api';

/**
 * Centralized user state management hook
 * Handles user data updates and ensures consistency across components
 */
const useUserState = (initialUser = null) => {
    const [user, setUser] = useState(initialUser);
    const [isUpdating, setIsUpdating] = useState(false);

    // Update user state when initialUser changes
    useEffect(() => {
        if (initialUser) {
            setUser(initialUser);
        }
    }, [initialUser]);

    // Function to refresh user data from server
    const refreshUserData = useCallback(async (username) => {
        if (!username) return null;
        
        try {
            setIsUpdating(true);
            const res = await fetchWithSession(`/api/users/profile/${username}`);
            if (res.ok) {
                const userData = await res.json();
                setUser(userData);
                return userData;
            }
        } catch (error) {
            console.error('Error refreshing user data:', error);
        } finally {
            setIsUpdating(false);
        }
        return null;
    }, []);

    // Function to update user data locally and optionally refresh from server
    const updateUser = useCallback((updatedUserData, shouldRefresh = false) => {
        if (updatedUserData) {
            console.log('Updating user state:', {
                followers: updatedUserData.followers?.length || 0,
                following: updatedUserData.following?.length || 0
            });
            
            setUser(updatedUserData);
            
            // Optionally refresh from server to ensure consistency
            if (shouldRefresh && updatedUserData.username) {
                setTimeout(() => {
                    refreshUserData(updatedUserData.username);
                }, 100); // Small delay to allow backend to process
            }
        }
    }, [refreshUserData]);

    // Function to handle follow/unfollow updates
    const handleFollowUpdate = useCallback((targetUserId, isFollowing, currentUserData) => {
        if (!user || !currentUserData) return;

        // Update the current user's following list
        let updatedFollowing = Array.isArray(user.following) ? [...user.following] : [];
        
        if (isFollowing) {
            // Add to following if not already there
            const isAlreadyFollowing = updatedFollowing.some(followingUser => {
                const followingId = typeof followingUser === 'string' ? followingUser : followingUser._id;
                return followingId === targetUserId;
            });
            
            if (!isAlreadyFollowing) {
                updatedFollowing.push(targetUserId);
            }
        } else {
            // Remove from following
            updatedFollowing = updatedFollowing.filter(followingUser => {
                const followingId = typeof followingUser === 'string' ? followingUser : followingUser._id;
                return followingId !== targetUserId;
            });
        }

        const updatedUser = {
            ...user,
            following: updatedFollowing
        };

        setUser(updatedUser);
        
        // Update localStorage to persist changes
        localStorage.setItem('user-threads', JSON.stringify(updatedUser));
        
        return updatedUser;
    }, [user]);

    return {
        user,
        isUpdating,
        updateUser,
        refreshUserData,
        handleFollowUpdate
    };
};

export default useUserState;
