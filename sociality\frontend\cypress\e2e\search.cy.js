describe('Search Functionality', () => {
  beforeEach(() => {
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    cy.visit('/search');
  });

  describe('Search Interface', () => {
    it('should display search input and filters', () => {
      cy.get('[data-testid="search-input"]').should('be.visible');
      cy.get('[data-testid="search-filters"]').should('be.visible');
      
      // Check filter options
      cy.contains('All').should('be.visible');
      cy.contains('Users').should('be.visible');
      cy.contains('Posts').should('be.visible');
      cy.contains('Tags').should('be.visible');
    });

    it('should show search suggestions on focus', () => {
      cy.intercept('GET', '/api/search/suggestions', {
        statusCode: 200,
        body: {
          trending: ['#technology', '#travel', '#food'],
          recent: ['react', 'javascript', 'web development']
        }
      }).as('getSearchSuggestions');

      cy.get('[data-testid="search-input"]').focus();
      cy.wait('@getSearchSuggestions');
      
      cy.contains('Trending').should('be.visible');
      cy.contains('#technology').should('be.visible');
      cy.contains('Recent').should('be.visible');
      cy.contains('react').should('be.visible');
    });

    it('should show empty state initially', () => {
      cy.contains('Search for users, posts, and more').should('be.visible');
      cy.get('[data-testid="search-icon"]').should('be.visible');
    });
  });

  describe('User Search', () => {
    beforeEach(() => {
      cy.get('[data-testid="filter-users"]').click();
    });

    it('should search for users', () => {
      cy.intercept('GET', '/api/search/users*', {
        statusCode: 200,
        body: [
          {
            _id: '2',
            username: 'john_doe',
            name: 'John Doe',
            bio: 'Software developer',
            profilePic: 'https://via.placeholder.com/150',
            followers: ['1'],
            isFollowing: true
          },
          {
            _id: '3',
            username: 'jane_smith',
            name: 'Jane Smith',
            bio: 'UI/UX Designer',
            profilePic: '',
            followers: [],
            isFollowing: false
          }
        ]
      }).as('searchUsers');

      cy.get('[data-testid="search-input"]').type('john');
      cy.wait('@searchUsers');
      
      // Should display user results
      cy.contains('John Doe').should('be.visible');
      cy.contains('@john_doe').should('be.visible');
      cy.contains('Software developer').should('be.visible');
      
      cy.contains('Jane Smith').should('be.visible');
      cy.contains('@jane_smith').should('be.visible');
      cy.contains('UI/UX Designer').should('be.visible');
    });

    it('should show follow/unfollow buttons for users', () => {
      cy.intercept('GET', '/api/search/users*', {
        statusCode: 200,
        body: [
          {
            _id: '2',
            username: 'john_doe',
            name: 'John Doe',
            bio: 'Software developer',
            profilePic: '',
            followers: [],
            isFollowing: false
          }
        ]
      }).as('searchUsers');

      cy.get('[data-testid="search-input"]').type('john');
      cy.wait('@searchUsers');
      
      cy.contains('Follow').should('be.visible');
    });

    it('should follow user from search results', () => {
      cy.intercept('GET', '/api/search/users*', {
        statusCode: 200,
        body: [
          {
            _id: '2',
            username: 'john_doe',
            name: 'John Doe',
            bio: 'Software developer',
            profilePic: '',
            followers: [],
            isFollowing: false
          }
        ]
      }).as('searchUsers');

      cy.intercept('POST', '/api/users/follow/2', {
        statusCode: 200,
        body: { message: 'User followed successfully' }
      }).as('followUser');

      cy.get('[data-testid="search-input"]').type('john');
      cy.wait('@searchUsers');
      
      cy.contains('Follow').click();
      cy.wait('@followUser');
      
      cy.contains('Following').should('be.visible');
    });

    it('should navigate to user profile when clicked', () => {
      cy.intercept('GET', '/api/search/users*', {
        statusCode: 200,
        body: [
          {
            _id: '2',
            username: 'john_doe',
            name: 'John Doe',
            bio: 'Software developer',
            profilePic: '',
            followers: [],
            isFollowing: false
          }
        ]
      }).as('searchUsers');

      cy.get('[data-testid="search-input"]').type('john');
      cy.wait('@searchUsers');
      
      cy.contains('John Doe').click();
      cy.url().should('include', '/john_doe');
    });

    it('should show no results message when no users found', () => {
      cy.intercept('GET', '/api/search/users*', {
        statusCode: 200,
        body: []
      }).as('searchUsersEmpty');

      cy.get('[data-testid="search-input"]').type('nonexistentuser');
      cy.wait('@searchUsersEmpty');
      
      cy.contains('No users found').should('be.visible');
      cy.contains('Try searching with different keywords').should('be.visible');
    });
  });

  describe('Post Search', () => {
    beforeEach(() => {
      cy.get('[data-testid="filter-posts"]').click();
    });

    it('should search for posts', () => {
      cy.intercept('GET', '/api/search/posts*', {
        statusCode: 200,
        body: [
          {
            _id: 'post1',
            text: 'Just finished building an amazing React app!',
            postedBy: {
              _id: '2',
              username: 'john_doe',
              name: 'John Doe',
              profilePic: ''
            },
            createdAt: new Date().toISOString(),
            likes: ['1'],
            replies: []
          },
          {
            _id: 'post2',
            text: 'React is such a powerful library for building UIs',
            postedBy: {
              _id: '3',
              username: 'jane_smith',
              name: 'Jane Smith',
              profilePic: ''
            },
            createdAt: new Date().toISOString(),
            likes: [],
            replies: []
          }
        ]
      }).as('searchPosts');

      cy.get('[data-testid="search-input"]').type('react');
      cy.wait('@searchPosts');
      
      // Should display post results
      cy.contains('Just finished building an amazing React app!').should('be.visible');
      cy.contains('John Doe').should('be.visible');
      cy.contains('@john_doe').should('be.visible');
      
      cy.contains('React is such a powerful library').should('be.visible');
      cy.contains('Jane Smith').should('be.visible');
      cy.contains('@jane_smith').should('be.visible');
    });

    it('should highlight search terms in post results', () => {
      cy.intercept('GET', '/api/search/posts*', {
        statusCode: 200,
        body: [
          {
            _id: 'post1',
            text: 'Just finished building an amazing React app!',
            postedBy: {
              _id: '2',
              username: 'john_doe',
              name: 'John Doe',
              profilePic: ''
            },
            createdAt: new Date().toISOString(),
            likes: [],
            replies: []
          }
        ]
      }).as('searchPosts');

      cy.get('[data-testid="search-input"]').type('react');
      cy.wait('@searchPosts');
      
      // Should highlight the search term
      cy.get('[data-testid="highlighted-text"]').should('contain', 'React');
    });

    it('should interact with posts in search results', () => {
      cy.intercept('GET', '/api/search/posts*', {
        statusCode: 200,
        body: [
          {
            _id: 'post1',
            text: 'Just finished building an amazing React app!',
            postedBy: {
              _id: '2',
              username: 'john_doe',
              name: 'John Doe',
              profilePic: ''
            },
            createdAt: new Date().toISOString(),
            likes: [],
            replies: []
          }
        ]
      }).as('searchPosts');

      cy.intercept('PUT', '/api/posts/like/post1', {
        statusCode: 200,
        body: { message: 'Post liked successfully' }
      }).as('likePost');

      cy.get('[data-testid="search-input"]').type('react');
      cy.wait('@searchPosts');
      
      // Like the post
      cy.get('[data-testid="like-button"]').first().click();
      cy.wait('@likePost');
    });

    it('should navigate to post detail from search results', () => {
      cy.intercept('GET', '/api/search/posts*', {
        statusCode: 200,
        body: [
          {
            _id: 'post1',
            text: 'Just finished building an amazing React app!',
            postedBy: {
              _id: '2',
              username: 'john_doe',
              name: 'John Doe',
              profilePic: ''
            },
            createdAt: new Date().toISOString(),
            likes: [],
            replies: []
          }
        ]
      }).as('searchPosts');

      cy.get('[data-testid="search-input"]').type('react');
      cy.wait('@searchPosts');
      
      cy.contains('Just finished building an amazing React app!').click();
      cy.url().should('include', '/john_doe/post/post1');
    });
  });

  describe('Hashtag Search', () => {
    beforeEach(() => {
      cy.get('[data-testid="filter-tags"]').click();
    });

    it('should search for hashtags', () => {
      cy.intercept('GET', '/api/search/hashtags*', {
        statusCode: 200,
        body: [
          {
            tag: 'technology',
            count: 1250,
            trending: true
          },
          {
            tag: 'javascript',
            count: 890,
            trending: false
          },
          {
            tag: 'webdev',
            count: 567,
            trending: true
          }
        ]
      }).as('searchHashtags');

      cy.get('[data-testid="search-input"]').type('tech');
      cy.wait('@searchHashtags');
      
      // Should display hashtag results
      cy.contains('#technology').should('be.visible');
      cy.contains('1,250 posts').should('be.visible');
      cy.contains('Trending').should('be.visible');
      
      cy.contains('#javascript').should('be.visible');
      cy.contains('890 posts').should('be.visible');
      
      cy.contains('#webdev').should('be.visible');
      cy.contains('567 posts').should('be.visible');
    });

    it('should follow hashtag', () => {
      cy.intercept('GET', '/api/search/hashtags*', {
        statusCode: 200,
        body: [
          {
            tag: 'technology',
            count: 1250,
            trending: true,
            isFollowing: false
          }
        ]
      }).as('searchHashtags');

      cy.intercept('POST', '/api/hashtags/follow', {
        statusCode: 200,
        body: { message: 'Hashtag followed successfully' }
      }).as('followHashtag');

      cy.get('[data-testid="search-input"]').type('tech');
      cy.wait('@searchHashtags');
      
      cy.get('[data-testid="follow-hashtag"]').click();
      cy.wait('@followHashtag');
      
      cy.contains('Following').should('be.visible');
    });

    it('should view posts for hashtag', () => {
      cy.intercept('GET', '/api/search/hashtags*', {
        statusCode: 200,
        body: [
          {
            tag: 'technology',
            count: 1250,
            trending: true
          }
        ]
      }).as('searchHashtags');

      cy.get('[data-testid="search-input"]').type('tech');
      cy.wait('@searchHashtags');
      
      cy.contains('#technology').click();
      cy.url().should('include', '/hashtag/technology');
    });
  });

  describe('Global Search', () => {
    beforeEach(() => {
      cy.get('[data-testid="filter-all"]').click();
    });

    it('should search across all content types', () => {
      cy.intercept('GET', '/api/search/all*', {
        statusCode: 200,
        body: {
          users: [
            {
              _id: '2',
              username: 'john_doe',
              name: 'John Doe',
              bio: 'Software developer',
              profilePic: '',
              followers: [],
              isFollowing: false
            }
          ],
          posts: [
            {
              _id: 'post1',
              text: 'Just finished building an amazing React app!',
              postedBy: {
                _id: '2',
                username: 'john_doe',
                name: 'John Doe',
                profilePic: ''
              },
              createdAt: new Date().toISOString(),
              likes: [],
              replies: []
            }
          ],
          hashtags: [
            {
              tag: 'javascript',
              count: 890,
              trending: false
            }
          ]
        }
      }).as('searchAll');

      cy.get('[data-testid="search-input"]').type('john');
      cy.wait('@searchAll');
      
      // Should display sections for each content type
      cy.contains('Users').should('be.visible');
      cy.contains('John Doe').should('be.visible');
      
      cy.contains('Posts').should('be.visible');
      cy.contains('Just finished building an amazing React app!').should('be.visible');
      
      cy.contains('Hashtags').should('be.visible');
      cy.contains('#javascript').should('be.visible');
    });

    it('should show "View All" links for each section', () => {
      cy.intercept('GET', '/api/search/all*', {
        statusCode: 200,
        body: {
          users: [
            { _id: '2', username: 'john_doe', name: 'John Doe', bio: '', profilePic: '', followers: [], isFollowing: false },
            { _id: '3', username: 'jane_doe', name: 'Jane Doe', bio: '', profilePic: '', followers: [], isFollowing: false },
            { _id: '4', username: 'bob_doe', name: 'Bob Doe', bio: '', profilePic: '', followers: [], isFollowing: false }
          ],
          posts: [],
          hashtags: []
        }
      }).as('searchAll');

      cy.get('[data-testid="search-input"]').type('doe');
      cy.wait('@searchAll');
      
      // Should show "View All" link when there are more than displayed
      cy.contains('View All Users').should('be.visible');
    });

    it('should navigate to filtered results when clicking "View All"', () => {
      cy.intercept('GET', '/api/search/all*', {
        statusCode: 200,
        body: {
          users: [
            { _id: '2', username: 'john_doe', name: 'John Doe', bio: '', profilePic: '', followers: [], isFollowing: false }
          ],
          posts: [],
          hashtags: []
        }
      }).as('searchAll');

      cy.get('[data-testid="search-input"]').type('john');
      cy.wait('@searchAll');
      
      cy.contains('View All Users').click();
      
      // Should switch to users filter
      cy.get('[data-testid="filter-users"]').should('have.class', 'active');
    });
  });

  describe('Search History', () => {
    it('should save search history', () => {
      cy.get('[data-testid="search-input"]').type('react{enter}');
      cy.get('[data-testid="search-input"]').clear().type('javascript{enter}');
      
      // Clear input and focus to see history
      cy.get('[data-testid="search-input"]').clear().focus();
      
      cy.contains('Recent Searches').should('be.visible');
      cy.contains('react').should('be.visible');
      cy.contains('javascript').should('be.visible');
    });

    it('should use search from history', () => {
      cy.intercept('GET', '/api/search/all*', {
        statusCode: 200,
        body: { users: [], posts: [], hashtags: [] }
      }).as('searchAll');

      cy.get('[data-testid="search-input"]').type('react{enter}');
      cy.wait('@searchAll');
      
      cy.get('[data-testid="search-input"]').clear().focus();
      cy.contains('react').click();
      
      cy.get('[data-testid="search-input"]').should('have.value', 'react');
    });

    it('should clear search history', () => {
      cy.get('[data-testid="search-input"]').type('react{enter}');
      cy.get('[data-testid="search-input"]').clear().focus();
      
      cy.contains('Clear History').click();
      cy.contains('Recent Searches').should('not.exist');
    });
  });

  describe('Advanced Search', () => {
    it('should open advanced search modal', () => {
      cy.get('[data-testid="advanced-search"]').click();
      
      cy.contains('Advanced Search').should('be.visible');
      cy.get('[data-testid="date-filter"]').should('be.visible');
      cy.get('[data-testid="user-filter"]').should('be.visible');
      cy.get('[data-testid="sort-filter"]').should('be.visible');
    });

    it('should filter by date range', () => {
      cy.intercept('GET', '/api/search/posts*', {
        statusCode: 200,
        body: []
      }).as('searchPostsAdvanced');

      cy.get('[data-testid="advanced-search"]').click();
      
      cy.get('[data-testid="date-from"]').type('2024-01-01');
      cy.get('[data-testid="date-to"]').type('2024-01-31');
      
      cy.contains('Apply Filters').click();
      cy.wait('@searchPostsAdvanced');
    });

    it('should filter by specific user', () => {
      cy.intercept('GET', '/api/search/posts*', {
        statusCode: 200,
        body: []
      }).as('searchPostsAdvanced');

      cy.get('[data-testid="advanced-search"]').click();
      
      cy.get('[data-testid="user-filter"]').type('john_doe');
      cy.contains('Apply Filters').click();
      
      cy.wait('@searchPostsAdvanced');
    });

    it('should sort results', () => {
      cy.intercept('GET', '/api/search/posts*', {
        statusCode: 200,
        body: []
      }).as('searchPostsAdvanced');

      cy.get('[data-testid="advanced-search"]').click();
      
      cy.get('[data-testid="sort-filter"]').select('Most Liked');
      cy.contains('Apply Filters').click();
      
      cy.wait('@searchPostsAdvanced');
    });
  });

  describe('Search Performance', () => {
    it('should debounce search queries', () => {
      cy.intercept('GET', '/api/search/all*', {
        statusCode: 200,
        body: { users: [], posts: [], hashtags: [] }
      }).as('searchAll');

      cy.get('[data-testid="search-input"]').type('r');
      cy.get('[data-testid="search-input"]').type('e');
      cy.get('[data-testid="search-input"]').type('a');
      cy.get('[data-testid="search-input"]').type('c');
      cy.get('[data-testid="search-input"]').type('t');
      
      // Should only make one API call after debounce
      cy.wait('@searchAll');
      cy.get('@searchAll.all').should('have.length', 1);
    });

    it('should show loading state during search', () => {
      cy.intercept('GET', '/api/search/all*', {
        statusCode: 200,
        body: { users: [], posts: [], hashtags: [] },
        delay: 1000
      }).as('searchAllSlow');

      cy.get('[data-testid="search-input"]').type('react');
      
      cy.get('[data-testid="search-loading"]').should('be.visible');
      cy.wait('@searchAllSlow');
      cy.get('[data-testid="search-loading"]').should('not.exist');
    });

    it('should handle search errors gracefully', () => {
      cy.intercept('GET', '/api/search/all*', {
        statusCode: 500,
        body: { error: 'Search service unavailable' }
      }).as('searchError');

      cy.get('[data-testid="search-input"]').type('react');
      cy.wait('@searchError');
      
      cy.contains('Search service unavailable').should('be.visible');
      cy.contains('Try again').should('be.visible');
    });
  });

  describe('Responsive Search', () => {
    it('should work on mobile devices', () => {
      cy.viewport(375, 667);
      
      cy.get('[data-testid="search-input"]').should('be.visible');
      cy.get('[data-testid="search-filters"]').should('be.visible');
      
      // Test mobile-specific search behavior
      cy.get('[data-testid="search-input"]').type('react');
      cy.get('[data-testid="mobile-search-results"]').should('be.visible');
    });

    it('should use overlay on mobile', () => {
      cy.viewport(375, 667);
      
      cy.get('[data-testid="search-input"]').focus();
      cy.get('[data-testid="search-overlay"]').should('be.visible');
      
      // Close overlay
      cy.get('[data-testid="close-search"]').click();
      cy.get('[data-testid="search-overlay"]').should('not.exist');
    });
  });
});
