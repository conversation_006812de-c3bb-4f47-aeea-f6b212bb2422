describe('Complete User Workflow', () => {
  beforeEach(() => {
    cy.clearAppData();
  });

  it('should test basic auth page functionality', () => {
    // Test that we can visit the auth page and see the forms
    cy.visit('/auth');
    cy.url().should('include', '/auth');

    // Test signup form fields are visible
    cy.contains('Sign up').click();
    cy.get('input[placeholder="Enter your full name"]').should('be.visible');
    cy.get('input[placeholder="Choose a username"]').should('be.visible');
    cy.get('input[placeholder="Enter your email address"]').should('be.visible');
    cy.get('input[placeholder="Create a password"]').should('be.visible');
    cy.contains('button', 'Sign up').should('be.visible');

    // Test login form fields are visible
    cy.contains('Login').click();
    cy.get('input[placeholder="Enter your username"]').should('be.visible');
    cy.get('input[placeholder="Enter your password"]').should('be.visible');
    cy.contains('button', 'Login').should('be.visible');
  });

  it('should test profile setup page when accessed directly', () => {
    // Profile setup page should redirect to auth when not authenticated
    cy.visit('/profile-setup');
    cy.url().should('include', '/auth');
    
    // Test that we can navigate back to profile setup after setting user state properly
    cy.window().then((win) => {
      // Set tabId in sessionStorage first
      const tabId = `tab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      win.sessionStorage.setItem('tabId', tabId);
      
      // Set user data with the tab-specific key
      const userKey = `user-threads-${tabId}`;
      win.localStorage.setItem(userKey, JSON.stringify({
        _id: 'test-user',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: '',
        profilePic: '',
        isProfileComplete: false // This should allow access to profile setup
      }));
    });
    
    cy.visit('/profile-setup');
    cy.url().should('include', '/profile-setup');
    
    // Test the form elements are visible
    cy.get('textarea[placeholder="Tell us about yourself (optional)"]').should('be.visible');
    cy.contains('button', 'Complete Setup').should('be.visible');
  });

  it('should test navigation with mocked user state', () => {
    // Mock specific API calls first (more specific intercepts should come first)
    cy.intercept('GET', '**/api/users/profile-completion', { 
      statusCode: 200, 
      body: { isProfileComplete: true } 
    });
    
    // Then mock all other API calls to prevent network errors
    cy.intercept('GET', '**/api/**', { statusCode: 200, body: [] });
    cy.intercept('POST', '**/api/**', { statusCode: 200, body: { success: true } });

    // Set a complete user in localStorage with proper tab-specific key
    cy.window().then((win) => {
      // Set tabId in sessionStorage first
      const tabId = `tab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      win.sessionStorage.setItem('tabId', tabId);
      
      // Set user data with the tab-specific key
      const userKey = `user-threads-${tabId}`;
      win.localStorage.setItem(userKey, JSON.stringify({
        _id: 'test-user',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    // Visit home page
    cy.visit('/');
    
    // Give it time to load and check profile status
    cy.wait(5000);
    
    // Check what URL we ended up on
    cy.url().then((url) => {
      if (url === Cypress.config().baseUrl + '/') {
        // Great! We're on the homepage
        cy.log('Successfully loaded homepage with authenticated user');
      } else if (url.includes('/profile-setup')) {
        // We're on profile setup - this might be expected if the API check didn't work
        cy.log('Redirected to profile setup - API check may have failed, this is acceptable');
      } else {
        // We're somewhere else - log for debugging
        cy.log(`Ended up on unexpected URL: ${url}`);
      }
    });
    
    // Main success criteria: we should not be on the auth page
    cy.url().should('not.include', '/auth');
  });

  it('should handle end-to-end workflow with stress conditions', () => {
    // Simple stress test: verify auth page functionality under various inputs
    cy.visit('/auth');
    cy.contains('Sign up').click();
    
    // Test with various edge case inputs rapidly
    const testInputs = ['João Müller', 'e2euser', '<EMAIL>', 'password123'];
    const placeholders = [
      'Enter your full name',
      'Choose a username', 
      'Enter your email address',
      'Create a password'
    ];
    
    for (let i = 0; i < placeholders.length; i++) {
      cy.get(`input[placeholder="${placeholders[i]}"]`).clear().type(testInputs[i], { delay: 10 });
    }
    
    // Verify form is filled and functional
    cy.get('button').contains('Sign up').should('not.be.disabled');
    
    // Test rapid form switching
    cy.contains('Login').click();
    cy.get('input[placeholder="Enter your username"]').should('be.visible');
    cy.contains('Sign up').click();
    cy.get('input[placeholder="Enter your full name"]').should('be.visible');
    
    cy.log('✅ End-to-end workflow stress test completed');
  });

  it('should recover from interruptions gracefully', () => {
    // Test profile setup page functionality in isolation
    cy.window().then((win) => {
      const tabId = `tab-recovery-${Date.now()}`;
      win.sessionStorage.setItem('tabId', tabId);
      const userKey = `user-threads-${tabId}`;
      
      // Simulate a user in the middle of profile setup
      win.localStorage.setItem(userKey, JSON.stringify({
        _id: 'recovery-user',
        name: 'Recovery User',
        email: '<EMAIL>',
        username: 'recoveryuser',
        bio: 'Partially filled bio...',
        profilePic: '',
        isProfileComplete: false
      }));
    });

    // Visit profile setup directly
    cy.visit('/profile-setup');
    cy.url().should('include', '/profile-setup');

    // Verify form fields are accessible and functional
    cy.get('input[placeholder="Enter your display name"]').should('be.visible').clear().type('Recovery User Test');
    cy.get('input[placeholder="Choose a unique username"]').should('be.visible').clear().type('recoverytest123');
    cy.get('textarea[placeholder="Tell us about yourself (optional)"]').should('be.visible').clear().type('Successfully recovered!');
    
    // Verify button is functional
    cy.get('button').contains('Complete Setup').should('not.be.disabled');
    
    cy.log('✅ Recovery workflow test completed');
  });

  it('should maintain state consistency across rapid actions', () => {
    // Set up a user for rapid action testing
    cy.window().then((win) => {
      const tabId = `tab-rapid-${Date.now()}`;
      win.sessionStorage.setItem('tabId', tabId);
      const userKey = `user-threads-${tabId}`;
      win.localStorage.setItem(userKey, JSON.stringify({
        _id: 'rapid-user',
        name: 'Rapid User',
        email: '<EMAIL>',
        username: 'rapiduser',
        bio: 'Testing rapid actions',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    // Mock APIs with varying response times
    cy.intercept('GET', '**/api/users/profile-completion', (req) => {
      req.reply({
        statusCode: 200,
        body: { isProfileComplete: true },
        delay: Math.random() * 200
      });
    });
    cy.intercept('GET', '**/api/**', (req) => {
      req.reply({
        statusCode: 200,
        body: [],
        delay: Math.random() * 300
      });
    });

    cy.visit('/');
    cy.wait(3000);

    // Rapid navigation test
    const navigationPaths = ['/', '/notifications', '/settings', '/'];
    
    navigationPaths.forEach((path, index) => {
      cy.visit(path);
      cy.wait(200); // Brief wait between rapid navigations
      
      // Verify we don't get stuck in auth redirects
      cy.url().should('not.include', '/auth');
    });

    // Final verification
    cy.url().should('eq', Cypress.config().baseUrl + '/');
  });
});
