/**
 * Mobile Navigation Test Component
 * Test component to verify all navigation buttons are visible on mobile
 */

import React from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  Badge, 
  Button,
  Flex
} from '@chakra-ui/react';
import { House, User, Chat, Gear, MagnifyingGlass, Bell } from "phosphor-react";

const MobileNavTest = () => {
  const navButtons = [
    { name: 'Home', icon: House, path: '/' },
    { name: 'Search', icon: MagnifyingGlass, path: '/search' },
    { name: 'Profile', icon: User, path: '/profile' },
    { name: 'Notifications', icon: Bell, path: '/notifications' },
    { name: 'Chat', icon: Chat, path: '/chat' },
    { name: 'Settings', icon: Gear, path: '/settings' }
  ];

  const deviceWidth = window.innerWidth;
  const buttonWidth = Math.floor((deviceWidth - 20) / navButtons.length); // 20px for padding
  const canFitAllButtons = buttonWidth >= 36; // Minimum button width

  return (
    <Box p={4} maxW="400px" mx="auto">
      <VStack spacing={4} align="stretch">
        <Text fontSize="xl" fontWeight="bold" textAlign="center">
          📱 Mobile Navigation Test
        </Text>
        
        <Box p={3} bg="gray.100" borderRadius="md">
          <Text fontWeight="bold" mb={2}>Device Info:</Text>
          <Text fontSize="sm">Width: {deviceWidth}px</Text>
          <Text fontSize="sm">Button Width: {buttonWidth}px</Text>
          <Text fontSize="sm">
            Can Fit All Buttons: <Badge colorScheme={canFitAllButtons ? 'green' : 'red'}>
              {canFitAllButtons ? 'Yes' : 'No'}
            </Badge>
          </Text>
        </Box>

        <Box p={3} bg="blue.50" borderRadius="md">
          <Text fontWeight="bold" mb={2}>Navigation Buttons ({navButtons.length}):</Text>
          <VStack spacing={2} align="start">
            {navButtons.map((button, index) => (
              <HStack key={index} spacing={2}>
                <Badge colorScheme="blue">{index + 1}</Badge>
                <Text fontSize="sm">{button.name}</Text>
                <Text fontSize="xs" color="gray.600">({button.path})</Text>
              </HStack>
            ))}
          </VStack>
        </Box>

        <Box p={3} bg="white" border="1px solid" borderColor="gray.200" borderRadius="md">
          <Text fontWeight="bold" mb={2}>Test Navigation Bar:</Text>
          <Flex
            gap="2px"
            padding="6px 2px"
            bg="rgba(8, 8, 8, 0.85)"
            borderRadius="0"
            justify="space-evenly"
            width="100%"
            borderTop="1px solid rgba(255, 255, 255, 0.1)"
          >
            {navButtons.map((ButtonIcon, index) => (
              <Button
                key={index}
                variant="ghost"
                minH="36px"
                minW="36px"
                maxW="48px"
                padding="4px"
                flex="1"
                flexShrink="0"
                bg="transparent"
                _hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
                color="white"
              >
                <ButtonIcon.icon size={18} weight="bold" />
              </Button>
            ))}
          </Flex>
        </Box>

        <Box p={3} bg="yellow.50" borderRadius="md">
          <Text fontWeight="bold" mb={2}>Recommendations:</Text>
          {!canFitAllButtons && (
            <VStack spacing={1} align="start">
              <Text fontSize="sm" color="orange.600">
                • Screen too narrow for all buttons
              </Text>
              <Text fontSize="sm" color="orange.600">
                • Consider reducing button size or using overflow scroll
              </Text>
            </VStack>
          )}
          {canFitAllButtons && (
            <Text fontSize="sm" color="green.600">
              ✅ All buttons should fit comfortably
            </Text>
          )}
        </Box>

        <Button
          colorScheme="blue"
          onClick={() => {
            console.log('📱 Navigation Test Results:');
            console.log('Device Width:', deviceWidth);
            console.log('Button Count:', navButtons.length);
            console.log('Calculated Button Width:', buttonWidth);
            console.log('Can Fit All Buttons:', canFitAllButtons);
            console.log('Navigation Buttons:', navButtons);
          }}
        >
          Log Test Results
        </Button>

        <Text fontSize="xs" color="gray.600" textAlign="center">
          This test simulates the mobile navigation bar to verify all buttons are visible.
          Check the browser console for detailed results.
        </Text>
      </VStack>
    </Box>
  );
};

export default MobileNavTest;
