// ***********************************************************
// This example support/component.js is processed and
// loaded automatically before your component test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Import global styles if needed
// import '../../src/index.css'

// Example command for mounting React components with providers
import { mount } from '@cypress/react'
import { ChakraProvider } from '@chakra-ui/react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { RecoilRoot } from 'recoil'

Cypress.Commands.add('mount', (component, options = {}) => {
  const { routerProps = {}, ...mountOptions } = options

  const wrapped = (
    <RecoilRoot>
      <ChakraProvider>
        <BrowserRouter {...routerProps}>
          {component}
        </BrowserRouter>
      </ChakraProvider>
    </RecoilRoot>
  )

  return mount(wrapped, mountOptions)
})
