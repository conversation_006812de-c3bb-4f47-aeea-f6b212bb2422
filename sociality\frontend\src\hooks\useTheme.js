/**
 * Theme management hook
 * Provides theme switching functionality with persistence and Chakra UI integration
 */
import { useEffect, useCallback, useRef } from 'react';
import { useRecoilState } from 'recoil';
import { useColorMode } from '@chakra-ui/react';
import { themeAtom } from '../atoms';
import { STORAGE_KEYS } from '../utils/constants';
import { applyThemeToDocument } from '../utils/themeUtils';

const useTheme = () => {
  const [theme, setTheme] = useRecoilState(themeAtom);
  const { colorMode, setColorMode } = useColorMode();
  const initialized = useRef(false);

  // Initialize theme from localStorage on mount (only once)
  useEffect(() => {
    if (initialized.current) return;
    initialized.current = true;

    let savedTheme = localStorage.getItem(STORAGE_KEYS.THEME);

    // Migration: Check old Chakra UI key if new key doesn't exist
    if (!savedTheme) {
      const oldTheme = localStorage.getItem('chakra-ui-color-mode');
      if (oldTheme && (oldTheme === 'light' || oldTheme === 'dark')) {
        savedTheme = oldTheme;
        localStorage.setItem(STORAGE_KEYS.THEME, savedTheme);
        localStorage.removeItem('chakra-ui-color-mode'); // Clean up old key
      }
    }

    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
      setTheme(savedTheme);
      setColorMode(savedTheme);
    } else {
      // Default to light mode if no saved preference
      setTheme('light');
      setColorMode('light');
      localStorage.setItem(STORAGE_KEYS.THEME, 'light');
    }
  }, []); // Empty dependency array to run only once

  // Sync theme changes with Chakra UI, document, and localStorage
  useEffect(() => {
    if (!initialized.current) return; // Don't sync until initialized

    // Small delay to prevent race conditions
    const timeoutId = setTimeout(() => {
      if (theme !== colorMode) {
        setColorMode(theme);
      }
      applyThemeToDocument(theme);
      localStorage.setItem(STORAGE_KEYS.THEME, theme);
    }, 10);

    return () => clearTimeout(timeoutId);
  }, [theme, colorMode, setColorMode]);

  // Toggle between light and dark themes
  const toggleTheme = useCallback(() => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
  }, [theme, setTheme]);

  // Set specific theme
  const setSpecificTheme = useCallback((newTheme) => {
    if (newTheme === 'light' || newTheme === 'dark') {
      setTheme(newTheme);
    }
  }, [setTheme]);

  // Check if current theme is dark
  const isDark = theme === 'dark';

  // Check if current theme is light
  const isLight = theme === 'light';

  return {
    theme,
    isDark,
    isLight,
    toggleTheme,
    setTheme: setSpecificTheme,
  };
};

export default useTheme;
