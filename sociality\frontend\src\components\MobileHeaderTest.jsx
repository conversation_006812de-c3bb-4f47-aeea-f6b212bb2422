/**
 * Mobile Header Test Component
 * Test component to verify dynamic mobile header functionality
 */

import React from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  Badge, 
  Code, 
  Button,
  Divider,
  Grid,
  GridItem
} from '@chakra-ui/react';
import { useDynamicHeader } from '../hooks/useDynamicHeader';
import { 
  getCurrentDeviceCategory,
  getDynamicSpacing,
  getDynamicButtonSize,
  getDynamicLogoSize,
  getSafeAreaInsets,
  hasNotch,
  getOrientationAdjustments
} from '../utils/responsiveBreakpoints';

const MobileHeaderTest = () => {
  const headerData = useDynamicHeader();
  
  const deviceInfo = {
    category: getCurrentDeviceCategory(),
    spacing: getDynamicSpacing(),
    buttonSize: getDynamicButtonSize(),
    logoSize: getDynamicLogoSize(),
    safeArea: getSafeAreaInsets(),
    hasNotch: hasNotch(),
    orientation: getOrientationAdjustments(),
    viewport: `${window.innerWidth}x${window.innerHeight}`,
    userAgent: navigator.userAgent.substring(0, 50) + '...'
  };

  const refreshData = () => {
    headerData.updateDeviceCategory();
    window.location.reload();
  };

  return (
    <Box p={6} maxW="800px" mx="auto" bg="gray.50" borderRadius="lg">
      <VStack spacing={6} align="stretch">
        <Text fontSize="2xl" fontWeight="bold" textAlign="center">
          📱 Dynamic Mobile Header Test
        </Text>
        
        {/* Device Detection */}
        <Box p={4} bg="white" borderRadius="md" shadow="sm">
          <Text fontWeight="bold" mb={3}>Device Detection:</Text>
          <Grid templateColumns="repeat(2, 1fr)" gap={3}>
            <GridItem>
              <Text fontSize="sm">
                Category: <Badge colorScheme="blue">{deviceInfo.category}</Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Mobile: <Badge colorScheme={headerData.isMobile ? 'green' : 'red'}>
                  {headerData.isMobile ? 'Yes' : 'No'}
                </Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Orientation: <Badge colorScheme="purple">{headerData.orientation}</Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Has Notch: <Badge colorScheme={deviceInfo.hasNotch ? 'orange' : 'gray'}>
                  {deviceInfo.hasNotch ? 'Yes' : 'No'}
                </Badge>
              </Text>
            </GridItem>
          </Grid>
        </Box>

        {/* Logo Information */}
        <Box p={4} bg="white" borderRadius="md" shadow="sm">
          <Text fontWeight="bold" mb={3}>Logo Configuration:</Text>
          <VStack align="start" spacing={2}>
            <Text fontSize="sm">
              Visible: <Badge colorScheme={headerData.isLogoVisible ? 'green' : 'red'}>
                {headerData.isLogoVisible ? 'Yes' : 'No'}
              </Badge>
            </Text>
            <Text fontSize="sm">
              Size: <Code>{deviceInfo.logoSize.size}px</Code>
            </Text>
            <Text fontSize="sm">
              Container: <Code>{deviceInfo.logoSize.containerSize}px</Code>
            </Text>
            <Text fontSize="sm">
              Position: <Code>top: {headerData.responsive.logoPosition.base.top}</Code>
            </Text>
          </VStack>
        </Box>

        {/* Navigation Information */}
        <Box p={4} bg="white" borderRadius="md" shadow="sm">
          <Text fontWeight="bold" mb={3}>Navigation Configuration:</Text>
          <Grid templateColumns="repeat(2, 1fr)" gap={3}>
            <GridItem>
              <Text fontSize="sm">
                Direction: <Code>{headerData.responsive.navDirection.base}</Code>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Spacing: <Code>{headerData.responsive.spacing.base}px</Code>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Button Size: <Code>{headerData.responsive.buttonSize.base}</Code>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Icon Size: <Code>{headerData.responsive.iconSize.base}px</Code>
              </Text>
            </GridItem>
          </Grid>
        </Box>

        {/* Safe Area Information */}
        <Box p={4} bg="white" borderRadius="md" shadow="sm">
          <Text fontWeight="bold" mb={3}>Safe Area Insets:</Text>
          <HStack spacing={4}>
            <Text fontSize="sm">Top: <Code>{deviceInfo.safeArea.top}px</Code></Text>
            <Text fontSize="sm">Bottom: <Code>{deviceInfo.safeArea.bottom}px</Code></Text>
            <Text fontSize="sm">Left: <Code>{deviceInfo.safeArea.left}px</Code></Text>
            <Text fontSize="sm">Right: <Code>{deviceInfo.safeArea.right}px</Code></Text>
          </HStack>
        </Box>

        {/* Orientation Adjustments */}
        <Box p={4} bg="white" borderRadius="md" shadow="sm">
          <Text fontWeight="bold" mb={3}>Orientation Adjustments:</Text>
          <VStack align="start" spacing={2}>
            <Text fontSize="sm">
              Logo Top: <Code>{deviceInfo.orientation.logoTop}px</Code>
            </Text>
            <Text fontSize="sm">
              Nav Bottom: <Code>{deviceInfo.orientation.navBottom}px</Code>
            </Text>
            <Text fontSize="sm">
              Spacing: <Code>{deviceInfo.orientation.spacing}px</Code>
            </Text>
            <Text fontSize="sm">
              Button Height: <Code>{deviceInfo.orientation.buttonSize.minH}</Code>
            </Text>
          </VStack>
        </Box>

        {/* Technical Information */}
        <Box p={4} bg="gray.100" borderRadius="md">
          <Text fontWeight="bold" mb={3}>Technical Info:</Text>
          <VStack align="start" spacing={1}>
            <Text fontSize="sm">Viewport: <Code>{deviceInfo.viewport}</Code></Text>
            <Text fontSize="xs" color="gray.600">
              User Agent: {deviceInfo.userAgent}
            </Text>
          </VStack>
        </Box>

        <Divider />

        {/* Test Actions */}
        <VStack spacing={3}>
          <Button
            colorScheme="blue"
            onClick={() => {
              console.log('📱 Header Data:', headerData);
              console.log('📱 Device Info:', deviceInfo);
            }}
          >
            Log Data to Console
          </Button>
          
          <Button
            colorScheme="green"
            onClick={refreshData}
          >
            Refresh & Update
          </Button>
          
          <Text fontSize="sm" color="gray.600" textAlign="center">
            Resize your browser window or rotate your device to see dynamic changes.
            Check the browser console for detailed logs.
          </Text>
        </VStack>

        {/* Responsive Breakpoints Display */}
        <Box p={4} bg="blue.50" borderRadius="md">
          <Text fontWeight="bold" mb={3}>Current Responsive Values:</Text>
          <Grid templateColumns="repeat(3, 1fr)" gap={2} fontSize="xs">
            <GridItem>
              <Text><strong>xs:</strong> {headerData.responsive.buttonSize.xs}</Text>
            </GridItem>
            <GridItem>
              <Text><strong>sm:</strong> {headerData.responsive.buttonSize.sm}</Text>
            </GridItem>
            <GridItem>
              <Text><strong>md:</strong> {headerData.responsive.buttonSize.md}</Text>
            </GridItem>
            <GridItem>
              <Text><strong>lg:</strong> {headerData.responsive.buttonSize.lg}</Text>
            </GridItem>
            <GridItem>
              <Text><strong>xl:</strong> {headerData.responsive.buttonSize.xl}</Text>
            </GridItem>
            <GridItem>
              <Text><strong>2xl:</strong> {headerData.responsive.buttonSize['2xl']}</Text>
            </GridItem>
          </Grid>
        </Box>
      </VStack>
    </Box>
  );
};

export default MobileHeaderTest;
