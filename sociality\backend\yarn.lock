# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@cypress/request-promise@^5.0.0":
  "integrity" "sha512-eKdYVpa9cBEw2kTBlHeu1PP16Blwtum6QHg/u9s/MoHkZfuo1pRGka1VlUHXF5kdew82BvOJVVGk0x8X0nbp+w=="
  "resolved" "https://registry.npmjs.org/@cypress/request-promise/-/request-promise-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "bluebird" "^3.5.0"
    "request-promise-core" "1.1.3"
    "stealthy-require" "^1.1.1"
    "tough-cookie" "^4.1.3"

"@cypress/request@^3.0.0", "@cypress/request@^3.0.1":
  "integrity" "sha512-h0NFgh1mJmm1nr4jCwkGHwKneVYKghUyWe6TMNrk0B9zsjAJxpg8C4/+BAcmLgCPa1vj1V8rNUaILl+zYRUWBQ=="
  "resolved" "https://registry.npmjs.org/@cypress/request/-/request-3.0.8.tgz"
  "version" "3.0.8"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~4.0.0"
    "http-signature" "~1.4.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "performance-now" "^2.1.0"
    "qs" "6.14.0"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "^5.0.0"
    "tunnel-agent" "^0.6.0"
    "uuid" "^8.3.2"

"@discordjs/builders@^1.11.2":
  "integrity" "sha512-F1WTABdd8/R9D1icJzajC4IuLyyS8f3rTOz66JsSI3pKvpCAtsMBweu8cyNYsIyvcrKAVn9EPK+Psoymq+XC0A=="
  "resolved" "https://registry.npmjs.org/@discordjs/builders/-/builders-1.11.2.tgz"
  "version" "1.11.2"
  dependencies:
    "@discordjs/formatters" "^0.6.1"
    "@discordjs/util" "^1.1.1"
    "@sapphire/shapeshift" "^4.0.0"
    "discord-api-types" "^0.38.1"
    "fast-deep-equal" "^3.1.3"
    "ts-mixer" "^6.0.4"
    "tslib" "^2.6.3"

"@discordjs/collection@^2.1.0":
  "integrity" "sha512-LiSusze9Tc7qF03sLCujF5iZp7K+vRNEDBZ86FT9aQAv3vxMLihUvKvpsCWiQ2DJq1tVckopKm1rxomgNUc9hg=="
  "resolved" "https://registry.npmjs.org/@discordjs/collection/-/collection-2.1.1.tgz"
  "version" "2.1.1"

"@discordjs/collection@^2.1.1":
  "integrity" "sha512-LiSusze9Tc7qF03sLCujF5iZp7K+vRNEDBZ86FT9aQAv3vxMLihUvKvpsCWiQ2DJq1tVckopKm1rxomgNUc9hg=="
  "resolved" "https://registry.npmjs.org/@discordjs/collection/-/collection-2.1.1.tgz"
  "version" "2.1.1"

"@discordjs/collection@1.5.3":
  "integrity" "sha512-SVb428OMd3WO1paV3rm6tSjM4wC+Kecaa1EUGX7vc6/fddvw/6lg90z4QtCqm21zvVe92vMMDt9+DkIvjXImQQ=="
  "resolved" "https://registry.npmjs.org/@discordjs/collection/-/collection-1.5.3.tgz"
  "version" "1.5.3"

"@discordjs/formatters@^0.6.1":
  "integrity" "sha512-5cnX+tASiPCqCWtFcFslxBVUaCetB0thvM/JyavhbXInP1HJIEU+Qv/zMrnuwSsX3yWH2lVXNJZeDK3EiP4HHg=="
  "resolved" "https://registry.npmjs.org/@discordjs/formatters/-/formatters-0.6.1.tgz"
  "version" "0.6.1"
  dependencies:
    "discord-api-types" "^0.38.1"

"@discordjs/rest@^2.5.0":
  "integrity" "sha512-PWhchxTzpn9EV3vvPRpwS0EE2rNYB9pvzDU/eLLW3mByJl0ZHZjHI2/wA8EbH2gRMQV7nu+0FoDF84oiPl8VAQ=="
  "resolved" "https://registry.npmjs.org/@discordjs/rest/-/rest-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "@discordjs/collection" "^2.1.1"
    "@discordjs/util" "^1.1.1"
    "@sapphire/async-queue" "^1.5.3"
    "@sapphire/snowflake" "^3.5.3"
    "@vladfrangu/async_event_emitter" "^2.4.6"
    "discord-api-types" "^0.38.1"
    "magic-bytes.js" "^1.10.0"
    "tslib" "^2.6.3"
    "undici" "6.21.1"

"@discordjs/util@^1.1.0", "@discordjs/util@^1.1.1":
  "integrity" "sha512-eddz6UnOBEB1oITPinyrB2Pttej49M9FZQY8NxgEvc3tq6ZICZ19m70RsmzRdDHk80O9NoYN/25AqJl8vPVf/g=="
  "resolved" "https://registry.npmjs.org/@discordjs/util/-/util-1.1.1.tgz"
  "version" "1.1.1"

"@discordjs/ws@^1.2.2":
  "integrity" "sha512-dyfq7yn0wO0IYeYOs3z79I6/HumhmKISzFL0Z+007zQJMtAFGtt3AEoq1nuLXtcunUE5YYYQqgKvybXukAK8/w=="
  "resolved" "https://registry.npmjs.org/@discordjs/ws/-/ws-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "@discordjs/collection" "^2.1.0"
    "@discordjs/rest" "^2.5.0"
    "@discordjs/util" "^1.1.0"
    "@sapphire/async-queue" "^1.5.2"
    "@types/ws" "^8.5.10"
    "@vladfrangu/async_event_emitter" "^2.2.4"
    "discord-api-types" "^0.38.1"
    "tslib" "^2.6.2"
    "ws" "^8.17.0"

"@mongodb-js/saslprep@^1.1.0", "@mongodb-js/saslprep@^1.1.9":
  "integrity" "sha512-EB0O3SCSNRUFk66iRCpI+cXzIjdswfCs7F6nOC3RAGJ7xr5YhaicvsRwJ9eyzYvYRlCSDUO/c7g4yNulxKC1WA=="
  "resolved" "https://registry.npmjs.org/@mongodb-js/saslprep/-/saslprep-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "sparse-bitfield" "^3.0.3"

"@sapphire/async-queue@^1.5.2", "@sapphire/async-queue@^1.5.3":
  "integrity" "sha512-cvGzxbba6sav2zZkH8GPf2oGk9yYoD5qrNWdu9fRehifgnFZJMV+nuy2nON2roRO4yQQ+v7MK/Pktl/HgfsUXg=="
  "resolved" "https://registry.npmjs.org/@sapphire/async-queue/-/async-queue-1.5.5.tgz"
  "version" "1.5.5"

"@sapphire/shapeshift@^4.0.0":
  "integrity" "sha512-d9dUmWVA7MMiKobL3VpLF8P2aeanRTu6ypG2OIaEv/ZHH/SUQ2iHOVyi5wAPjQ+HmnMuL0whK9ez8I/raWbtIg=="
  "resolved" "https://registry.npmjs.org/@sapphire/shapeshift/-/shapeshift-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "fast-deep-equal" "^3.1.3"
    "lodash" "^4.17.21"

"@sapphire/snowflake@^3.5.3", "@sapphire/snowflake@3.5.3":
  "integrity" "sha512-jjmJywLAFoWeBi1W7994zZyiNWPIiqRRNAmSERxyg93xRGzNYvGjlZ0gR6x0F4gPRi2+0O6S71kOZYyr3cxaIQ=="
  "resolved" "https://registry.npmjs.org/@sapphire/snowflake/-/snowflake-3.5.3.tgz"
  "version" "3.5.3"

"@socket.io/component-emitter@~3.1.0":
  "integrity" "sha512-9BCxFwvbGg/RsZK9tjXd8s4UcwR0MWeFQ1XEKIQVVvAGJyINdrqKMcTRyLoK8Rse1GjzLV9cwjWV1olXRWEXVA=="
  "resolved" "https://registry.npmjs.org/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz"
  "version" "3.1.2"

"@types/cors@^2.8.12":
  "integrity" "sha512-nX3d0sxJW41CqQvfOzVG1NCTXfFDrDWIghCZncpHeWlVFd81zxB/DLhg7avFg6eHLCRX7ckBmoIIcqa++upvJA=="
  "resolved" "https://registry.npmjs.org/@types/cors/-/cors-2.8.18.tgz"
  "version" "2.8.18"
  dependencies:
    "@types/node" "*"

"@types/luxon@~3.4.0":
  "integrity" "sha512-TifLZlFudklWlMBfhubvgqTXRzLDI5pCbGa4P8a3wPyUQSW+1xQ5eDsreP9DWHX3tjq1ke96uYG/nwundroWcA=="
  "resolved" "https://registry.npmjs.org/@types/luxon/-/luxon-3.4.2.tgz"
  "version" "3.4.2"

"@types/node@*", "@types/node@>=10.0.0":
  "integrity" "sha512-LNdjOkUDlU1RZb8e1kOIUpN1qQUlzGkEtbVNo53vbrwDg5om6oduhm4SiUaPW5ASTXhAiP0jInWG8Qx9fVlOeQ=="
  "resolved" "https://registry.npmjs.org/@types/node/-/node-22.15.29.tgz"
  "version" "22.15.29"
  dependencies:
    "undici-types" "~6.21.0"

"@types/webidl-conversions@*":
  "integrity" "sha512-CiJJvcRtIgzadHCYXw7dqEnMNRjhGZlYK05Mj9OyktqV8uVT8fD2BFOB7S1uwBE3Kj2Z+4UyPmFw/Ixgw/LAlA=="
  "resolved" "https://registry.npmjs.org/@types/webidl-conversions/-/webidl-conversions-7.0.3.tgz"
  "version" "7.0.3"

"@types/whatwg-url@^11.0.2":
  "integrity" "sha512-coYR071JRaHa+xoEvvYqvnIHaVqaYrLPbsufM9BF63HkwI5Lgmy2QR8Q5K/lYDYo5AK82wOvSOS0UsLTpTG7uQ=="
  "resolved" "https://registry.npmjs.org/@types/whatwg-url/-/whatwg-url-11.0.5.tgz"
  "version" "11.0.5"
  dependencies:
    "@types/webidl-conversions" "*"

"@types/whatwg-url@^8.2.1":
  "integrity" "sha512-FtQu10RWgn3D9U4aazdwIE2yzphmTJREDqNdODHrbrZmmMqI0vMheC/6NE/J1Yveaj8H+ela+YwWTjq5PGmuhA=="
  "resolved" "https://registry.npmjs.org/@types/whatwg-url/-/whatwg-url-8.2.2.tgz"
  "version" "8.2.2"
  dependencies:
    "@types/node" "*"
    "@types/webidl-conversions" "*"

"@types/ws@^8.5.10":
  "integrity" "sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg=="
  "resolved" "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz"
  "version" "8.18.1"
  dependencies:
    "@types/node" "*"

"@vladfrangu/async_event_emitter@^2.2.4", "@vladfrangu/async_event_emitter@^2.4.6":
  "integrity" "sha512-RaI5qZo6D2CVS6sTHFKg1v5Ohq/+Bo2LZ5gzUEwZ/WkHhwtGTCB/sVLw8ijOkAUxasZ+WshN/Rzj4ywsABJ5ZA=="
  "resolved" "https://registry.npmjs.org/@vladfrangu/async_event_emitter/-/async_event_emitter-2.4.6.tgz"
  "version" "2.4.6"

"accepts@~1.3.4", "accepts@~1.3.8":
  "integrity" "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw=="
  "resolved" "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz"
  "version" "1.3.8"
  dependencies:
    "mime-types" "~2.1.34"
    "negotiator" "0.6.3"

"ajv@^6.12.3":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"anymatch@~3.1.2":
  "integrity" "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw=="
  "resolved" "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"append-field@^1.0.0":
  "integrity" "sha512-klpgFSWLW1ZEs8svjfb7g4qWY0YS5imI82dTg+QahUvJ8YqAY0P10Uk8tTyh9ZGuYEZEMaeJYCF5BFuX552hsw=="
  "resolved" "https://registry.npmjs.org/append-field/-/append-field-1.0.0.tgz"
  "version" "1.0.0"

"array-buffer-byte-length@^1.0.1", "array-buffer-byte-length@^1.0.2":
  "integrity" "sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw=="
  "resolved" "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bound" "^1.0.3"
    "is-array-buffer" "^3.0.5"

"array-flatten@1.1.1":
  "integrity" "sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg=="
  "resolved" "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array.prototype.findindex@^2.0.2":
  "integrity" "sha512-LLm4mhxa9v8j0A/RPnpQAP4svXToJFh+Hp1pNYl5ZD5qpB4zdx/D4YjpVcETkhFbUKWO3iGMVLvrOnnmkAJT6A=="
  "resolved" "https://registry.npmjs.org/array.prototype.findindex/-/array.prototype.findindex-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "call-bind" "^1.0.8"
    "call-bound" "^1.0.3"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.6"
    "es-object-atoms" "^1.0.0"
    "es-shim-unscopables" "^1.0.2"

"arraybuffer.prototype.slice@^1.0.4":
  "integrity" "sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ=="
  "resolved" "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "array-buffer-byte-length" "^1.0.1"
    "call-bind" "^1.0.8"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.5"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.6"
    "is-array-buffer" "^3.0.4"

"asn1.js@^5.4.1":
  "integrity" "sha512-+I//4cYPccV8LdmBLiX8CYvf9Sp3vQsrqu2QNXRcrbiWvcx/UdlFiqUJJzxRQxgsZmvhXhn4cSKeSmoFjVdupA=="
  "resolved" "https://registry.npmjs.org/asn1.js/-/asn1.js-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bn.js" "^4.0.0"
    "inherits" "^2.0.1"
    "minimalistic-assert" "^1.0.0"
    "safer-buffer" "^2.1.0"

"asn1@~0.2.3":
  "integrity" "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ=="
  "resolved" "https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz"
  "version" "0.2.6"
  dependencies:
    "safer-buffer" "~2.1.0"

"assert-plus@^1.0.0", "assert-plus@1.0.0":
  "integrity" "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw=="
  "resolved" "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz"
  "version" "1.0.0"

"async-function@^1.0.0":
  "integrity" "sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA=="
  "resolved" "https://registry.npmjs.org/async-function/-/async-function-1.0.0.tgz"
  "version" "1.0.0"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"available-typed-arrays@^1.0.7":
  "integrity" "sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ=="
  "resolved" "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "possible-typed-array-names" "^1.0.0"

"aws-sign2@~0.7.0":
  "integrity" "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA=="
  "resolved" "https://registry.npmjs.org/aws-sign2/-/aws-sign2-0.7.0.tgz"
  "version" "0.7.0"

"aws4@^1.8.0":
  "integrity" "sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw=="
  "resolved" "https://registry.npmjs.org/aws4/-/aws4-1.13.2.tgz"
  "version" "1.13.2"

"axios@^1.9.0":
  "integrity" "sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg=="
  "resolved" "https://registry.npmjs.org/axios/-/axios-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "follow-redirects" "^1.15.6"
    "form-data" "^4.0.0"
    "proxy-from-env" "^1.1.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base64id@~2.0.0", "base64id@2.0.0":
  "integrity" "sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog=="
  "resolved" "https://registry.npmjs.org/base64id/-/base64id-2.0.0.tgz"
  "version" "2.0.0"

"base64url@3.x.x":
  "integrity" "sha512-ir1UPr3dkwexU7FdV8qBBbNDRUhMmIekYMFZfi+C/sLNnRESKPl23nB9b2pltqfOQNnGzsDdId90AEtG5tCx4A=="
  "resolved" "https://registry.npmjs.org/base64url/-/base64url-3.0.1.tgz"
  "version" "3.0.1"

"bcrypt-pbkdf@^1.0.0":
  "integrity" "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w=="
  "resolved" "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "tweetnacl" "^0.14.3"

"bcryptjs@^2.4.3":
  "integrity" "sha512-V/Hy/X9Vt7f3BbPJEi8BdVFMByHi+jNXrYkW3huaybV/kQ0KJg0Y6PkEMbn+zeT+i+SiKZ/HMqJGIIt4LZDqNQ=="
  "resolved" "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.3.tgz"
  "version" "2.4.3"

"binary-extensions@^2.0.0":
  "integrity" "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw=="
  "resolved" "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  "version" "2.3.0"

"bl@^1.2.3":
  "integrity" "sha512-pvcNpa0UU69UT341rO6AYy4FVAIkUHuZXRIWbq+zHnsVcRzDDjIAhGuuYoi0d//cwIwtt4pkpKycWEfjdV+vww=="
  "resolved" "https://registry.npmjs.org/bl/-/bl-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "readable-stream" "^2.3.5"
    "safe-buffer" "^5.1.1"

"bluebird@^3.5.0":
  "integrity" "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="
  "resolved" "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"bn.js@^4.0.0":
  "integrity" "sha512-n4DSx829VRTRByMRGdjQ9iqsN0Bh4OolPsFnaZBLcbi8iXcB+kJ9s7EnRt4wILZNV3kPLHkRVfOc/HvhC3ovDw=="
  "resolved" "https://registry.npmjs.org/bn.js/-/bn.js-4.12.2.tgz"
  "version" "4.12.2"

"body-parser@1.20.3":
  "integrity" "sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g=="
  "resolved" "https://registry.npmjs.org/body-parser/-/body-parser-1.20.3.tgz"
  "version" "1.20.3"
  dependencies:
    "bytes" "3.1.2"
    "content-type" "~1.0.5"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "on-finished" "2.4.1"
    "qs" "6.13.0"
    "raw-body" "2.5.2"
    "type-is" "~1.6.18"
    "unpipe" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"braces@~3.0.2":
  "integrity" "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA=="
  "resolved" "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "fill-range" "^7.1.1"

"bson@^5.5.0":
  "integrity" "sha512-ix0EwukN2EpC0SRWIj/7B5+A6uQMQy6KMREI9qQqvgpkV2frH63T0UDVd1SYedL6dNCmDBYB3QtXi4ISk9YT+g=="
  "resolved" "https://registry.npmjs.org/bson/-/bson-5.5.1.tgz"
  "version" "5.5.1"

"bson@^6.10.4":
  "integrity" "sha512-WIsKqkSC0ABoBJuT1LEX+2HEvNmNKKgnTAyd0fL8qzK4SH2i9NXg+t08YtdZp/V9IZ33cxe3iV4yM0qg8lMQng=="
  "resolved" "https://registry.npmjs.org/bson/-/bson-6.10.4.tgz"
  "version" "6.10.4"

"buffer-equal-constant-time@^1.0.1":
  "integrity" "sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA=="
  "resolved" "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz"
  "version" "1.0.1"

"buffer-from@^1.0.0":
  "integrity" "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="
  "resolved" "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"busboy@^1.6.0":
  "integrity" "sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA=="
  "resolved" "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "streamsearch" "^1.1.0"

"bytes@3.1.2":
  "integrity" "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="
  "resolved" "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz"
  "version" "3.1.2"

"call-bind-apply-helpers@^1.0.0", "call-bind-apply-helpers@^1.0.1", "call-bind-apply-helpers@^1.0.2":
  "integrity" "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ=="
  "resolved" "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"

"call-bind@^1.0.7", "call-bind@^1.0.8":
  "integrity" "sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww=="
  "resolved" "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "call-bind-apply-helpers" "^1.0.0"
    "es-define-property" "^1.0.0"
    "get-intrinsic" "^1.2.4"
    "set-function-length" "^1.2.2"

"call-bound@^1.0.2", "call-bound@^1.0.3", "call-bound@^1.0.4":
  "integrity" "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg=="
  "resolved" "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind-apply-helpers" "^1.0.2"
    "get-intrinsic" "^1.3.0"

"caseless@~0.12.0":
  "integrity" "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw=="
  "resolved" "https://registry.npmjs.org/caseless/-/caseless-0.12.0.tgz"
  "version" "0.12.0"

"chokidar@^3.5.2":
  "integrity" "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw=="
  "resolved" "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"cloudinary-core@^2.13.0":
  "integrity" "sha512-z53GPNWnvU0Zi+ns8CIVbZBfj7ps/++zDvwIyiFuq5p1MoK+KUCg0k5mBceDDHTnx1gHmHUd9aohS+gDxPNt6w=="
  "resolved" "https://registry.npmjs.org/cloudinary-core/-/cloudinary-core-2.13.1.tgz"
  "version" "2.13.1"

"cloudinary@^1.40.0":
  "integrity" "sha512-4o84y+E7dbif3lMns+p3UW6w6hLHEifbX/7zBJvaih1E9QNMZITENQ14GPYJC4JmhygYXsuuBb9bRA3xWEoOfg=="
  "resolved" "https://registry.npmjs.org/cloudinary/-/cloudinary-1.41.3.tgz"
  "version" "1.41.3"
  dependencies:
    "cloudinary-core" "^2.13.0"
    "core-js" "^3.30.1"
    "lodash" "^4.17.21"
    "q" "^1.5.1"

"combined-stream@^1.0.6", "combined-stream@^1.0.8", "combined-stream@~1.0.6":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^2.0.0":
  "integrity" "sha512-MWufYdFw53ccGjCA+Ol7XJYpAlW6/prSMzuPOTRnJGcGzuhLn4Scrz7qf6o8bROZ514ltazcIFJZevcfbo0x7A=="
  "resolved" "https://registry.npmjs.org/concat-stream/-/concat-stream-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^3.0.2"
    "typedarray" "^0.0.6"

"connect-mongo@^5.1.0":
  "integrity" "sha512-xT0vxQLqyqoUTxPLzlP9a/u+vir0zNkhiy9uAdHjSCcUUf7TS5b55Icw8lVyYFxfemP3Mf9gdwUOgeF3cxCAhw=="
  "resolved" "https://registry.npmjs.org/connect-mongo/-/connect-mongo-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "debug" "^4.3.1"
    "kruptein" "^3.0.0"

"content-disposition@0.5.4":
  "integrity" "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ=="
  "resolved" "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz"
  "version" "0.5.4"
  dependencies:
    "safe-buffer" "5.2.1"

"content-type@~1.0.4", "content-type@~1.0.5":
  "integrity" "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA=="
  "resolved" "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz"
  "version" "1.0.5"

"cookie-parser@^1.4.6":
  "integrity" "sha512-nGUvgXnotP3BsjiLX2ypbQnWoGUPIIfHQNZkkC668ntrzGWEZVW70HDEB1qnNGMicPje6EttlIgzo51YSwNQGw=="
  "resolved" "https://registry.npmjs.org/cookie-parser/-/cookie-parser-1.4.7.tgz"
  "version" "1.4.7"
  dependencies:
    "cookie" "0.7.2"
    "cookie-signature" "1.0.6"

"cookie-signature@1.0.6":
  "integrity" "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ=="
  "resolved" "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie-signature@1.0.7":
  "integrity" "sha512-NXdYc3dLr47pBkpUCHtKSwIOQXLVn8dZEuywboCOJY/osA0wFSLlSawr3KN8qXJEyX66FcONTH8EIlVuK0yyFA=="
  "resolved" "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.7.tgz"
  "version" "1.0.7"

"cookie@~0.7.2", "cookie@0.7.2":
  "integrity" "sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w=="
  "resolved" "https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz"
  "version" "0.7.2"

"cookie@0.7.1":
  "integrity" "sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w=="
  "resolved" "https://registry.npmjs.org/cookie/-/cookie-0.7.1.tgz"
  "version" "0.7.1"

"core-js@^3.30.1":
  "integrity" "sha512-Sz4PP4ZA+Rq4II21qkNqOEDTDrCvcANId3xpIgB34NDkWc3UduWj2dqEtN9yZIq8Dk3HyPI33x9sqqU5C8sr0g=="
  "resolved" "https://registry.npmjs.org/core-js/-/core-js-3.42.0.tgz"
  "version" "3.42.0"

"core-util-is@~1.0.0":
  "integrity" "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ=="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"core-util-is@1.0.2":
  "integrity" "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ=="
  "resolved" "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz"
  "version" "1.0.2"

"cors@^2.8.5", "cors@~2.8.5":
  "integrity" "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g=="
  "resolved" "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz"
  "version" "2.8.5"
  dependencies:
    "object-assign" "^4"
    "vary" "^1"

"cron@^3.1.6":
  "integrity" "sha512-0eYZqCnapmxYcV06uktql93wNWdlTmmBFP2iYz+JPVcQqlyFYcn1lFuIk4R54pkOmE7mcldTAPZv6X5XA4Q46A=="
  "resolved" "https://registry.npmjs.org/cron/-/cron-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "@types/luxon" "~3.4.0"
    "luxon" "~3.5.0"

"cross-env@^7.0.3":
  "integrity" "sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw=="
  "resolved" "https://registry.npmjs.org/cross-env/-/cross-env-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "cross-spawn" "^7.0.1"

"cross-spawn@^7.0.1":
  "integrity" "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz"
  "version" "7.0.6"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"dashdash@^1.12.0":
  "integrity" "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g=="
  "resolved" "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "assert-plus" "^1.0.0"

"data-view-buffer@^1.0.2":
  "integrity" "sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ=="
  "resolved" "https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bound" "^1.0.3"
    "es-errors" "^1.3.0"
    "is-data-view" "^1.0.2"

"data-view-byte-length@^1.0.2":
  "integrity" "sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ=="
  "resolved" "https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bound" "^1.0.3"
    "es-errors" "^1.3.0"
    "is-data-view" "^1.0.2"

"data-view-byte-offset@^1.0.1":
  "integrity" "sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ=="
  "resolved" "https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "is-data-view" "^1.0.1"

"date-fns@^4.1.0":
  "integrity" "sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg=="
  "resolved" "https://registry.npmjs.org/date-fns/-/date-fns-4.1.0.tgz"
  "version" "4.1.0"

"debug@^3.2.7":
  "integrity" "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4", "debug@^4.3.1", "debug@4.x":
  "integrity" "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "ms" "^2.1.3"

"debug@~4.3.1":
  "integrity" "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  "version" "4.3.7"
  dependencies:
    "ms" "^2.1.3"

"debug@~4.3.2":
  "integrity" "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  "version" "4.3.7"
  dependencies:
    "ms" "^2.1.3"

"debug@~4.3.4":
  "integrity" "sha512-Er2nc/H7RrMXZBFCEim6TCmMk02Z8vLC2Rbi1KEBggpo0fS6l0S1nnapwmIi3yW/+GOJap1Krg4w0Hg80oCqgQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz"
  "version" "4.3.7"
  dependencies:
    "ms" "^2.1.3"

"debug@2.6.9":
  "integrity" "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"define-data-property@^1.0.1", "define-data-property@^1.1.4":
  "integrity" "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A=="
  "resolved" "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "gopd" "^1.0.1"

"define-properties@^1.2.1":
  "integrity" "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg=="
  "resolved" "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "define-data-property" "^1.0.1"
    "has-property-descriptors" "^1.0.0"
    "object-keys" "^1.1.1"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"depd@~2.0.0", "depd@2.0.0":
  "integrity" "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw=="
  "resolved" "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz"
  "version" "2.0.0"

"destroy@1.2.0":
  "integrity" "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg=="
  "resolved" "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz"
  "version" "1.2.0"

"discord-api-types@^0.38.1":
  "integrity" "sha512-XN0qhcQpetkyb/49hcDHuoeUPsQqOkb17wbV/t48gUkoEDi4ajhsxqugGcxvcN17BBtI9FPPWEgzv6IhQmCwyw=="
  "resolved" "https://registry.npmjs.org/discord-api-types/-/discord-api-types-0.38.11.tgz"
  "version" "0.38.11"

"discord.js@^14.19.3":
  "integrity" "sha512-lncTRk0k+8Q5D3nThnODBR8fR8x2fM798o8Vsr40Krx0DjPwpZCuxxTcFMrXMQVOqM1QB9wqWgaXPg3TbmlHqA=="
  "resolved" "https://registry.npmjs.org/discord.js/-/discord.js-14.19.3.tgz"
  "version" "14.19.3"
  dependencies:
    "@discordjs/builders" "^1.11.2"
    "@discordjs/collection" "1.5.3"
    "@discordjs/formatters" "^0.6.1"
    "@discordjs/rest" "^2.5.0"
    "@discordjs/util" "^1.1.1"
    "@discordjs/ws" "^1.2.2"
    "@sapphire/snowflake" "3.5.3"
    "discord-api-types" "^0.38.1"
    "fast-deep-equal" "3.1.3"
    "lodash.snakecase" "4.1.1"
    "magic-bytes.js" "^1.10.0"
    "tslib" "^2.6.3"
    "undici" "6.21.1"

"dotenv@^16.3.1":
  "integrity" "sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg=="
  "resolved" "https://registry.npmjs.org/dotenv/-/dotenv-16.5.0.tgz"
  "version" "16.5.0"

"dunder-proto@^1.0.0", "dunder-proto@^1.0.1":
  "integrity" "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A=="
  "resolved" "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind-apply-helpers" "^1.0.1"
    "es-errors" "^1.3.0"
    "gopd" "^1.2.0"

"ecc-jsbn@~0.1.1":
  "integrity" "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw=="
  "resolved" "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.1.0"

"ecdsa-sig-formatter@1.0.11":
  "integrity" "sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ=="
  "resolved" "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz"
  "version" "1.0.11"
  dependencies:
    "safe-buffer" "^5.0.1"

"ee-first@1.1.1":
  "integrity" "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow=="
  "resolved" "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"encodeurl@~1.0.2":
  "integrity" "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w=="
  "resolved" "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"encodeurl@~2.0.0":
  "integrity" "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg=="
  "resolved" "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz"
  "version" "2.0.0"

"end-of-stream@^1.1.0":
  "integrity" "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q=="
  "resolved" "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"engine.io-client@~6.6.1":
  "integrity" "sha512-T0iLjnyNWahNyv/lcjS2y4oE358tVS/SYQNxYXGAJ9/GLgH4VCvOQ/mhTjqU88mLZCQgiG8RIegFHYCdVC+j5w=="
  "resolved" "https://registry.npmjs.org/engine.io-client/-/engine.io-client-6.6.3.tgz"
  "version" "6.6.3"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    "debug" "~4.3.1"
    "engine.io-parser" "~5.2.1"
    "ws" "~8.17.1"
    "xmlhttprequest-ssl" "~2.1.1"

"engine.io-parser@~5.2.1":
  "integrity" "sha512-HqD3yTBfnBxIrbnM1DoD6Pcq8NECnh8d4As1Qgh0z5Gg3jRRIqijury0CL3ghu/edArpUYiYqQiDUQBIs4np3Q=="
  "resolved" "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-5.2.3.tgz"
  "version" "5.2.3"

"engine.io@~6.6.0":
  "integrity" "sha512-ZCkIjSYNDyGn0R6ewHDtXgns/Zre/NT6Agvq1/WobF7JXgFff4SeDroKiCO3fNJreU9YG429Sc81o4w5ok/W5g=="
  "resolved" "https://registry.npmjs.org/engine.io/-/engine.io-6.6.4.tgz"
  "version" "6.6.4"
  dependencies:
    "@types/cors" "^2.8.12"
    "@types/node" ">=10.0.0"
    "accepts" "~1.3.4"
    "base64id" "2.0.0"
    "cookie" "~0.7.2"
    "cors" "~2.8.5"
    "debug" "~4.3.1"
    "engine.io-parser" "~5.2.1"
    "ws" "~8.17.1"

"es-abstract@^1.23.5", "es-abstract@^1.23.6", "es-abstract@^1.23.9":
  "integrity" "sha512-WSzPgsdLtTcQwm4CROfS5ju2Wa1QQcVeT37jFjYzdFz1r9ahadC8B8/a4qxJxM+09F18iumCdRmlr96ZYkQvEg=="
  "resolved" "https://registry.npmjs.org/es-abstract/-/es-abstract-1.24.0.tgz"
  "version" "1.24.0"
  dependencies:
    "array-buffer-byte-length" "^1.0.2"
    "arraybuffer.prototype.slice" "^1.0.4"
    "available-typed-arrays" "^1.0.7"
    "call-bind" "^1.0.8"
    "call-bound" "^1.0.4"
    "data-view-buffer" "^1.0.2"
    "data-view-byte-length" "^1.0.2"
    "data-view-byte-offset" "^1.0.1"
    "es-define-property" "^1.0.1"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.1.1"
    "es-set-tostringtag" "^2.1.0"
    "es-to-primitive" "^1.3.0"
    "function.prototype.name" "^1.1.8"
    "get-intrinsic" "^1.3.0"
    "get-proto" "^1.0.1"
    "get-symbol-description" "^1.1.0"
    "globalthis" "^1.0.4"
    "gopd" "^1.2.0"
    "has-property-descriptors" "^1.0.2"
    "has-proto" "^1.2.0"
    "has-symbols" "^1.1.0"
    "hasown" "^2.0.2"
    "internal-slot" "^1.1.0"
    "is-array-buffer" "^3.0.5"
    "is-callable" "^1.2.7"
    "is-data-view" "^1.0.2"
    "is-negative-zero" "^2.0.3"
    "is-regex" "^1.2.1"
    "is-set" "^2.0.3"
    "is-shared-array-buffer" "^1.0.4"
    "is-string" "^1.1.1"
    "is-typed-array" "^1.1.15"
    "is-weakref" "^1.1.1"
    "math-intrinsics" "^1.1.0"
    "object-inspect" "^1.13.4"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.7"
    "own-keys" "^1.0.1"
    "regexp.prototype.flags" "^1.5.4"
    "safe-array-concat" "^1.1.3"
    "safe-push-apply" "^1.0.0"
    "safe-regex-test" "^1.1.0"
    "set-proto" "^1.0.0"
    "stop-iteration-iterator" "^1.1.0"
    "string.prototype.trim" "^1.2.10"
    "string.prototype.trimend" "^1.0.9"
    "string.prototype.trimstart" "^1.0.8"
    "typed-array-buffer" "^1.0.3"
    "typed-array-byte-length" "^1.0.3"
    "typed-array-byte-offset" "^1.0.4"
    "typed-array-length" "^1.0.7"
    "unbox-primitive" "^1.1.0"
    "which-typed-array" "^1.1.19"

"es-define-property@^1.0.0", "es-define-property@^1.0.1":
  "integrity" "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g=="
  "resolved" "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  "version" "1.0.1"

"es-errors@^1.3.0":
  "integrity" "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="
  "resolved" "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  "version" "1.3.0"

"es-object-atoms@^1.0.0", "es-object-atoms@^1.1.1":
  "integrity" "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA=="
  "resolved" "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "es-errors" "^1.3.0"

"es-set-tostringtag@^2.1.0":
  "integrity" "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA=="
  "resolved" "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.6"
    "has-tostringtag" "^1.0.2"
    "hasown" "^2.0.2"

"es-shim-unscopables@^1.0.2":
  "integrity" "sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw=="
  "resolved" "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "hasown" "^2.0.2"

"es-to-primitive@^1.3.0":
  "integrity" "sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g=="
  "resolved" "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "is-callable" "^1.2.7"
    "is-date-object" "^1.0.5"
    "is-symbol" "^1.0.4"

"escape-html@~1.0.3":
  "integrity" "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow=="
  "resolved" "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"etag@~1.8.1":
  "integrity" "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg=="
  "resolved" "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz"
  "version" "1.8.1"

"eventemitter3@^3.0.0":
  "integrity" "sha512-tvtQIeLVHjDkJYnzf2dgVMxfuSGJeM/7UCG17TT4EumTfNtF+0nebF/4zWOIkCreAbtNqhGEboB6BWrwqNaw4Q=="
  "resolved" "https://registry.npmjs.org/eventemitter3/-/eventemitter3-3.1.2.tgz"
  "version" "3.1.2"

"express-session@^1.17.1", "express-session@^1.18.1":
  "integrity" "sha512-a5mtTqEaZvBCL9A9aqkrtfz+3SMDhOVUnjafjo+s7A9Txkq+SVX2DLvSp1Zrv4uCXa3lMSK3viWnh9Gg07PBUA=="
  "resolved" "https://registry.npmjs.org/express-session/-/express-session-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "cookie" "0.7.2"
    "cookie-signature" "1.0.7"
    "debug" "2.6.9"
    "depd" "~2.0.0"
    "on-headers" "~1.0.2"
    "parseurl" "~1.3.3"
    "safe-buffer" "5.2.1"
    "uid-safe" "~2.1.5"

"express@^4.18.2":
  "integrity" "sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA=="
  "resolved" "https://registry.npmjs.org/express/-/express-4.21.2.tgz"
  "version" "4.21.2"
  dependencies:
    "accepts" "~1.3.8"
    "array-flatten" "1.1.1"
    "body-parser" "1.20.3"
    "content-disposition" "0.5.4"
    "content-type" "~1.0.4"
    "cookie" "0.7.1"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "encodeurl" "~2.0.0"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "1.3.1"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "merge-descriptors" "1.0.3"
    "methods" "~1.1.2"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.12"
    "proxy-addr" "~2.0.7"
    "qs" "6.13.0"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.2.1"
    "send" "0.19.0"
    "serve-static" "1.16.2"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend@~3.0.2":
  "integrity" "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g=="
  "resolved" "https://registry.npmjs.org/extend/-/extend-3.0.2.tgz"
  "version" "3.0.2"

"extsprintf@^1.2.0", "extsprintf@1.3.0":
  "integrity" "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g=="
  "resolved" "https://registry.npmjs.org/extsprintf/-/extsprintf-1.3.0.tgz"
  "version" "1.3.0"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3", "fast-deep-equal@3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"file-type@^3.9.0":
  "integrity" "sha512-RLoqTXE8/vPmMuTI88DAzhMYC99I8BWv7zYP4A1puo5HIjEJ5EX48ighy4ZyKMG9EDXxBgW6e++cn7d1xuFghA=="
  "resolved" "https://registry.npmjs.org/file-type/-/file-type-3.9.0.tgz"
  "version" "3.9.0"

"fill-range@^7.1.1":
  "integrity" "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg=="
  "resolved" "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  "version" "7.1.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@1.3.1":
  "integrity" "sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ=="
  "resolved" "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~2.0.0"
    "escape-html" "~1.0.3"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "statuses" "2.0.1"
    "unpipe" "~1.0.0"

"follow-redirects@^1.15.6":
  "integrity" "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz"
  "version" "1.15.9"

"for-each@^0.3.3", "for-each@^0.3.5":
  "integrity" "sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg=="
  "resolved" "https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz"
  "version" "0.3.5"
  dependencies:
    "is-callable" "^1.2.7"

"forever-agent@~0.6.1":
  "integrity" "sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw=="
  "resolved" "https://registry.npmjs.org/forever-agent/-/forever-agent-0.6.1.tgz"
  "version" "0.6.1"

"form-data@^4.0.0", "form-data@~4.0.0":
  "integrity" "sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "es-set-tostringtag" "^2.1.0"
    "mime-types" "^2.1.12"

"form-data@~2.3.2":
  "integrity" "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.6"
    "mime-types" "^2.1.12"

"forwarded@0.2.0":
  "integrity" "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow=="
  "resolved" "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz"
  "version" "0.2.0"

"fresh@0.5.2":
  "integrity" "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q=="
  "resolved" "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz"
  "version" "0.5.2"

"function-bind@^1.1.2":
  "integrity" "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"function.prototype.name@^1.1.6", "function.prototype.name@^1.1.8":
  "integrity" "sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q=="
  "resolved" "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.8.tgz"
  "version" "1.1.8"
  dependencies:
    "call-bind" "^1.0.8"
    "call-bound" "^1.0.3"
    "define-properties" "^1.2.1"
    "functions-have-names" "^1.2.3"
    "hasown" "^2.0.2"
    "is-callable" "^1.2.7"

"functions-have-names@^1.2.3":
  "integrity" "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ=="
  "resolved" "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
  "version" "1.2.3"

"get-intrinsic@^1.2.4", "get-intrinsic@^1.2.5", "get-intrinsic@^1.2.6", "get-intrinsic@^1.2.7", "get-intrinsic@^1.3.0":
  "integrity" "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ=="
  "resolved" "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "call-bind-apply-helpers" "^1.0.2"
    "es-define-property" "^1.0.1"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.1.1"
    "function-bind" "^1.1.2"
    "get-proto" "^1.0.1"
    "gopd" "^1.2.0"
    "has-symbols" "^1.1.0"
    "hasown" "^2.0.2"
    "math-intrinsics" "^1.1.0"

"get-proto@^1.0.0", "get-proto@^1.0.1":
  "integrity" "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g=="
  "resolved" "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "dunder-proto" "^1.0.1"
    "es-object-atoms" "^1.0.0"

"get-symbol-description@^1.1.0":
  "integrity" "sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg=="
  "resolved" "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "call-bound" "^1.0.3"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.6"

"getpass@^0.1.1":
  "integrity" "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng=="
  "resolved" "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz"
  "version" "0.1.7"
  dependencies:
    "assert-plus" "^1.0.0"

"glob-parent@~5.1.2":
  "integrity" "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"globalthis@^1.0.4":
  "integrity" "sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ=="
  "resolved" "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "define-properties" "^1.2.1"
    "gopd" "^1.0.1"

"gopd@^1.0.1", "gopd@^1.2.0":
  "integrity" "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="
  "resolved" "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  "version" "1.2.0"

"har-schema@^2.0.0":
  "integrity" "sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q=="
  "resolved" "https://registry.npmjs.org/har-schema/-/har-schema-2.0.0.tgz"
  "version" "2.0.0"

"har-validator@~5.1.3":
  "integrity" "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w=="
  "resolved" "https://registry.npmjs.org/har-validator/-/har-validator-5.1.5.tgz"
  "version" "5.1.5"
  dependencies:
    "ajv" "^6.12.3"
    "har-schema" "^2.0.0"

"has-bigints@^1.0.2":
  "integrity" "sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg=="
  "resolved" "https://registry.npmjs.org/has-bigints/-/has-bigints-1.1.0.tgz"
  "version" "1.1.0"

"has-flag@^3.0.0":
  "integrity" "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-property-descriptors@^1.0.0", "has-property-descriptors@^1.0.2":
  "integrity" "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg=="
  "resolved" "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "es-define-property" "^1.0.0"

"has-proto@^1.2.0":
  "integrity" "sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ=="
  "resolved" "https://registry.npmjs.org/has-proto/-/has-proto-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "dunder-proto" "^1.0.0"

"has-symbols@^1.0.3", "has-symbols@^1.1.0":
  "integrity" "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ=="
  "resolved" "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  "version" "1.1.0"

"has-tostringtag@^1.0.2":
  "integrity" "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw=="
  "resolved" "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "has-symbols" "^1.0.3"

"hasown@^2.0.2":
  "integrity" "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ=="
  "resolved" "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "function-bind" "^1.1.2"

"http-errors@2.0.0":
  "integrity" "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ=="
  "resolved" "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "depd" "2.0.0"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "toidentifier" "1.0.1"

"http-signature@~1.2.0":
  "integrity" "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ=="
  "resolved" "https://registry.npmjs.org/http-signature/-/http-signature-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^1.2.2"
    "sshpk" "^1.7.0"

"http-signature@~1.4.0":
  "integrity" "sha512-G5akfn7eKbpDN+8nPS/cb57YeA1jLTVxjpCj7tmm3QKPdyDy7T+qSC40e9ptydSWvkwjSXw1VbkpyEm39ukeAg=="
  "resolved" "https://registry.npmjs.org/http-signature/-/http-signature-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "jsprim" "^2.0.2"
    "sshpk" "^1.18.0"

"iconv-lite@0.4.24":
  "integrity" "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA=="
  "resolved" "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"ignore-by-default@^1.0.1":
  "integrity" "sha512-Ius2VYcGNk7T90CppJqcIkS5ooHUZyIQK+ClZfMfMNFEF9VSE73Fq+906u/CWu92x4gzZMWOwfFYckPObzdEbA=="
  "resolved" "https://registry.npmjs.org/ignore-by-default/-/ignore-by-default-1.0.1.tgz"
  "version" "1.0.1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@~2.0.3", "inherits@2.0.4":
  "integrity" "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="
  "resolved" "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  "version" "2.0.4"

"internal-slot@^1.1.0":
  "integrity" "sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw=="
  "resolved" "https://registry.npmjs.org/internal-slot/-/internal-slot-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "es-errors" "^1.3.0"
    "hasown" "^2.0.2"
    "side-channel" "^1.1.0"

"ip-address@^9.0.5":
  "integrity" "sha512-zHtQzGojZXTwZTHQqra+ETKd4Sn3vgi7uBmlPoXVWZqYvuKmtI0l/VZTjqGmJY9x88GGOaZ9+G9ES8hC4T4X8g=="
  "resolved" "https://registry.npmjs.org/ip-address/-/ip-address-9.0.5.tgz"
  "version" "9.0.5"
  dependencies:
    "jsbn" "1.1.0"
    "sprintf-js" "^1.1.3"

"ipaddr.js@1.9.1":
  "integrity" "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="
  "resolved" "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-array-buffer@^3.0.4", "is-array-buffer@^3.0.5":
  "integrity" "sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A=="
  "resolved" "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz"
  "version" "3.0.5"
  dependencies:
    "call-bind" "^1.0.8"
    "call-bound" "^1.0.3"
    "get-intrinsic" "^1.2.6"

"is-async-function@^2.0.0":
  "integrity" "sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ=="
  "resolved" "https://registry.npmjs.org/is-async-function/-/is-async-function-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "async-function" "^1.0.0"
    "call-bound" "^1.0.3"
    "get-proto" "^1.0.1"
    "has-tostringtag" "^1.0.2"
    "safe-regex-test" "^1.1.0"

"is-bigint@^1.1.0":
  "integrity" "sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ=="
  "resolved" "https://registry.npmjs.org/is-bigint/-/is-bigint-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "has-bigints" "^1.0.2"

"is-binary-path@~2.1.0":
  "integrity" "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw=="
  "resolved" "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-boolean-object@^1.2.1":
  "integrity" "sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A=="
  "resolved" "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "call-bound" "^1.0.3"
    "has-tostringtag" "^1.0.2"

"is-callable@^1.2.7":
  "integrity" "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA=="
  "resolved" "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
  "version" "1.2.7"

"is-data-view@^1.0.1", "is-data-view@^1.0.2":
  "integrity" "sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw=="
  "resolved" "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bound" "^1.0.2"
    "get-intrinsic" "^1.2.6"
    "is-typed-array" "^1.1.13"

"is-date-object@^1.0.5", "is-date-object@^1.1.0":
  "integrity" "sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg=="
  "resolved" "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "call-bound" "^1.0.2"
    "has-tostringtag" "^1.0.2"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-finalizationregistry@^1.1.0":
  "integrity" "sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg=="
  "resolved" "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bound" "^1.0.3"

"is-generator-function@^1.0.10":
  "integrity" "sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ=="
  "resolved" "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "call-bound" "^1.0.3"
    "get-proto" "^1.0.0"
    "has-tostringtag" "^1.0.2"
    "safe-regex-test" "^1.1.0"

"is-glob@^4.0.1", "is-glob@~4.0.1":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-map@^2.0.3":
  "integrity" "sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw=="
  "resolved" "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz"
  "version" "2.0.3"

"is-negative-zero@^2.0.3":
  "integrity" "sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw=="
  "resolved" "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.3.tgz"
  "version" "2.0.3"

"is-number-object@^1.1.1":
  "integrity" "sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw=="
  "resolved" "https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bound" "^1.0.3"
    "has-tostringtag" "^1.0.2"

"is-number@^7.0.0":
  "integrity" "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng=="
  "resolved" "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-regex@^1.2.1":
  "integrity" "sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g=="
  "resolved" "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "call-bound" "^1.0.2"
    "gopd" "^1.2.0"
    "has-tostringtag" "^1.0.2"
    "hasown" "^2.0.2"

"is-set@^2.0.3":
  "integrity" "sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg=="
  "resolved" "https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz"
  "version" "2.0.3"

"is-shared-array-buffer@^1.0.4":
  "integrity" "sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A=="
  "resolved" "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bound" "^1.0.3"

"is-string@^1.1.1":
  "integrity" "sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA=="
  "resolved" "https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bound" "^1.0.3"
    "has-tostringtag" "^1.0.2"

"is-symbol@^1.0.4", "is-symbol@^1.1.1":
  "integrity" "sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w=="
  "resolved" "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bound" "^1.0.2"
    "has-symbols" "^1.1.0"
    "safe-regex-test" "^1.1.0"

"is-typed-array@^1.1.13", "is-typed-array@^1.1.14", "is-typed-array@^1.1.15":
  "integrity" "sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ=="
  "resolved" "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz"
  "version" "1.1.15"
  dependencies:
    "which-typed-array" "^1.1.16"

"is-typedarray@~1.0.0":
  "integrity" "sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA=="
  "resolved" "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-weakmap@^2.0.2":
  "integrity" "sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w=="
  "resolved" "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz"
  "version" "2.0.2"

"is-weakref@^1.0.2", "is-weakref@^1.1.1":
  "integrity" "sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew=="
  "resolved" "https://registry.npmjs.org/is-weakref/-/is-weakref-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bound" "^1.0.3"

"is-weakset@^2.0.3":
  "integrity" "sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ=="
  "resolved" "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "call-bound" "^1.0.3"
    "get-intrinsic" "^1.2.6"

"isarray@^2.0.5":
  "integrity" "sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz"
  "version" "2.0.5"

"isarray@~1.0.0":
  "integrity" "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ=="
  "resolved" "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isstream@~0.1.2":
  "integrity" "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g=="
  "resolved" "https://registry.npmjs.org/isstream/-/isstream-0.1.2.tgz"
  "version" "0.1.2"

"jsbn@~0.1.0":
  "integrity" "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg=="
  "resolved" "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz"
  "version" "0.1.1"

"jsbn@1.1.0":
  "integrity" "sha512-4bYVV3aAMtDTTu4+xsDYa6sy9GyJ69/amsu9sYF2zqjiEoZA5xJi3BrfX3uY+/IekIu7MwdObdbDWpoZdBv3/A=="
  "resolved" "https://registry.npmjs.org/jsbn/-/jsbn-1.1.0.tgz"
  "version" "1.1.0"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema@0.4.0":
  "integrity" "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA=="
  "resolved" "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz"
  "version" "0.4.0"

"json-stringify-safe@~5.0.1":
  "integrity" "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA=="
  "resolved" "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz"
  "version" "5.0.1"

"jsonwebtoken@^9.0.1":
  "integrity" "sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ=="
  "resolved" "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz"
  "version" "9.0.2"
  dependencies:
    "jws" "^3.2.2"
    "lodash.includes" "^4.3.0"
    "lodash.isboolean" "^3.0.3"
    "lodash.isinteger" "^4.0.4"
    "lodash.isnumber" "^3.0.3"
    "lodash.isplainobject" "^4.0.6"
    "lodash.isstring" "^4.0.1"
    "lodash.once" "^4.0.0"
    "ms" "^2.1.1"
    "semver" "^7.5.4"

"jsprim@^1.2.2":
  "integrity" "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw=="
  "resolved" "https://registry.npmjs.org/jsprim/-/jsprim-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.4.0"
    "verror" "1.10.0"

"jsprim@^2.0.2":
  "integrity" "sha512-gqXddjPqQ6G40VdnI6T6yObEC+pDNvyP95wdQhkWkg7crHH3km5qP1FsOXEkzEQwnz6gz5qGTn1c2Y52wP3OyQ=="
  "resolved" "https://registry.npmjs.org/jsprim/-/jsprim-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "assert-plus" "1.0.0"
    "extsprintf" "1.3.0"
    "json-schema" "0.4.0"
    "verror" "1.10.0"

"jwa@^1.4.1":
  "integrity" "sha512-eeH5JO+21J78qMvTIDdBXidBd6nG2kZjg5Ohz/1fpa28Z4CcsWUzJ1ZZyFq/3z3N17aZy+ZuBoHljASbL1WfOw=="
  "resolved" "https://registry.npmjs.org/jwa/-/jwa-1.4.2.tgz"
  "version" "1.4.2"
  dependencies:
    "buffer-equal-constant-time" "^1.0.1"
    "ecdsa-sig-formatter" "1.0.11"
    "safe-buffer" "^5.0.1"

"jws@^3.2.2":
  "integrity" "sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA=="
  "resolved" "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "jwa" "^1.4.1"
    "safe-buffer" "^5.0.1"

"kareem@2.5.1":
  "integrity" "sha512-7jFxRVm+jD+rkq3kY0iZDJfsO2/t4BBPeEb2qKn2lR/9KhuksYk5hxzfRYWMPV8P/x2d0kHD306YyWLzjjH+uA=="
  "resolved" "https://registry.npmjs.org/kareem/-/kareem-2.5.1.tgz"
  "version" "2.5.1"

"kruptein@^3.0.0":
  "integrity" "sha512-vTftnEjfbqFHLqxDUMQCj6gBo5lKqjV4f0JsM8rk8rM3xmvFZ2eSy4YALdaye7E+cDKnEj7eAjFR3vwh8a4PgQ=="
  "resolved" "https://registry.npmjs.org/kruptein/-/kruptein-3.0.7.tgz"
  "version" "3.0.7"
  dependencies:
    "asn1.js" "^5.4.1"

"lodash.includes@^4.3.0":
  "integrity" "sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w=="
  "resolved" "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz"
  "version" "4.3.0"

"lodash.isboolean@^3.0.3":
  "integrity" "sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg=="
  "resolved" "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz"
  "version" "3.0.3"

"lodash.isinteger@^4.0.4":
  "integrity" "sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA=="
  "resolved" "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz"
  "version" "4.0.4"

"lodash.isnumber@^3.0.3":
  "integrity" "sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw=="
  "resolved" "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz"
  "version" "3.0.3"

"lodash.isplainobject@^4.0.6":
  "integrity" "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA=="
  "resolved" "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  "version" "4.0.6"

"lodash.isstring@^4.0.1":
  "integrity" "sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw=="
  "resolved" "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz"
  "version" "4.0.1"

"lodash.once@^4.0.0":
  "integrity" "sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg=="
  "resolved" "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz"
  "version" "4.1.1"

"lodash.snakecase@4.1.1":
  "integrity" "sha512-QZ1d4xoBHYUeuouhEq3lk3Uq7ldgyFXGBhg04+oRLnIz8o9T65Eh+8YdroUwn846zchkA9yDsDl5CVVaV2nqYw=="
  "resolved" "https://registry.npmjs.org/lodash.snakecase/-/lodash.snakecase-4.1.1.tgz"
  "version" "4.1.1"

"lodash@^4.17.15", "lodash@^4.17.21", "lodash@>=4.0":
  "integrity" "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg=="
  "resolved" "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  "version" "4.17.21"

"luxon@~3.5.0":
  "integrity" "sha512-rh+Zjr6DNfUYR3bPwJEnuwDdqMbxZW7LOQfUN4B54+Cl+0o5zaU9RJ6bcidfDtC1cWCZXQ+nvX8bf6bAji37QQ=="
  "resolved" "https://registry.npmjs.org/luxon/-/luxon-3.5.0.tgz"
  "version" "3.5.0"

"magic-bytes.js@^1.10.0":
  "integrity" "sha512-ThQLOhN86ZkJ7qemtVRGYM+gRgR8GEXNli9H/PMvpnZsE44Xfh3wx9kGJaldg314v85m+bFW6WBMaVHJc/c3zA=="
  "resolved" "https://registry.npmjs.org/magic-bytes.js/-/magic-bytes.js-1.12.1.tgz"
  "version" "1.12.1"

"math-intrinsics@^1.1.0":
  "integrity" "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g=="
  "resolved" "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  "version" "1.1.0"

"media-typer@0.3.0":
  "integrity" "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ=="
  "resolved" "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memory-pager@^1.0.2":
  "integrity" "sha512-ZS4Bp4r/Zoeq6+NLJpP+0Zzm0pR8whtGPf1XExKLJBAczGMnSi3It14OiNCStjQjM6NU1okjQGSxgEZN8eBYKg=="
  "resolved" "https://registry.npmjs.org/memory-pager/-/memory-pager-1.5.0.tgz"
  "version" "1.5.0"

"merge-descriptors@1.0.3":
  "integrity" "sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ=="
  "resolved" "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.3.tgz"
  "version" "1.0.3"

"methods@~1.1.2":
  "integrity" "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w=="
  "resolved" "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz"
  "version" "1.1.2"

"mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12", "mime-types@~2.1.19", "mime-types@~2.1.24", "mime-types@~2.1.34":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime@^1.6.0", "mime@1.6.0":
  "integrity" "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="
  "resolved" "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz"
  "version" "1.6.0"

"minimalistic-assert@^1.0.0":
  "integrity" "sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A=="
  "resolved" "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.1.2":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimist@^1.2.6":
  "integrity" "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA=="
  "resolved" "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  "version" "1.2.8"

"mkdirp@^0.5.6":
  "integrity" "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw=="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "minimist" "^1.2.6"

"mongodb-connection-string-url@^2.6.0":
  "integrity" "sha512-WvTZlI9ab0QYtTYnuMLgobULWhokRjtC7db9LtcVfJ+Hsnyr5eo6ZtNAt3Ly24XZScGMelOcGtm7lSn0332tPQ=="
  "resolved" "https://registry.npmjs.org/mongodb-connection-string-url/-/mongodb-connection-string-url-2.6.0.tgz"
  "version" "2.6.0"
  dependencies:
    "@types/whatwg-url" "^8.2.1"
    "whatwg-url" "^11.0.0"

"mongodb-connection-string-url@^3.0.0":
  "integrity" "sha512-rMO7CGo/9BFwyZABcKAWL8UJwH/Kc2x0g72uhDWzG48URRax5TCIcJ7Rc3RZqffZzO/Gwff/jyKwCU9TN8gehA=="
  "resolved" "https://registry.npmjs.org/mongodb-connection-string-url/-/mongodb-connection-string-url-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "@types/whatwg-url" "^11.0.2"
    "whatwg-url" "^14.1.0 || ^13.0.0"

"mongodb@>= 5.1.0 < 7":
  "integrity" "sha512-neerUzg/8U26cgruLysKEjJvoNSXhyID3RvzvdcpsIi2COYM3FS3o9nlH7fxFtefTb942dX3W9i37oPfCVj4wA=="
  "resolved" "https://registry.npmjs.org/mongodb/-/mongodb-6.17.0.tgz"
  "version" "6.17.0"
  dependencies:
    "@mongodb-js/saslprep" "^1.1.9"
    "bson" "^6.10.4"
    "mongodb-connection-string-url" "^3.0.0"

"mongodb@5.9.2":
  "integrity" "sha512-H60HecKO4Bc+7dhOv4sJlgvenK4fQNqqUIlXxZYQNbfEWSALGAwGoyJd/0Qwk4TttFXUOHJ2ZJQe/52ScaUwtQ=="
  "resolved" "https://registry.npmjs.org/mongodb/-/mongodb-5.9.2.tgz"
  "version" "5.9.2"
  dependencies:
    "bson" "^5.5.0"
    "mongodb-connection-string-url" "^2.6.0"
    "socks" "^2.7.1"
  optionalDependencies:
    "@mongodb-js/saslprep" "^1.1.0"

"mongoose@^7.4.0":
  "integrity" "sha512-5Bo4CrUxrPITrhMKsqUTOkXXo2CoRC5tXxVQhnddCzqDMwRXfyStrxj1oY865g8gaekSBhxAeNkYyUSJvGm9Hw=="
  "resolved" "https://registry.npmjs.org/mongoose/-/mongoose-7.8.7.tgz"
  "version" "7.8.7"
  dependencies:
    "bson" "^5.5.0"
    "kareem" "2.5.1"
    "mongodb" "5.9.2"
    "mpath" "0.9.0"
    "mquery" "5.0.0"
    "ms" "2.1.3"
    "sift" "16.0.1"

"mpath@0.9.0":
  "integrity" "sha512-ikJRQTk8hw5DEoFVxHG1Gn9T/xcjtdnOKIU1JTmGjZZlg9LST2mBLmcX3/ICIbgJydT2GOc15RnNy5mHmzfSew=="
  "resolved" "https://registry.npmjs.org/mpath/-/mpath-0.9.0.tgz"
  "version" "0.9.0"

"mquery@5.0.0":
  "integrity" "sha512-iQMncpmEK8R8ncT8HJGsGc9Dsp8xcgYMVSbs5jgnm1lFHTZqMJTUWTDx1LBO8+mK3tPNZWFLBghQEIOULSTHZg=="
  "resolved" "https://registry.npmjs.org/mquery/-/mquery-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "debug" "4.x"

"ms@^2.1.1", "ms@^2.1.3", "ms@2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"ms@2.0.0":
  "integrity" "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  "version" "2.0.0"

"multer@^2.0.2":
  "integrity" "sha512-u7f2xaZ/UG8oLXHvtF/oWTRvT44p9ecwBBqTwgJVq0+4BW1g8OW01TyMEGWBHbyMOYVHXslaut7qEQ1meATXgw=="
  "resolved" "https://registry.npmjs.org/multer/-/multer-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "append-field" "^1.0.0"
    "busboy" "^1.6.0"
    "concat-stream" "^2.0.0"
    "mkdirp" "^0.5.6"
    "object-assign" "^4.1.1"
    "type-is" "^1.6.18"
    "xtend" "^4.0.2"

"negotiator@0.6.3":
  "integrity" "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg=="
  "resolved" "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz"
  "version" "0.6.3"

"node-telegram-bot-api@^0.66.0":
  "integrity" "sha512-s4Hrg5q+VPl4/tJVG++pImxF6eb8tNJNj4KnDqAOKL6zGU34lo9RXmyAN158njwGN+v8hdNf8s9fWIYW9hPb5A=="
  "resolved" "https://registry.npmjs.org/node-telegram-bot-api/-/node-telegram-bot-api-0.66.0.tgz"
  "version" "0.66.0"
  dependencies:
    "@cypress/request" "^3.0.1"
    "@cypress/request-promise" "^5.0.0"
    "array.prototype.findindex" "^2.0.2"
    "bl" "^1.2.3"
    "debug" "^3.2.7"
    "eventemitter3" "^3.0.0"
    "file-type" "^3.9.0"
    "mime" "^1.6.0"
    "pump" "^2.0.0"

"nodemon@^3.0.1":
  "integrity" "sha512-WDjw3pJ0/0jMFmyNDp3gvY2YizjLmmOUQo6DEBY+JgdvW/yQ9mEeSw6H5ythl5Ny2ytb7f9C2nIbjSxMNzbJXw=="
  "resolved" "https://registry.npmjs.org/nodemon/-/nodemon-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "chokidar" "^3.5.2"
    "debug" "^4"
    "ignore-by-default" "^1.0.1"
    "minimatch" "^3.1.2"
    "pstree.remy" "^1.1.8"
    "semver" "^7.5.3"
    "simple-update-notifier" "^2.0.0"
    "supports-color" "^5.5.0"
    "touch" "^3.1.0"
    "undefsafe" "^2.0.5"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA=="
  "resolved" "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"oauth-sign@~0.9.0":
  "integrity" "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ=="
  "resolved" "https://registry.npmjs.org/oauth-sign/-/oauth-sign-0.9.0.tgz"
  "version" "0.9.0"

"oauth@0.10.x":
  "integrity" "sha512-JtFnB+8nxDEXgNyniwz573xxbKSOu3R8D40xQKqcjwJ2CDkYqUDI53o6IuzDJBx60Z8VKCm271+t8iFjakrl8Q=="
  "resolved" "https://registry.npmjs.org/oauth/-/oauth-0.10.2.tgz"
  "version" "0.10.2"

"object-assign@^4", "object-assign@^4.1.1":
  "integrity" "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg=="
  "resolved" "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-inspect@^1.13.3", "object-inspect@^1.13.4":
  "integrity" "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew=="
  "resolved" "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz"
  "version" "1.13.4"

"object-keys@^1.1.1":
  "integrity" "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA=="
  "resolved" "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object.assign@^4.1.7":
  "integrity" "sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw=="
  "resolved" "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz"
  "version" "4.1.7"
  dependencies:
    "call-bind" "^1.0.8"
    "call-bound" "^1.0.3"
    "define-properties" "^1.2.1"
    "es-object-atoms" "^1.0.0"
    "has-symbols" "^1.1.0"
    "object-keys" "^1.1.1"

"on-finished@2.4.1":
  "integrity" "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg=="
  "resolved" "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA=="
  "resolved" "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.1", "once@^1.4.0":
  "integrity" "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w=="
  "resolved" "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"own-keys@^1.0.1":
  "integrity" "sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg=="
  "resolved" "https://registry.npmjs.org/own-keys/-/own-keys-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-intrinsic" "^1.2.6"
    "object-keys" "^1.1.1"
    "safe-push-apply" "^1.0.0"

"parseurl@~1.3.3":
  "integrity" "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="
  "resolved" "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"passport-google-oauth20@^2.0.0":
  "integrity" "sha512-KSk6IJ15RoxuGq7D1UKK/8qKhNfzbLeLrG3gkLZ7p4A6DBCcv7xpyQwuXtWdpyR0+E0mwkpjY1VfPOhxQrKzdQ=="
  "resolved" "https://registry.npmjs.org/passport-google-oauth20/-/passport-google-oauth20-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "passport-oauth2" "1.x.x"

"passport-oauth2@1.x.x":
  "integrity" "sha512-cjsQbOrXIDE4P8nNb3FQRCCmJJ/utnFKEz2NX209f7KOHPoX18gF7gBzBbLLsj2/je4KrgiwLLGjf0lm9rtTBA=="
  "resolved" "https://registry.npmjs.org/passport-oauth2/-/passport-oauth2-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "base64url" "3.x.x"
    "oauth" "0.10.x"
    "passport-strategy" "1.x.x"
    "uid2" "0.0.x"
    "utils-merge" "1.x.x"

"passport-strategy@1.x.x":
  "integrity" "sha512-CB97UUvDKJde2V0KDWWB3lyf6PC3FaZP7YxZ2G8OAtn9p4HI9j9JLP9qjOGZFvyl8uwNT8qM+hGnz/n16NI7oA=="
  "resolved" "https://registry.npmjs.org/passport-strategy/-/passport-strategy-1.0.0.tgz"
  "version" "1.0.0"

"passport@^0.7.0":
  "integrity" "sha512-cPLl+qZpSc+ireUvt+IzqbED1cHHkDoVYMo30jbJIdOOjQ1MQYZBPiNvmi8UM6lJuOpTPXJGZQk0DtC4y61MYQ=="
  "resolved" "https://registry.npmjs.org/passport/-/passport-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "passport-strategy" "1.x.x"
    "pause" "0.0.1"
    "utils-merge" "^1.0.1"

"path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-to-regexp@0.1.12":
  "integrity" "sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ=="
  "resolved" "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.12.tgz"
  "version" "0.1.12"

"pause@0.0.1":
  "integrity" "sha512-KG8UEiEVkR3wGEb4m5yZkVCzigAD+cVEJck2CzYZO37ZGJfctvVptVO192MwrtPhzONn6go8ylnOdMhKqi4nfg=="
  "resolved" "https://registry.npmjs.org/pause/-/pause-0.0.1.tgz"
  "version" "0.0.1"

"performance-now@^2.1.0":
  "integrity" "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="
  "resolved" "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  "version" "2.1.0"

"picomatch@^2.0.4", "picomatch@^2.2.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"possible-typed-array-names@^1.0.0":
  "integrity" "sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg=="
  "resolved" "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz"
  "version" "1.1.0"

"process-nextick-args@~2.0.0":
  "integrity" "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="
  "resolved" "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"proxy-addr@~2.0.7":
  "integrity" "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg=="
  "resolved" "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"proxy-from-env@^1.1.0":
  "integrity" "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="
  "resolved" "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  "version" "1.1.0"

"psl@^1.1.28", "psl@^1.1.33":
  "integrity" "sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w=="
  "resolved" "https://registry.npmjs.org/psl/-/psl-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "punycode" "^2.3.1"

"pstree.remy@^1.1.8":
  "integrity" "sha512-77DZwxQmxKnu3aR542U+X8FypNzbfJ+C5XQDk3uWjWxn6151aIMGthWYRXTqT1E5oJvg+ljaa2OJi+VfvCOQ8w=="
  "resolved" "https://registry.npmjs.org/pstree.remy/-/pstree.remy-1.1.8.tgz"
  "version" "1.1.8"

"pump@^2.0.0":
  "integrity" "sha512-ruPMNRkN3MHP1cWJc9OWr+T/xDP0jhXYCLfJcBuX54hhfIBnaQmAUMfDcG4DM5UMWByBbJY69QSphm3jtDKIkA=="
  "resolved" "https://registry.npmjs.org/pump/-/pump-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"punycode@^2.1.0", "punycode@^2.1.1", "punycode@^2.3.1":
  "integrity" "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  "version" "2.3.1"

"q@^1.5.1":
  "integrity" "sha512-kV/CThkXo6xyFEZUugw/+pIOywXcDbFYgSct5cT3gqlbkBE1SJdwy6UQoZvodiWF/ckQLZyDE/Bu1M6gVu5lVw=="
  "resolved" "https://registry.npmjs.org/q/-/q-1.5.1.tgz"
  "version" "1.5.1"

"qs@~6.5.2":
  "integrity" "sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA=="
  "resolved" "https://registry.npmjs.org/qs/-/qs-6.5.3.tgz"
  "version" "6.5.3"

"qs@6.13.0":
  "integrity" "sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg=="
  "resolved" "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz"
  "version" "6.13.0"
  dependencies:
    "side-channel" "^1.0.6"

"qs@6.14.0":
  "integrity" "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w=="
  "resolved" "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz"
  "version" "6.14.0"
  dependencies:
    "side-channel" "^1.1.0"

"querystringify@^2.1.1":
  "integrity" "sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ=="
  "resolved" "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz"
  "version" "2.2.0"

"random-bytes@~1.0.0":
  "integrity" "sha512-iv7LhNVO047HzYR3InF6pUcUsPQiHTM1Qal51DcGSuZFBil1aBBWG5eHPNek7bvILMaYJ/8RU1e8w1AMdHmLQQ=="
  "resolved" "https://registry.npmjs.org/random-bytes/-/random-bytes-1.0.0.tgz"
  "version" "1.0.0"

"range-parser@~1.2.1":
  "integrity" "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="
  "resolved" "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.5.2":
  "integrity" "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA=="
  "resolved" "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "bytes" "3.1.2"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"react-icons@^5.5.0":
  "integrity" "sha512-MEFcXdkP3dLo8uumGI5xN3lDFNsRtrjbOEKDLD7yv76v4wpnEq2Lt2qeHaQOr34I/wPN3s3+N08WkQ+CW37Xiw=="
  "resolved" "https://registry.npmjs.org/react-icons/-/react-icons-5.5.0.tgz"
  "version" "5.5.0"

"react@*":
  "integrity" "sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg=="
  "resolved" "https://registry.npmjs.org/react/-/react-19.1.0.tgz"
  "version" "19.1.0"

"readable-stream@^2.3.5":
  "integrity" "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.2":
  "integrity" "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA=="
  "resolved" "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@~3.6.0":
  "integrity" "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA=="
  "resolved" "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"reflect.getprototypeof@^1.0.6", "reflect.getprototypeof@^1.0.9":
  "integrity" "sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw=="
  "resolved" "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "call-bind" "^1.0.8"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.9"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.0.0"
    "get-intrinsic" "^1.2.7"
    "get-proto" "^1.0.1"
    "which-builtin-type" "^1.2.1"

"regexp.prototype.flags@^1.5.4":
  "integrity" "sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA=="
  "resolved" "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz"
  "version" "1.5.4"
  dependencies:
    "call-bind" "^1.0.8"
    "define-properties" "^1.2.1"
    "es-errors" "^1.3.0"
    "get-proto" "^1.0.1"
    "gopd" "^1.2.0"
    "set-function-name" "^2.0.2"

"request-promise-core@1.1.3":
  "integrity" "sha512-QIs2+ArIGQVp5ZYbWD5ZLCY29D5CfWizP8eWnm8FoGD1TX61veauETVQbrV60662V0oFBkrDOuaBI8XgtuyYAQ=="
  "resolved" "https://registry.npmjs.org/request-promise-core/-/request-promise-core-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "lodash" "^4.17.15"

"request@^2.34":
  "integrity" "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw=="
  "resolved" "https://registry.npmjs.org/request/-/request-2.88.2.tgz"
  "version" "2.88.2"
  dependencies:
    "aws-sign2" "~0.7.0"
    "aws4" "^1.8.0"
    "caseless" "~0.12.0"
    "combined-stream" "~1.0.6"
    "extend" "~3.0.2"
    "forever-agent" "~0.6.1"
    "form-data" "~2.3.2"
    "har-validator" "~5.1.3"
    "http-signature" "~1.2.0"
    "is-typedarray" "~1.0.0"
    "isstream" "~0.1.2"
    "json-stringify-safe" "~5.0.1"
    "mime-types" "~2.1.19"
    "oauth-sign" "~0.9.0"
    "performance-now" "^2.1.0"
    "qs" "~6.5.2"
    "safe-buffer" "^5.1.2"
    "tough-cookie" "~2.5.0"
    "tunnel-agent" "^0.6.0"
    "uuid" "^3.3.2"

"requires-port@^1.0.0":
  "integrity" "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ=="
  "resolved" "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"safe-array-concat@^1.1.3":
  "integrity" "sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q=="
  "resolved" "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "call-bind" "^1.0.8"
    "call-bound" "^1.0.2"
    "get-intrinsic" "^1.2.6"
    "has-symbols" "^1.1.0"
    "isarray" "^2.0.5"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.1", "safe-buffer@^5.1.2", "safe-buffer@5.2.1":
  "integrity" "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@~5.1.0":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@~5.1.1":
  "integrity" "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="
  "resolved" "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-push-apply@^1.0.0":
  "integrity" "sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA=="
  "resolved" "https://registry.npmjs.org/safe-push-apply/-/safe-push-apply-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "es-errors" "^1.3.0"
    "isarray" "^2.0.5"

"safe-regex-test@^1.1.0":
  "integrity" "sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw=="
  "resolved" "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "is-regex" "^1.2.1"

"safer-buffer@^2.0.2", "safer-buffer@^2.1.0", "safer-buffer@>= 2.1.2 < 3", "safer-buffer@~2.1.0":
  "integrity" "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="
  "resolved" "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"semver@^7.5.3", "semver@^7.5.4":
  "integrity" "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz"
  "version" "7.7.2"

"send@0.19.0":
  "integrity" "sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw=="
  "resolved" "https://registry.npmjs.org/send/-/send-0.19.0.tgz"
  "version" "0.19.0"
  dependencies:
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "mime" "1.6.0"
    "ms" "2.1.3"
    "on-finished" "2.4.1"
    "range-parser" "~1.2.1"
    "statuses" "2.0.1"

"serve-static@1.16.2":
  "integrity" "sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw=="
  "resolved" "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz"
  "version" "1.16.2"
  dependencies:
    "encodeurl" "~2.0.0"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.19.0"

"set-function-length@^1.2.2":
  "integrity" "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg=="
  "resolved" "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "define-data-property" "^1.1.4"
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.4"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.2"

"set-function-name@^2.0.2":
  "integrity" "sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ=="
  "resolved" "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "define-data-property" "^1.1.4"
    "es-errors" "^1.3.0"
    "functions-have-names" "^1.2.3"
    "has-property-descriptors" "^1.0.2"

"set-proto@^1.0.0":
  "integrity" "sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw=="
  "resolved" "https://registry.npmjs.org/set-proto/-/set-proto-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "dunder-proto" "^1.0.1"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.0.0"

"setprototypeof@1.2.0":
  "integrity" "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="
  "resolved" "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz"
  "version" "1.2.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"side-channel-list@^1.0.0":
  "integrity" "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA=="
  "resolved" "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "es-errors" "^1.3.0"
    "object-inspect" "^1.13.3"

"side-channel-map@^1.0.1":
  "integrity" "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA=="
  "resolved" "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.5"
    "object-inspect" "^1.13.3"

"side-channel-weakmap@^1.0.2":
  "integrity" "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A=="
  "resolved" "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.5"
    "object-inspect" "^1.13.3"
    "side-channel-map" "^1.0.1"

"side-channel@^1.0.6", "side-channel@^1.1.0":
  "integrity" "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw=="
  "resolved" "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "es-errors" "^1.3.0"
    "object-inspect" "^1.13.3"
    "side-channel-list" "^1.0.0"
    "side-channel-map" "^1.0.1"
    "side-channel-weakmap" "^1.0.2"

"sift@16.0.1":
  "integrity" "sha512-Wv6BjQ5zbhW7VFefWusVP33T/EM0vYikCaQ2qR8yULbsilAT8/wQaXvuQ3ptGLpoKx+lihJE3y2UTgKDyyNHZQ=="
  "resolved" "https://registry.npmjs.org/sift/-/sift-16.0.1.tgz"
  "version" "16.0.1"

"simple-update-notifier@^2.0.0":
  "integrity" "sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w=="
  "resolved" "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "semver" "^7.5.3"

"smart-buffer@^4.2.0":
  "integrity" "sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg=="
  "resolved" "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz"
  "version" "4.2.0"

"socket.io-adapter@~2.5.2":
  "integrity" "sha512-eLDQas5dzPgOWCk9GuuJC2lBqItuhKI4uxGgo9aIV7MYbk2h9Q6uULEh8WBzThoI7l+qU9Ast9fVUmkqPP9wYg=="
  "resolved" "https://registry.npmjs.org/socket.io-adapter/-/socket.io-adapter-2.5.5.tgz"
  "version" "2.5.5"
  dependencies:
    "debug" "~4.3.4"
    "ws" "~8.17.1"

"socket.io-client@^4.8.1":
  "integrity" "sha512-hJVXfu3E28NmzGk8o1sHhN3om52tRvwYeidbj7xKy2eIIse5IoKX3USlS6Tqt3BHAtflLIkCQBkzVrEEfWUyYQ=="
  "resolved" "https://registry.npmjs.org/socket.io-client/-/socket.io-client-4.8.1.tgz"
  "version" "4.8.1"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    "debug" "~4.3.2"
    "engine.io-client" "~6.6.1"
    "socket.io-parser" "~4.2.4"

"socket.io-parser@~4.2.4":
  "integrity" "sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew=="
  "resolved" "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-4.2.4.tgz"
  "version" "4.2.4"
  dependencies:
    "@socket.io/component-emitter" "~3.1.0"
    "debug" "~4.3.1"

"socket.io@^4.7.2":
  "integrity" "sha512-oZ7iUCxph8WYRHHcjBEc9unw3adt5CmSNlppj/5Q4k2RIrhl8Z5yY2Xr4j9zj0+wzVZ0bxmYoGSzKJnRl6A4yg=="
  "resolved" "https://registry.npmjs.org/socket.io/-/socket.io-4.8.1.tgz"
  "version" "4.8.1"
  dependencies:
    "accepts" "~1.3.4"
    "base64id" "~2.0.0"
    "cors" "~2.8.5"
    "debug" "~4.3.2"
    "engine.io" "~6.6.0"
    "socket.io-adapter" "~2.5.2"
    "socket.io-parser" "~4.2.4"

"socks@^2.7.1":
  "integrity" "sha512-D3YaD0aRxR3mEcqnidIs7ReYJFVzWdd6fXJYUM8ixcQcJRGTka/b3saV0KflYhyVJXKhb947GndU35SxYNResQ=="
  "resolved" "https://registry.npmjs.org/socks/-/socks-2.8.4.tgz"
  "version" "2.8.4"
  dependencies:
    "ip-address" "^9.0.5"
    "smart-buffer" "^4.2.0"

"sparse-bitfield@^3.0.3":
  "integrity" "sha512-kvzhi7vqKTfkh0PZU+2D2PIllw2ymqJKujUcyPMd9Y75Nv4nPbGJZXNhxsgdQab2BmlDct1YnfQCguEvHr7VsQ=="
  "resolved" "https://registry.npmjs.org/sparse-bitfield/-/sparse-bitfield-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "memory-pager" "^1.0.2"

"sprintf-js@^1.1.3":
  "integrity" "sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA=="
  "resolved" "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz"
  "version" "1.1.3"

"sshpk@^1.18.0", "sshpk@^1.7.0":
  "integrity" "sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ=="
  "resolved" "https://registry.npmjs.org/sshpk/-/sshpk-1.18.0.tgz"
  "version" "1.18.0"
  dependencies:
    "asn1" "~0.2.3"
    "assert-plus" "^1.0.0"
    "bcrypt-pbkdf" "^1.0.0"
    "dashdash" "^1.12.0"
    "ecc-jsbn" "~0.1.1"
    "getpass" "^0.1.1"
    "jsbn" "~0.1.0"
    "safer-buffer" "^2.0.2"
    "tweetnacl" "~0.14.0"

"statuses@2.0.1":
  "integrity" "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ=="
  "resolved" "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz"
  "version" "2.0.1"

"stealthy-require@^1.1.1":
  "integrity" "sha512-ZnWpYnYugiOVEY5GkcuJK1io5V8QmNYChG62gSit9pQVGErXtrKuPC55ITaVSukmMta5qpMU7vqLt2Lnni4f/g=="
  "resolved" "https://registry.npmjs.org/stealthy-require/-/stealthy-require-1.1.1.tgz"
  "version" "1.1.1"

"stop-iteration-iterator@^1.1.0":
  "integrity" "sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ=="
  "resolved" "https://registry.npmjs.org/stop-iteration-iterator/-/stop-iteration-iterator-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "es-errors" "^1.3.0"
    "internal-slot" "^1.1.0"

"streamsearch@^1.1.0":
  "integrity" "sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg=="
  "resolved" "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.1.1", "string_decoder@~1.1.1":
  "integrity" "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg=="
  "resolved" "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string.prototype.trim@^1.2.10":
  "integrity" "sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA=="
  "resolved" "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz"
  "version" "1.2.10"
  dependencies:
    "call-bind" "^1.0.8"
    "call-bound" "^1.0.2"
    "define-data-property" "^1.1.4"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.5"
    "es-object-atoms" "^1.0.0"
    "has-property-descriptors" "^1.0.2"

"string.prototype.trimend@^1.0.9":
  "integrity" "sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz"
  "version" "1.0.9"
  dependencies:
    "call-bind" "^1.0.8"
    "call-bound" "^1.0.2"
    "define-properties" "^1.2.1"
    "es-object-atoms" "^1.0.0"

"string.prototype.trimstart@^1.0.8":
  "integrity" "sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg=="
  "resolved" "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-object-atoms" "^1.0.0"

"supports-color@^5.5.0":
  "integrity" "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"tldts-core@^6.1.86":
  "integrity" "sha512-Je6p7pkk+KMzMv2XXKmAE3McmolOQFdxkKw0R8EYNr7sELW46JqnNeTX8ybPiQgvg1ymCoF8LXs5fzFaZvJPTA=="
  "resolved" "https://registry.npmjs.org/tldts-core/-/tldts-core-6.1.86.tgz"
  "version" "6.1.86"

"tldts@^6.1.32":
  "integrity" "sha512-WMi/OQ2axVTf/ykqCQgXiIct+mSQDFdH2fkwhPwgEwvJ1kSzZRiinb0zF2Xb8u4+OqPChmyI6MEu4EezNJz+FQ=="
  "resolved" "https://registry.npmjs.org/tldts/-/tldts-6.1.86.tgz"
  "version" "6.1.86"
  dependencies:
    "tldts-core" "^6.1.86"

"to-regex-range@^5.0.1":
  "integrity" "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ=="
  "resolved" "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"toidentifier@1.0.1":
  "integrity" "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="
  "resolved" "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz"
  "version" "1.0.1"

"touch@^3.1.0":
  "integrity" "sha512-r0eojU4bI8MnHr8c5bNo7lJDdI2qXlWWJk6a9EAFG7vbhTjElYhBVS3/miuE0uOuoLdb8Mc/rVfsmm6eo5o9GA=="
  "resolved" "https://registry.npmjs.org/touch/-/touch-3.1.1.tgz"
  "version" "3.1.1"

"tough-cookie@^4.1.3":
  "integrity" "sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag=="
  "resolved" "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.4.tgz"
  "version" "4.1.4"
  dependencies:
    "psl" "^1.1.33"
    "punycode" "^2.1.1"
    "universalify" "^0.2.0"
    "url-parse" "^1.5.3"

"tough-cookie@^5.0.0":
  "integrity" "sha512-FVDYdxtnj0G6Qm/DhNPSb8Ju59ULcup3tuJxkFb5K8Bv2pUXILbf0xZWU8PX8Ov19OXljbUyveOFwRMwkXzO+A=="
  "resolved" "https://registry.npmjs.org/tough-cookie/-/tough-cookie-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "tldts" "^6.1.32"

"tough-cookie@~2.5.0":
  "integrity" "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g=="
  "resolved" "https://registry.npmjs.org/tough-cookie/-/tough-cookie-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "psl" "^1.1.28"
    "punycode" "^2.1.1"

"tr46@^3.0.0":
  "integrity" "sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA=="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "punycode" "^2.1.1"

"tr46@^5.1.0":
  "integrity" "sha512-hdF5ZgjTqgAntKkklYw0R03MG2x/bSzTtkxmIRw/sTNV8YXsCJ1tfLAX23lhxhHJlEf3CRCOCGGWw3vI3GaSPw=="
  "resolved" "https://registry.npmjs.org/tr46/-/tr46-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "punycode" "^2.3.1"

"ts-mixer@^6.0.4":
  "integrity" "sha512-ufKpbmrugz5Aou4wcr5Wc1UUFWOLhq+Fm6qa6P0w0K5Qw2yhaUoiWszhCVuNQyNwrlGiscHOmqYoAox1PtvgjA=="
  "resolved" "https://registry.npmjs.org/ts-mixer/-/ts-mixer-6.0.4.tgz"
  "version" "6.0.4"

"tslib@^2.6.2", "tslib@^2.6.3":
  "integrity" "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  "version" "2.8.1"

"tunnel-agent@^0.6.0":
  "integrity" "sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w=="
  "resolved" "https://registry.npmjs.org/tunnel-agent/-/tunnel-agent-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "safe-buffer" "^5.0.1"

"tweetnacl@^0.14.3", "tweetnacl@~0.14.0":
  "integrity" "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA=="
  "resolved" "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz"
  "version" "0.14.5"

"type-is@^1.6.18", "type-is@~1.6.18":
  "integrity" "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g=="
  "resolved" "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"typed-array-buffer@^1.0.3":
  "integrity" "sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw=="
  "resolved" "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "call-bound" "^1.0.3"
    "es-errors" "^1.3.0"
    "is-typed-array" "^1.1.14"

"typed-array-byte-length@^1.0.3":
  "integrity" "sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg=="
  "resolved" "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "call-bind" "^1.0.8"
    "for-each" "^0.3.3"
    "gopd" "^1.2.0"
    "has-proto" "^1.2.0"
    "is-typed-array" "^1.1.14"

"typed-array-byte-offset@^1.0.4":
  "integrity" "sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ=="
  "resolved" "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "available-typed-arrays" "^1.0.7"
    "call-bind" "^1.0.8"
    "for-each" "^0.3.3"
    "gopd" "^1.2.0"
    "has-proto" "^1.2.0"
    "is-typed-array" "^1.1.15"
    "reflect.getprototypeof" "^1.0.9"

"typed-array-length@^1.0.7":
  "integrity" "sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg=="
  "resolved" "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "call-bind" "^1.0.7"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "is-typed-array" "^1.1.13"
    "possible-typed-array-names" "^1.0.0"
    "reflect.getprototypeof" "^1.0.6"

"typedarray@^0.0.6":
  "integrity" "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA=="
  "resolved" "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"uid-safe@~2.1.5":
  "integrity" "sha512-KPHm4VL5dDXKz01UuEd88Df+KzynaohSL9fBh096KWAxSKZQDI2uBrVqtvRM4rwrIrRRKsdLNML/lnaaVSRioA=="
  "resolved" "https://registry.npmjs.org/uid-safe/-/uid-safe-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "random-bytes" "~1.0.0"

"uid2@0.0.x":
  "integrity" "sha512-IevTus0SbGwQzYh3+fRsAMTVVPOoIVufzacXcHPmdlle1jUpq7BRL+mw3dgeLanvGZdwwbWhRV6XrcFNdBmjWA=="
  "resolved" "https://registry.npmjs.org/uid2/-/uid2-0.0.4.tgz"
  "version" "0.0.4"

"unbox-primitive@^1.1.0":
  "integrity" "sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw=="
  "resolved" "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "call-bound" "^1.0.3"
    "has-bigints" "^1.0.2"
    "has-symbols" "^1.1.0"
    "which-boxed-primitive" "^1.1.1"

"undefsafe@^2.0.5":
  "integrity" "sha512-WxONCrssBM8TSPRqN5EmsjVrsv4A8X12J4ArBiiayv3DyyG3ZlIg6yysuuSYdZsVz3TKcTg2fd//Ujd4CHV1iA=="
  "resolved" "https://registry.npmjs.org/undefsafe/-/undefsafe-2.0.5.tgz"
  "version" "2.0.5"

"undici-types@~6.21.0":
  "integrity" "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ=="
  "resolved" "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz"
  "version" "6.21.0"

"undici@6.21.1":
  "integrity" "sha512-q/1rj5D0/zayJB2FraXdaWxbhWiNKDvu8naDT2dl1yTlvJp4BLtOcp2a5BvgGNQpYYJzau7tf1WgKv3b+7mqpQ=="
  "resolved" "https://registry.npmjs.org/undici/-/undici-6.21.1.tgz"
  "version" "6.21.1"

"universalify@^0.2.0":
  "integrity" "sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg=="
  "resolved" "https://registry.npmjs.org/universalify/-/universalify-0.2.0.tgz"
  "version" "0.2.0"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ=="
  "resolved" "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"url-parse@^1.5.3":
  "integrity" "sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ=="
  "resolved" "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"util-deprecate@^1.0.1", "util-deprecate@~1.0.1":
  "integrity" "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="
  "resolved" "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"utils-merge@^1.0.1", "utils-merge@1.0.1", "utils-merge@1.x.x":
  "integrity" "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA=="
  "resolved" "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^3.3.2":
  "integrity" "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-3.4.0.tgz"
  "version" "3.4.0"

"uuid@^8.3.2":
  "integrity" "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz"
  "version" "8.3.2"

"uuid@^9.0.1":
  "integrity" "sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA=="
  "resolved" "https://registry.npmjs.org/uuid/-/uuid-9.0.1.tgz"
  "version" "9.0.1"

"vary@^1", "vary@~1.1.2":
  "integrity" "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg=="
  "resolved" "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz"
  "version" "1.1.2"

"verror@1.10.0":
  "integrity" "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw=="
  "resolved" "https://registry.npmjs.org/verror/-/verror-1.10.0.tgz"
  "version" "1.10.0"
  dependencies:
    "assert-plus" "^1.0.0"
    "core-util-is" "1.0.2"
    "extsprintf" "^1.2.0"

"webidl-conversions@^7.0.0":
  "integrity" "sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g=="
  "resolved" "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-7.0.0.tgz"
  "version" "7.0.0"

"whatwg-url@^11.0.0":
  "integrity" "sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-11.0.0.tgz"
  "version" "11.0.0"
  dependencies:
    "tr46" "^3.0.0"
    "webidl-conversions" "^7.0.0"

"whatwg-url@^14.1.0 || ^13.0.0":
  "integrity" "sha512-De72GdQZzNTUBBChsXueQUnPKDkg/5A5zp7pFDuQAj5UFoENpiACU0wlCvzpAGnTkj++ihpKwKyYewn/XNUbKw=="
  "resolved" "https://registry.npmjs.org/whatwg-url/-/whatwg-url-14.2.0.tgz"
  "version" "14.2.0"
  dependencies:
    "tr46" "^5.1.0"
    "webidl-conversions" "^7.0.0"

"which-boxed-primitive@^1.1.0", "which-boxed-primitive@^1.1.1":
  "integrity" "sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA=="
  "resolved" "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-bigint" "^1.1.0"
    "is-boolean-object" "^1.2.1"
    "is-number-object" "^1.1.1"
    "is-string" "^1.1.1"
    "is-symbol" "^1.1.1"

"which-builtin-type@^1.2.1":
  "integrity" "sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q=="
  "resolved" "https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "call-bound" "^1.0.2"
    "function.prototype.name" "^1.1.6"
    "has-tostringtag" "^1.0.2"
    "is-async-function" "^2.0.0"
    "is-date-object" "^1.1.0"
    "is-finalizationregistry" "^1.1.0"
    "is-generator-function" "^1.0.10"
    "is-regex" "^1.2.1"
    "is-weakref" "^1.0.2"
    "isarray" "^2.0.5"
    "which-boxed-primitive" "^1.1.0"
    "which-collection" "^1.0.2"
    "which-typed-array" "^1.1.16"

"which-collection@^1.0.2":
  "integrity" "sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw=="
  "resolved" "https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-map" "^2.0.3"
    "is-set" "^2.0.3"
    "is-weakmap" "^2.0.2"
    "is-weakset" "^2.0.3"

"which-typed-array@^1.1.16", "which-typed-array@^1.1.19":
  "integrity" "sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw=="
  "resolved" "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.19.tgz"
  "version" "1.1.19"
  dependencies:
    "available-typed-arrays" "^1.0.7"
    "call-bind" "^1.0.8"
    "call-bound" "^1.0.4"
    "for-each" "^0.3.5"
    "get-proto" "^1.0.1"
    "gopd" "^1.2.0"
    "has-tostringtag" "^1.0.2"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wrappy@1":
  "integrity" "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ=="
  "resolved" "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"ws@^8.17.0":
  "integrity" "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz"
  "version" "8.18.2"

"ws@~8.17.1":
  "integrity" "sha512-6XQFvXTkbfUOZOKKILFG1PDK2NDQs4azKQl26T0YS5CxqWLgXajbPZ+h4gZekJyRqFU8pvnbAbbs/3TgRPy+GQ=="
  "resolved" "https://registry.npmjs.org/ws/-/ws-8.17.1.tgz"
  "version" "8.17.1"

"xmlhttprequest-ssl@~2.1.1":
  "integrity" "sha512-TEU+nJVUUnA4CYJFLvK5X9AOeH4KvDvhIfm0vV1GaQRtchnG0hgK5p8hw/xjv8cunWYCsiPCSDzObPyhEwq3KQ=="
  "resolved" "https://registry.npmjs.org/xmlhttprequest-ssl/-/xmlhttprequest-ssl-2.1.2.tgz"
  "version" "2.1.2"

"xtend@^4.0.2":
  "integrity" "sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ=="
  "resolved" "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz"
  "version" "4.0.2"
