describe('Security Tests', () => {
  beforeEach(() => {
    cy.clearAppData();
  });

  describe('Authentication Security', () => {
    it('should not expose sensitive information in localStorage', () => {
      // Mock login
      cy.intercept('POST', '/api/users/login', {
        statusCode: 200,
        body: {
          _id: 'user123',
          name: 'Test User',
          email: '<EMAIL>',
          username: 'testuser',
          bio: 'Test bio',
          profilePic: '',
          isProfileComplete: true
        }
      }).as('loginRequest');

      cy.visit('/auth');
      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();
      cy.wait('@loginRequest');

      // Check that sensitive data is not stored in localStorage
      cy.window().then((win) => {
        const userData = win.localStorage.getItem('user-threads');
        if (userData) {
          const parsedData = JSON.parse(userData);
          expect(parsedData).to.not.have.property('password');
          expect(parsedData).to.not.have.property('token');
          expect(parsedData).to.not.have.property('refreshToken');
        }
      });
    });

    it('should handle session timeout properly', () => {
      // Mock authenticated user
      cy.window().then((win) => {
        win.localStorage.setItem('user-threads', JSON.stringify({
          _id: 'user123',
          name: 'Test User',
          username: 'testuser',
          isProfileComplete: true
        }));
      });

      // Mock 401 unauthorized response for posts feed
      cy.intercept('GET', '/api/posts/feed', {
        statusCode: 401,
        body: { error: 'Session expired' }
      }).as('sessionExpired');

      cy.visit('/');
      
      // Wait for the API call to be made
      cy.wait('@sessionExpired');

      // Should redirect to auth page
      cy.url().should('include', '/auth');
      
      // Should clear user data
      cy.window().then((win) => {
        expect(win.localStorage.getItem('user-threads')).to.be.null;
      });
    });

    it('should prevent unauthorized access to protected routes', () => {
      // Try to access protected routes without authentication
      const protectedRoutes = ['/update', '/search'];

      protectedRoutes.forEach(route => {
        cy.visit(route);
        cy.url().should('include', '/auth');
      });
    });

    it('should validate password strength during signup', () => {
      cy.visit('/auth');
      cy.contains('Sign up').click();

      const weakPasswords = ['123', 'password', 'abc123'];
      
      weakPasswords.forEach(password => {
        cy.get('input[placeholder="Create a password"]').clear().type(password);
        cy.get('input[placeholder="Enter your full name"]').type('Test User');
        cy.get('input[placeholder="Choose a username"]').type('testuser');
        cy.get('input[placeholder="Enter your email address"]').type('<EMAIL>');
        cy.contains('button', 'Sign up').click();

        // Should show password strength error (if implemented)
        // For now, just verify form doesn't submit with weak password
        cy.url().should('include', '/auth');
      });
    });
  });

  describe('XSS Protection', () => {
    beforeEach(() => {
      // Setup authenticated user for protected tests
      cy.setupAuthenticatedUser();
    });

    it('should sanitize user input in posts', () => {
      const maliciousInputs = [
        '<script>alert("XSS")</script>',
        '<img src="x" onerror="alert(\'XSS\')">',
        'javascript:alert("XSS")',
        '<svg onload="alert(\'XSS\')"></svg>'
      ];

      cy.intercept('GET', '/api/posts/feed', { body: [] }).as('getFeed');
      cy.visit('/');

      maliciousInputs.forEach(input => {
        cy.intercept('POST', '/api/posts/create', {
          statusCode: 201,
          body: {
            _id: 'test-post-id',
            text: input,
            postedBy: { username: 'testuser', name: 'Test User' },
            likes: [],
            replies: [],
            createdAt: new Date().toISOString()
          }
        }).as('createMaliciousPost');

        // Use the actual floating create post button
        cy.get('[class*="brand-button"]').click();
        
        // Type in the textarea that appears in the modal
        cy.get('textarea[placeholder="What\'s happening?"]').clear().type(input);
        cy.get('button').contains('Post').click();
        cy.wait('@createMaliciousPost');

        // Close modal and reopen for next test
        cy.get('[aria-label="Close"]').click();

        // Should not execute the script
        cy.on('window:alert', () => {
          throw new Error('XSS attack successful - alert was executed');
        });

        // Script tags should be escaped or removed in displayed content
        cy.get('body').should('not.contain', '<script>');
        cy.get('body').should('not.contain', 'javascript:');
      });
    });

    it('should sanitize user profile information', () => {
      const xssPayloads = {
        name: '<script>alert("XSS in name")</script>',
        bio: '<img src="x" onerror="alert(\'XSS in bio\')">'
      };

      cy.intercept('PUT', '/api/users/update', {
        statusCode: 200,
        body: {
          _id: 'security-test-user',
          name: xssPayloads.name,
          bio: xssPayloads.bio,
          username: 'securitytestuser'
        }
      }).as('updateProfileWithXSS');

      cy.visit('/update');

      // Use actual form field selectors based on the UpdateProfilePage
      cy.get('input[placeholder="John Doe"]').clear().type(xssPayloads.name);
      cy.get('textarea[placeholder="Tell us about yourself"]').clear().type(xssPayloads.bio);
      cy.contains('button', 'Update').click();
      cy.wait('@updateProfileWithXSS');

      // Should not execute scripts
      cy.on('window:alert', () => {
        throw new Error('XSS attack in profile successful');
      });

      // Visit profile page to check sanitization
      cy.visit('/securitytestuser');
      cy.get('body').should('not.contain', '<script>');
      cy.get('body').should('not.contain', '<img');
    });
  });

  describe('Content Security Policy', () => {
    it('should have proper CSP headers', () => {
      cy.visit('/', {
        onBeforeLoad: (win) => {
          // Check for CSP in meta tags or headers
          cy.document().then((doc) => {
            const cspMeta = doc.querySelector('meta[http-equiv="Content-Security-Policy"]');
            if (cspMeta) {
              const csp = cspMeta.getAttribute('content');
              expect(csp).to.include("script-src 'self'");
              expect(csp).to.include("object-src 'none'");
            }
          });
        }
      });
    });

    it('should handle inline scripts safely', () => {
      cy.visit('/');
      
      // Try to inject inline script
      cy.window().then((win) => {
        const script = win.document.createElement('script');
        script.innerHTML = 'window.xssTest = true;';
        win.document.head.appendChild(script);
        
        // Check if script executed (may or may not based on CSP implementation)
        cy.log('Inline script test completed');
      });
    });
  });

  describe('Data Privacy', () => {
    it('should not expose user data in error messages', () => {
      cy.intercept('GET', '/api/users/profile/nonexistentuser', {
        statusCode: 404,
        body: { error: 'User not found' }
      }).as('userNotFound');

      cy.visit('/nonexistentuser');
      cy.wait('@userNotFound');

      // Error message should not contain sensitive information
      cy.contains('User not found').should('be.visible');
      cy.get('body').should('not.contain', '@email');
      cy.get('body').should('not.contain', 'password');
      cy.get('body').should('not.contain', 'token');
    });

    it('should handle timing attacks consistently', () => {
      const startTime = Date.now();
      
      cy.intercept('POST', '/api/users/login', {
        statusCode: 401,
        body: { error: 'Invalid username or password' },
        delay: 500 // Consistent delay regardless of whether user exists
      }).as('loginFailure');

      cy.visit('/auth');
      cy.get('input[placeholder="Enter your username"]').type('nonexistentuser');
      cy.get('input[placeholder="Enter your password"]').type('wrongpassword');
      cy.contains('button', 'Login').click();
      cy.wait('@loginFailure');

      cy.then(() => {
        const responseTime = Date.now() - startTime;
        // Response time should be reasonable (allowing for network and processing)
        // Increased range to account for Cypress overhead
        expect(responseTime).to.be.within(400, 3000);
      });
    });
  });

  describe('API Security', () => {
    beforeEach(() => {
      cy.setupAuthenticatedUser();
    });

    it('should handle rate limiting gracefully', () => {
      // Mock rate limiting response
      cy.intercept('POST', '/api/posts/create', {
        statusCode: 429,
        body: { error: 'Rate limit exceeded. Please try again later.' }
      }).as('rateLimited');

      cy.visit('/');
      cy.get('[class*="brand-button"]').click();
      cy.get('textarea[placeholder="What\'s happening?"]').type('Rate limit test');
      cy.get('button').contains('Post').click();
      cy.wait('@rateLimited');

      // Should show appropriate error message
      cy.contains('Rate limit exceeded').should('be.visible');
    });

    it('should validate API input parameters', () => {
      // Test with invalid post content
      cy.intercept('POST', '/api/posts/create', {
        statusCode: 400,
        body: { error: 'Post content is required and must be less than 500 characters' }
      }).as('invalidPost');

      cy.visit('/');
      cy.get('[class*="brand-button"]').click();
      cy.get('textarea[placeholder="What\'s happening?"]').type('x'.repeat(600)); // Too long
      cy.get('button').contains('Post').click();
      cy.wait('@invalidPost');

      cy.contains('must be less than 500 characters').should('be.visible');
    });
  });

  describe('Input Validation', () => {
    beforeEach(() => {
      cy.setupAuthenticatedUser();
    });

    it('should handle malicious input in search', () => {
      // Mock search endpoint
      cy.intercept('GET', '/api/users/search*', {
        statusCode: 200,
        body: []
      }).as('searchUsers');

      cy.visit('/search');
      
      // Test various potentially malicious inputs
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        '"; DROP TABLE users; --',
        '../../../etc/passwd',
        'javascript:alert(1)',
        '{{7*7}}'
      ];

      maliciousInputs.forEach(input => {
        cy.get('input[placeholder="Search users by username or name..."]').clear().type(input);
        cy.wait('@searchUsers');
        
        // Verify the input doesn't cause any script execution
        cy.window().then((win) => {
          // Should not have any script-injected global variables
          expect(win.xssTest).to.be.undefined;
          expect(win.alert).to.be.a('function'); // Alert should still be the native function
        });
      });
    });
  });

  describe('Session Management Security', () => {
    it('should regenerate session context after login', () => {
      // Get initial state
      cy.visit('/auth');
      cy.window().then((win) => {
        const initialStorage = win.localStorage.getItem('user-threads');
        expect(initialStorage).to.be.null;
      });

      // Login with proper response
      cy.intercept('POST', '/api/users/login', {
        statusCode: 200,
        body: {
          _id: 'user123',
          name: 'Test User',
          username: 'testuser',
          email: '<EMAIL>',
          isProfileComplete: true
        }
      }).as('loginRequest');

      cy.get('input[placeholder="Enter your username"]').type('testuser');
      cy.get('input[placeholder="Enter your password"]').type('password123');
      cy.contains('button', 'Login').click();
      cy.wait('@loginRequest');

      // Verify new session context
      cy.window().then((win) => {
        const newStorage = win.localStorage.getItem('user-threads');
        expect(newStorage).to.not.be.null;
        const userData = JSON.parse(newStorage);
        expect(userData._id).to.equal('user123');
      });
    });

    it('should clear session data on logout', () => {
      // Setup logged in state
      cy.setupAuthenticatedUser();

      cy.intercept('DELETE', '/api/users/logout', {
        statusCode: 200,
        body: { message: 'Logged out successfully' }
      }).as('logoutRequest');

      cy.visit('/settings');
      
      // Find and click the logout button
      cy.contains('button', 'Logout').click();
      cy.wait('@logoutRequest');

      // Verify session data is cleared
      cy.window().then((win) => {
        const userData = win.localStorage.getItem('user-threads');
        expect(userData).to.be.null;
      });

      // Should redirect to auth page
      cy.url().should('include', '/auth');
    });
  });

  describe('File Upload Security', () => {
    beforeEach(() => {
      cy.setupAuthenticatedUser();
    });

    it('should validate file upload security', () => {
      cy.visit('/update');

      // Check if file input exists, if not skip test
      cy.get('body').then(($body) => {
        if ($body.find('input[type="file"]').length > 0) {
          // Test file type validation
          const maliciousFile = new File(['<script>alert("XSS")</script>'], 'script.js', {
            type: 'application/javascript'
          });

          cy.get('input[type="file"]').then(input => {
            const dt = new DataTransfer();
            dt.items.add(maliciousFile);
            input[0].files = dt.files;
            input[0].dispatchEvent(new Event('change', { bubbles: true }));
          });

          // Should either show error or prevent upload of non-image files
          cy.get('body').should('exist'); // Test passes if no error occurs
        } else {
          cy.log('File upload not available - test skipped');
        }
      });
    });
  });

  describe('Password Security', () => {
    it('should mask password input', () => {
      cy.visit('/auth');
      
      const password = 'mysecretpassword';
      cy.get('input[placeholder="Enter your password"]')
        .type(password)
        .should('have.attr', 'type', 'password');

      // Test password visibility toggle if exists
      cy.get('body').then(($body) => {
        if ($body.find('input[placeholder="Enter your password"]').siblings('div').find('button').length > 0) {
          cy.get('input[placeholder="Enter your password"]')
            .siblings('div')
            .find('button')
            .click();
            
          cy.get('input[placeholder="Enter your password"]')
            .should('have.attr', 'type', 'text')
            .should('have.value', password);

          cy.get('input[placeholder="Enter your password"]')
            .siblings('div')
            .find('button')
            .click();
            
          cy.get('input[placeholder="Enter your password"]')
            .should('have.attr', 'type', 'password');
        }
      });
    });

    it('should validate password strength on signup', () => {
      cy.visit('/auth');
      cy.contains('Sign up').click();

      // Test weak password
      cy.get('input[placeholder="Create a password"]').type('123');
      cy.contains('button', 'Sign up').click();

      // Should show validation error or stay on page
      cy.url().should('include', '/auth');
    });
  });

  describe('Information Disclosure Prevention', () => {
    it('should not expose sensitive headers', () => {
      cy.request({
        url: '/',
        failOnStatusCode: false
      }).then((response) => {
        // Should not expose server information
        expect(response.headers).to.not.have.property('server');
        expect(response.headers).to.not.have.property('x-powered-by');
        
        // Check for basic security headers (may or may not be present)
        if (response.headers['x-content-type-options']) {
          expect(response.headers['x-content-type-options']).to.equal('nosniff');
        }
      });
    });

    it('should not expose stack traces in error responses', () => {
      cy.intercept('GET', '/api/posts/feed', {
        statusCode: 500,
        body: { error: 'Internal Server Error' }
      }).as('serverError');

      cy.visit('/');
      cy.wait('@serverError');

      // Should show generic error message, not stack trace
      cy.get('body').should('not.contain', 'at Object');
      cy.get('body').should('not.contain', 'node_modules');
      cy.get('body').should('not.contain', '/src/');
    });
  });
});
