/**
 * Mobile OAuth Test Utility
 * Simple test to verify mobile OAuth utilities are working
 */

// Test imports
try {
  console.log('🧪 Testing mobile OAuth imports...');
  
  // Test device detection
  import('./deviceDetection.js').then(deviceModule => {
    console.log('✅ Device detection module loaded');
    
    const { isMobileDevice, getOAuthStrategy } = deviceModule;
    console.log('📱 Is mobile device:', isMobileDevice());
    console.log('🔐 OAuth strategy:', getOAuthStrategy());
  }).catch(error => {
    console.error('❌ Device detection import failed:', error);
  });
  
  // Test mobile OAuth
  import('./mobileOAuth.js').then(oauthModule => {
    console.log('✅ Mobile OAuth module loaded');
    
    const { isOAuthCallback, getOAuthInstructions } = oauthModule;
    console.log('🔄 Is OAuth callback:', isOAuthCallback());
    console.log('📋 OAuth instructions:', getOAuthInstructions());
  }).catch(error => {
    console.error('❌ Mobile OAuth import failed:', error);
  });
  
} catch (error) {
  console.error('❌ Mobile OAuth test failed:', error);
}

export const testMobileOAuth = () => {
  console.log('🧪 Mobile OAuth test completed - check console for results');
};
