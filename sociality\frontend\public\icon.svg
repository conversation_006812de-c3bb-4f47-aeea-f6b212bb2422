<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 129.24 161.86">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5, .cls-6 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: url(#linear-gradient-6);
      }

      .cls-3 {
        fill: url(#linear-gradient-5);
      }

      .cls-4 {
        fill: url(#linear-gradient-2);
      }

      .cls-5 {
        fill: url(#linear-gradient-3);
      }

      .cls-6 {
        fill: url(#linear-gradient-4);
      }
    </style>
    <linearGradient id="linear-gradient" x1="72.43" y1="146.66" x2="-4.22" y2="110.07" gradientTransform="translate(0 199.08) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#00cc85"/>
      <stop offset=".33" stop-color="#009985"/>
      <stop offset="1" stop-color="#006785"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="47.71" y1="120.11" x2="112.96" y2="54.5" gradientTransform="translate(0 199.08) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0079b9"/>
      <stop offset="1" stop-color="#003d5b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="12.19" y1="177.4" x2="27.2" y2="177.4" gradientTransform="translate(0 199.08) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0079b9"/>
      <stop offset="1" stop-color="#003d5b"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="118.31" y1="136.72" x2="53.44" y2="118.55" gradientTransform="translate(0 199.08) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#00cc85"/>
      <stop offset=".33" stop-color="#009985"/>
      <stop offset="1" stop-color="#006785"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="137.88" y1="66.88" x2="73" y2="48.7" gradientTransform="translate(0 199.08) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#00cc85"/>
      <stop offset=".33" stop-color="#009985"/>
      <stop offset="1" stop-color="#006785"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="12.19" y1="99.83" x2="27.2" y2="99.83" gradientTransform="translate(0 199.08) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0079b9"/>
      <stop offset="1" stop-color="#003d5b"/>
    </linearGradient>
  </defs>
  <g id="OBJECTS">
    <g>
      <path class="cls-1" d="M42.21,15.64c-.45,6.18-1.79,13.63-6.94,20.32-4.09,5.32-13.71,6.72-18.97,7.44C6.9,44.67,0,50.56,0,59.41c0,7.57,4.77,13.9,13.24,15.77,7.96,1.76,14.62,3.17,19.6,7.67,4.47,4.05,7.38,10,9.29,20.94,1.6,9.19,7.53,14.81,15.97,14.81s16.03-7.18,16.03-16.03-6.24-15.03-15.03-15.98c-11.04-1.32-26.82-10.94-26.82-26.13s11.83-25.31,27.24-28.47c9.25-1.79,14.73-7.55,14.73-15.96,0-8.85-7.18-16.03-16.03-16.03s-15.52,6.65-16.01,15.64h0Z"/>
      <path class="cls-4" d="M87.03,58.9c.22,4.91,1.79,13.63,6.94,20.32,4.09,5.32,13.71,6.73,18.97,7.44,9.4,1.27,16.3,7.16,16.3,16.01,0,7.57-4.77,13.9-13.24,15.77-7.96,1.76-14.62,3.17-19.6,7.67-4.47,4.05-7.38,10-9.29,20.94-1.6,9.19-7.53,14.81-15.97,14.81s-16.03-7.18-16.03-16.03,6.24-15.03,15.03-15.98c11.04-1.32,26.82-10.94,26.82-26.13s-11.83-25.31-27.24-28.47c-9.25-1.79-14.73-7.55-14.73-15.96,0-8.85,7.18-16.03,16.03-16.03s15.8,6.97,16.01,15.64Z"/>
      <path class="cls-5" d="M12.2,22.02c-.19-4.14,3.02-7.65,7.16-7.83s7.65,3.02,7.83,7.16-3.02,7.65-7.16,7.83c-4.14.19-7.65-3.02-7.83-7.16Z"/>
      <path class="cls-6" d="M100.83,65.49c-.19-4.14,3.02-7.65,7.16-7.83,4.14-.19,7.65,3.02,7.83,7.16.19,4.14-3.02,7.65-7.16,7.83s-7.65-3.02-7.83-7.16Z"/>
      <path class="cls-3" d="M100.83,140.82c-.19-4.14,3.02-7.65,7.16-7.83,4.14-.19,7.65,3.02,7.83,7.16.19,4.14-3.02,7.65-7.16,7.83-4.14.19-7.65-3.02-7.83-7.16Z"/>
      <path class="cls-2" d="M12.2,99.59c-.19-4.14,3.02-7.65,7.16-7.83s7.65,3.02,7.83,7.16-3.02,7.65-7.16,7.83c-4.14.19-7.65-3.02-7.83-7.16Z"/>
    </g>
  </g>
</svg>