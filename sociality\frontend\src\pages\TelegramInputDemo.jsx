import React from 'react';
import { Box, VStack, Text, Heading, Container, Code, UnorderedList, ListItem, useColorMode, IconButton, HStack, useColorModeValue } from '@chakra-ui/react';
import { SunIcon, MoonIcon } from '@chakra-ui/icons';
import MessageInput from '../components/MessageInput';

const TelegramInputDemo = () => {
  const { colorMode, toggleColorMode } = useColorMode();
  const bgColor = useColorModeValue("gray.50", "gray.900");
  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  const textColor = useColorModeValue("gray.800", "white");
  const subtextColor = useColorModeValue("gray.600", "gray.400");
  
  // Mock setMessages function for demo
  const mockSetMessages = (updater) => {
    console.log('Message would be added:', updater);
  };

  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="4xl">
        <VStack spacing={8} align="stretch">
          <Box textAlign="center">
            <HStack justify="center" mb={4}>
              <Heading size="xl" color={textColor}>
                Telegram-Style Message Input
              </Heading>
              <IconButton
                icon={colorMode === 'light' ? <MoonIcon /> : <SunIcon />}
                onClick={toggleColorMode}
                variant="ghost"
                size="lg"
                aria-label="Toggle color mode"
                ml={4}
              />
            </HStack>
            <Text color={subtextColor} fontSize="lg">
              A modern, feature-rich message input component inspired by Telegram
            </Text>
            <Text color={subtextColor} fontSize="sm" mt={2}>
              Click the {colorMode === 'light' ? 'moon' : 'sun'} icon to toggle between light and dark modes
            </Text>
          </Box>

          <Box
            bg={cardBg}
            border="1px solid"
            borderColor={borderColor}
            borderRadius="lg"
            p={6}
            position="relative"
            h="400px"
            boxShadow={useColorModeValue("md", "lg")}
          >
            <Text color={textColor} mb={4} fontWeight="medium">
              Live Demo - {colorMode === 'light' ? 'Light' : 'Dark'} Mode
            </Text>
            
            {/* Mock chat messages area */}
            <Box
              flex={1}
              bg={useColorModeValue("gray.100", "gray.700")}
              borderRadius="md"
              p={4}
              mb={4}
              h="300px"
              overflowY="auto"
            >
              <Text color={subtextColor} fontSize="sm" textAlign="center" mt={20}>
                Type a message below to see the Telegram-style input in action!
              </Text>
            </Box>
            
            {/* The actual Telegram-style input */}
            <MessageInput setMessages={mockSetMessages} />
          </Box>

          <Box
            bg={cardBg}
            border="1px solid"
            borderColor={borderColor}
            borderRadius="lg"
            p={6}
            boxShadow={useColorModeValue("md", "lg")}
          >
            <Heading size="md" mb={4} color={textColor}>
              Features
            </Heading>
            <UnorderedList spacing={2} color={subtextColor}>
              <ListItem>
                <strong>Auto-resizing textarea</strong> - Expands as you type multi-line messages
              </ListItem>
              <ListItem>
                <strong>Emoji picker</strong> - Quick access to commonly used emojis
              </ListItem>
              <ListItem>
                <strong>File attachments</strong> - Support for images and documents
              </ListItem>
              <ListItem>
                <strong>Voice messages</strong> - Record and send voice notes (UI ready)
              </ListItem>
              <ListItem>
                <strong>Image preview</strong> - See selected images before sending
              </ListItem>
              <ListItem>
                <strong>Keyboard shortcuts</strong> - Enter to send, Shift+Enter for new line
              </ListItem>
              <ListItem>
                <strong>Smooth animations</strong> - Telegram-style transitions and effects
              </ListItem>
              <ListItem>
                <strong>Light/Dark mode support</strong> - Seamlessly adapts to both themes
              </ListItem>
              <ListItem>
                <strong>Responsive design</strong> - Works great on desktop and mobile
              </ListItem>
            </UnorderedList>
          </Box>

          <Box
            bg={cardBg}
            border="1px solid"
            borderColor={borderColor}
            borderRadius="lg"
            p={6}
            boxShadow={useColorModeValue("md", "lg")}
          >
            <Heading size="md" mb={4} color={textColor}>
              Usage
            </Heading>
            <Code
              display="block"
              whiteSpace="pre"
              p={4}
              bg={useColorModeValue("gray.100", "gray.700")}
              borderRadius="md"
              color={useColorModeValue("green.600", "green.300")}
              fontSize="sm"
            >
{`import MessageInput from './components/MessageInput';

// In your chat component
<MessageInput setMessages={setMessages} />`}
            </Code>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default TelegramInputDemo;
