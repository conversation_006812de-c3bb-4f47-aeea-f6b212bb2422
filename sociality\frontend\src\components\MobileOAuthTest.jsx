/**
 * Mobile OAuth Test Component
 * Simple component to test mobile OAuth functionality
 */

import React from 'react';
import { Box, Button, Text, VStack, Badge, Code } from '@chakra-ui/react';
import { 
  isMobileDevice, 
  getOAuthMethod, 
  getOAuthButtonText, 
  getOAuthLoadingText,
  isOAuthCallback 
} from '../utils/simpleMobileOAuth';

const MobileOAuthTest = () => {
  const deviceInfo = {
    isMobile: isMobileDevice(),
    oauthMethod: getOAuthMethod(),
    buttonText: getOAuthButtonText(),
    loadingText: getOAuthLoadingText(),
    isCallback: isOAuthCallback(),
    userAgent: navigator.userAgent,
    viewport: `${window.innerWidth}x${window.innerHeight}`
  };

  return (
    <Box p={6} maxW="600px" mx="auto">
      <VStack spacing={4} align="stretch">
        <Text fontSize="2xl" fontWeight="bold" textAlign="center">
          📱 Mobile OAuth Test
        </Text>
        
        <Box p={4} bg="gray.100" borderRadius="md">
          <Text fontWeight="bold" mb={2}>Device Detection:</Text>
          <VStack align="start" spacing={2}>
            <Text>
              Mobile Device: <Badge colorScheme={deviceInfo.isMobile ? 'green' : 'red'}>
                {deviceInfo.isMobile ? 'Yes' : 'No'}
              </Badge>
            </Text>
            <Text>
              OAuth Method: <Badge colorScheme="blue">{deviceInfo.oauthMethod}</Badge>
            </Text>
            <Text>
              Button Text: <Code>{deviceInfo.buttonText}</Code>
            </Text>
            <Text>
              Loading Text: <Code>{deviceInfo.loadingText}</Code>
            </Text>
            <Text>
              Is OAuth Callback: <Badge colorScheme={deviceInfo.isCallback ? 'yellow' : 'gray'}>
                {deviceInfo.isCallback ? 'Yes' : 'No'}
              </Badge>
            </Text>
          </VStack>
        </Box>

        <Box p={4} bg="gray.50" borderRadius="md">
          <Text fontWeight="bold" mb={2}>Technical Info:</Text>
          <VStack align="start" spacing={1}>
            <Text fontSize="sm">Viewport: {deviceInfo.viewport}</Text>
            <Text fontSize="xs" color="gray.600">
              User Agent: {deviceInfo.userAgent}
            </Text>
          </VStack>
        </Box>

        <Button
          colorScheme="blue"
          onClick={() => {
            console.log('📱 Device Info:', deviceInfo);
            alert('Check console for detailed device information');
          }}
        >
          Log Device Info to Console
        </Button>

        <Text fontSize="sm" color="gray.600" textAlign="center">
          This component helps test mobile OAuth detection and functionality.
          Check the browser console for detailed logs.
        </Text>
      </VStack>
    </Box>
  );
};

export default MobileOAuthTest;
