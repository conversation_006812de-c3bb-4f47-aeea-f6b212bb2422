describe('Notifications', () => {
  beforeEach(() => {
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    // Mock notifications API
    cy.intercept('GET', '/api/notifications', {
      statusCode: 200,
      body: [
        {
          _id: 'notif1',
          type: 'like',
          from: {
            _id: '2',
            username: 'john_doe',
            name: '<PERSON>',
            profilePic: ''
          },
          post: {
            _id: 'post1',
            text: 'My awesome post'
          },
          createdAt: new Date().toISOString(),
          read: false
        },
        {
          _id: 'notif2',
          type: 'follow',
          from: {
            _id: '3',
            username: 'jane_smith',
            name: '<PERSON>',
            profilePic: ''
          },
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          read: true
        },
        {
          _id: 'notif3',
          type: 'comment',
          from: {
            _id: '4',
            username: 'bob_w<PERSON>',
            name: '<PERSON>',
            profilePic: ''
          },
          post: {
            _id: 'post2',
            text: 'Another post'
          },
          comment: {
            text: 'Great work!'
          },
          createdAt: new Date(Date.now() - 7200000).toISOString(),
          read: false
        }
      ]
    }).as('getNotifications');

    cy.visit('/notifications');
    cy.wait('@getNotifications');
  });

  describe('Notification Display', () => {
    it('should display notifications list', () => {
      // Check like notification
      cy.contains('John Doe liked your post').should('be.visible');
      cy.contains('My awesome post').should('be.visible');
      
      // Check follow notification
      cy.contains('Jane Smith started following you').should('be.visible');
      
      // Check comment notification
      cy.contains('Bob Wilson commented on your post').should('be.visible');
      cy.contains('Great work!').should('be.visible');
    });

    it('should show unread notification indicator', () => {
      // Unread notifications should have indicator
      cy.get('[data-testid="notification-item"]').first().should('have.class', 'unread');
      cy.get('[data-testid="unread-indicator"]').should('have.length', 2);
    });

    it('should display relative timestamps', () => {
      cy.contains('just now').should('be.visible');
      cy.contains('1 hour ago').should('be.visible');
      cy.contains('2 hours ago').should('be.visible');
    });

    it('should show profile pictures', () => {
      cy.get('[data-testid="user-avatar"]').should('have.length', 3);
    });

    it('should show empty state when no notifications', () => {
      cy.intercept('GET', '/api/notifications', { body: [] }).as('getEmptyNotifications');
      cy.reload();
      cy.wait('@getEmptyNotifications');
      
      cy.contains('No notifications yet').should('be.visible');
      cy.contains('When someone likes or comments on your posts').should('be.visible');
    });
  });

  describe('Notification Interactions', () => {
    it('should mark notification as read when clicked', () => {
      cy.intercept('PUT', '/api/notifications/notif1/read', {
        statusCode: 200,
        body: { message: 'Notification marked as read' }
      }).as('markAsRead');

      // Click on unread notification
      cy.get('[data-testid="notification-item"]').first().click();
      cy.wait('@markAsRead');
      
      // Should no longer have unread indicator
      cy.get('[data-testid="notification-item"]').first().should('not.have.class', 'unread');
    });

    it('should navigate to post when post notification clicked', () => {
      cy.intercept('GET', '/api/posts/*/*', {
        statusCode: 200,
        body: {
          _id: 'post1',
          text: 'My awesome post',
          postedBy: {
            _id: '1',
            username: 'testuser',
            name: 'Test User'
          }
        }
      }).as('getPost');

      // Click on like notification
      cy.get('[data-testid="notification-item"]').first().click();
      
      cy.wait('@getPost');
      cy.url().should('include', '/testuser/post/post1');
    });

    it('should navigate to user profile when follow notification clicked', () => {
      cy.intercept('GET', '/api/users/profile/jane_smith', {
        statusCode: 200,
        body: {
          _id: '3',
          username: 'jane_smith',
          name: 'Jane Smith',
          bio: 'Designer',
          profilePic: '',
          followers: [],
          following: []
        }
      }).as('getUserProfile');

      // Click on follow notification (second item)
      cy.get('[data-testid="notification-item"]').eq(1).click();
      
      cy.wait('@getUserProfile');
      cy.url().should('include', '/jane_smith');
    });

    it('should delete notification', () => {
      cy.intercept('DELETE', '/api/notifications/notif1', {
        statusCode: 200,
        body: { message: 'Notification deleted' }
      }).as('deleteNotification');

      // Click menu on notification
      cy.get('[data-testid="notification-menu"]').first().click();
      cy.contains('Delete').click();
      
      cy.wait('@deleteNotification');
      
      // Notification should be removed from list
      cy.contains('John Doe liked your post').should('not.exist');
    });
  });

  describe('Notification Actions', () => {
    it('should mark all notifications as read', () => {
      cy.intercept('PUT', '/api/notifications/mark-all-read', {
        statusCode: 200,
        body: { message: 'All notifications marked as read' }
      }).as('markAllAsRead');

      cy.get('[data-testid="mark-all-read"]').click();
      cy.wait('@markAllAsRead');
      
      // All unread indicators should be gone
      cy.get('[data-testid="unread-indicator"]').should('not.exist');
      cy.contains('All notifications marked as read').should('be.visible');
    });

    it('should delete all notifications', () => {
      cy.intercept('DELETE', '/api/notifications/all', {
        statusCode: 200,
        body: { message: 'All notifications deleted' }
      }).as('deleteAllNotifications');

      cy.get('[data-testid="delete-all"]').click();
      cy.contains('Delete All Notifications').click(); // Confirm modal
      
      cy.wait('@deleteAllNotifications');
      cy.contains('No notifications yet').should('be.visible');
    });

    it('should filter notifications by type', () => {
      cy.get('[data-testid="filter-likes"]').click();
      
      // Should only show like notifications
      cy.contains('John Doe liked your post').should('be.visible');
      cy.contains('Jane Smith started following you').should('not.exist');
      cy.contains('Bob Wilson commented on your post').should('not.exist');
      
      // Switch to follows filter
      cy.get('[data-testid="filter-follows"]').click();
      cy.contains('Jane Smith started following you').should('be.visible');
      cy.contains('John Doe liked your post').should('not.exist');
      
      // Show all notifications
      cy.get('[data-testid="filter-all"]').click();
      cy.contains('John Doe liked your post').should('be.visible');
      cy.contains('Jane Smith started following you').should('be.visible');
      cy.contains('Bob Wilson commented on your post').should('be.visible');
    });

    it('should refresh notifications', () => {
      cy.intercept('GET', '/api/notifications', {
        statusCode: 200,
        body: [
          {
            _id: 'notif4',
            type: 'like',
            from: {
              _id: '5',
              username: 'new_user',
              name: 'New User',
              profilePic: ''
            },
            post: {
              _id: 'post3',
              text: 'Fresh post'
            },
            createdAt: new Date().toISOString(),
            read: false
          }
        ]
      }).as('refreshNotifications');

      cy.get('[data-testid="refresh-notifications"]').click();
      cy.wait('@refreshNotifications');
      
      cy.contains('New User liked your post').should('be.visible');
    });
  });

  describe('Notification Settings', () => {
    it('should access notification settings from notifications page', () => {
      cy.get('[data-testid="notification-settings"]').click();
      cy.url().should('include', '/settings');
      cy.contains('Notification Settings').should('be.visible');
    });

    it('should show notification preferences', () => {
      cy.get('[data-testid="notification-settings"]').click();
      
      // Should show toggle switches for different notification types
      cy.get('[data-testid="likes-notifications"]').should('be.visible');
      cy.get('[data-testid="comments-notifications"]').should('be.visible');
      cy.get('[data-testid="follows-notifications"]').should('be.visible');
      cy.get('[data-testid="mentions-notifications"]').should('be.visible');
    });
  });

  describe('Real-time Notifications', () => {
    it('should receive real-time notifications via socket', () => {
      // Mock socket event
      cy.window().then((win) => {
        if (win.socket) {
          win.socket.emit('notification', {
            _id: 'new-notif',
            type: 'like',
            from: {
              _id: '6',
              username: 'live_user',
              name: 'Live User',
              profilePic: ''
            },
            post: {
              _id: 'post4',
              text: 'Live post'
            },
            createdAt: new Date().toISOString(),
            read: false
          });
        }
      });

      // Should show new notification
      cy.contains('Live User liked your post').should('be.visible');
      cy.get('[data-testid="notification-count"]').should('contain', '3'); // Updated count
    });

    it('should show notification toast for new notifications', () => {
      cy.window().then((win) => {
        if (win.socket) {
          win.socket.emit('notification', {
            _id: 'toast-notif',
            type: 'follow',
            from: {
              _id: '7',
              username: 'toast_user',
              name: 'Toast User',
              profilePic: ''
            },
            createdAt: new Date().toISOString(),
            read: false
          });
        }
      });

      // Should show toast notification
      cy.get('[data-testid="notification-toast"]').should('be.visible');
      cy.contains('Toast User started following you').should('be.visible');
    });

    it('should update notification count in header', () => {
      // Initial count should be 2 (unread notifications)
      cy.get('[data-testid="notification-badge"]').should('contain', '2');
      
      // Mark one as read
      cy.intercept('PUT', '/api/notifications/notif1/read', {
        statusCode: 200,
        body: { message: 'Notification marked as read' }
      }).as('markAsRead');

      cy.get('[data-testid="notification-item"]').first().click();
      cy.wait('@markAsRead');
      
      // Count should decrease
      cy.get('[data-testid="notification-badge"]').should('contain', '1');
    });
  });

  describe('Notification Types', () => {
    it('should display like notifications correctly', () => {
      cy.contains('John Doe liked your post').should('be.visible');
      cy.get('[data-testid="like-icon"]').should('be.visible');
    });

    it('should display comment notifications correctly', () => {
      cy.contains('Bob Wilson commented on your post').should('be.visible');
      cy.contains('Great work!').should('be.visible');
      cy.get('[data-testid="comment-icon"]').should('be.visible');
    });

    it('should display follow notifications correctly', () => {
      cy.contains('Jane Smith started following you').should('be.visible');
      cy.get('[data-testid="follow-icon"]').should('be.visible');
    });

    it('should display mention notifications', () => {
      cy.intercept('GET', '/api/notifications', {
        statusCode: 200,
        body: [
          {
            _id: 'mention1',
            type: 'mention',
            from: {
              _id: '8',
              username: 'mention_user',
              name: 'Mention User',
              profilePic: ''
            },
            post: {
              _id: 'post5',
              text: 'Hey @testuser, check this out!'
            },
            createdAt: new Date().toISOString(),
            read: false
          }
        ]
      }).as('getMentionNotifications');

      cy.reload();
      cy.wait('@getMentionNotifications');
      
      cy.contains('Mention User mentioned you in a post').should('be.visible');
      cy.get('[data-testid="mention-icon"]').should('be.visible');
    });

    it('should display repost notifications', () => {
      cy.intercept('GET', '/api/notifications', {
        statusCode: 200,
        body: [
          {
            _id: 'repost1',
            type: 'repost',
            from: {
              _id: '9',
              username: 'repost_user',
              name: 'Repost User',
              profilePic: ''
            },
            post: {
              _id: 'post6',
              text: 'Original post content'
            },
            createdAt: new Date().toISOString(),
            read: false
          }
        ]
      }).as('getRepostNotifications');

      cy.reload();
      cy.wait('@getRepostNotifications');
      
      cy.contains('Repost User reposted your post').should('be.visible');
      cy.get('[data-testid="repost-icon"]').should('be.visible');
    });
  });

  describe('Notification Pagination', () => {
    it('should load more notifications when scrolling', () => {
      cy.intercept('GET', '/api/notifications?page=2', {
        statusCode: 200,
        body: [
          {
            _id: 'old-notif',
            type: 'like',
            from: {
              _id: '10',
              username: 'old_user',
              name: 'Old User',
              profilePic: ''
            },
            post: {
              _id: 'old-post',
              text: 'Old post'
            },
            createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
            read: true
          }
        ]
      }).as('getOlderNotifications');

      // Scroll to bottom
      cy.scrollTo('bottom');
      cy.wait('@getOlderNotifications');
      
      cy.contains('Old User liked your post').should('be.visible');
    });

    it('should show load more button', () => {
      cy.get('[data-testid="load-more"]').should('be.visible');
      
      cy.intercept('GET', '/api/notifications?page=2', {
        statusCode: 200,
        body: []
      }).as('getMoreNotifications');

      cy.get('[data-testid="load-more"]').click();
      cy.wait('@getMoreNotifications');
      
      // Button should disappear when no more notifications
      cy.get('[data-testid="load-more"]').should('not.exist');
    });
  });

  describe('Error Handling', () => {
    it('should handle failed notification loading', () => {
      cy.intercept('GET', '/api/notifications', {
        statusCode: 500,
        body: { error: 'Failed to load notifications' }
      }).as('getNotificationsError');

      cy.reload();
      cy.wait('@getNotificationsError');
      
      cy.contains('Failed to load notifications').should('be.visible');
      cy.contains('Try again').should('be.visible');
    });

    it('should retry failed requests', () => {
      cy.intercept('GET', '/api/notifications', {
        statusCode: 500,
        body: { error: 'Failed to load notifications' }
      }).as('getNotificationsError');

      cy.reload();
      cy.wait('@getNotificationsError');
      
      // Mock successful retry
      cy.intercept('GET', '/api/notifications', {
        statusCode: 200,
        body: []
      }).as('retryNotifications');

      cy.contains('Try again').click();
      cy.wait('@retryNotifications');
      
      cy.contains('No notifications yet').should('be.visible');
    });

    it('should handle mark as read failures', () => {
      cy.intercept('PUT', '/api/notifications/notif1/read', {
        statusCode: 500,
        body: { error: 'Failed to mark as read' }
      }).as('markAsReadError');

      cy.get('[data-testid="notification-item"]').first().click();
      cy.wait('@markAsReadError');
      
      cy.contains('Failed to mark as read').should('be.visible');
      // Notification should remain unread
      cy.get('[data-testid="notification-item"]').first().should('have.class', 'unread');
    });
  });

  describe('Responsive Notifications', () => {
    it('should display correctly on mobile', () => {
      cy.viewport(375, 667);
      
      cy.get('[data-testid="notification-item"]').should('be.visible');
      cy.get('[data-testid="mobile-notification-actions"]').should('be.visible');
    });

    it('should use swipe gestures on mobile', () => {
      cy.viewport(375, 667);
      
      // Swipe notification item to reveal actions
      cy.get('[data-testid="notification-item"]').first()
        .trigger('touchstart', { touches: [{ clientX: 100, clientY: 100 }] })
        .trigger('touchmove', { touches: [{ clientX: 200, clientY: 100 }] })
        .trigger('touchend');
      
      cy.get('[data-testid="swipe-actions"]').should('be.visible');
      cy.contains('Mark as Read').should('be.visible');
      cy.contains('Delete').should('be.visible');
    });
  });
});
