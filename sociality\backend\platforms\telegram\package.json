{"name": "sociality-telegram-platform", "version": "1.0.0", "description": "Telegram integration for Sociality cross-platform messaging", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "morgan": "^1.10.0", "dotenv": "^16.3.1", "axios": "^1.5.0", "node-telegram-bot-api": "^0.64.0", "mongoose": "^8.0.3", "socket.io": "^4.7.2", "passport": "^0.6.0", "express-session": "^1.17.3", "connect-mongo": "^5.0.0", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["telegram", "bot", "federation", "messaging"], "author": "Sociality Team", "license": "MIT"}