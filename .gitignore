# ===================================
# Node.js Dependencies & Package Managers
# ===================================
node_modules/
*/node_modules/
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Package manager lock files (keep package-lock.json but ignore others if needed)
# Uncomment the next line if you want to ignore package-lock.json
# package-lock.json
yarn.lock
pnpm-lock.yaml
.pnp
.pnp.js

# Yarn v2+
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ===================================
# Environment Variables & Secrets
# ===================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local
sociality/backend/.env*
sociality/frontend/.env*
sociality/federation-registry/.env*

# ===================================
# Build Output & Distribution
# ===================================
dist/
build/
out/
*/dist/
*/build/
*/out/
sociality/frontend/dist/
sociality/backend/dist/
sociality/federation-registry/dist/

# ===================================
# Logs & Debug Files
# ===================================
*.log
logs/
*.log.*
pids
*.pid
*.seed
*.pid.lock
lib-cov
coverage/
.nyc_output
.grunt
.lock-wscript

# ===================================
# Runtime & Cache Files
# ===================================
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/
.tern-port
.vscode-test
.eslintcache
.stylelintcache

# ===================================
# Testing & Coverage
# ===================================
coverage/
.nyc_output/
cypress/videos/
cypress/screenshots/
cypress/downloads/
test-results/
playwright-report/
test-results.xml
junit.xml

# ===================================
# Database & Storage
# ===================================
*.sqlite
*.sqlite3
*.db
uploads/
*/uploads/
sociality/backend/uploads/

# ===================================
# IDE & Editor Files
# ===================================
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
*.sublime-project
*.sublime-workspace

# ===================================
# Operating System Files
# ===================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# ===================================
# Temporary & Backup Files
# ===================================
tmp/
temp/
*.tmp
*.temp
*.bak
*.backup
*.old
.history/

# ===================================
# Docker & Containerization
# ===================================
.dockerignore
docker-compose.override.yml
.docker/

# ===================================
# Deployment & Cloud Services
# ===================================
.railway/
.vercel/
.netlify/
.firebase/
.aws/
.gcp/

# ===================================
# Tunneling & Development Tools
# ===================================
ngrok
*.ngrok
.ngrok2/

# ===================================
# Security & Certificates
# ===================================
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx
*.jks

# ===================================
# Miscellaneous
# ===================================
.sass-cache/
.connect.lock
.grunt/
.lock-wscript
.wafpickle-*
.task-runner-cache
.jspm-packages/
typings/
.npm
.eslintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===================================
# Keep Important Files (Exceptions)
# ===================================
!.gitignore
!.gitkeep
!.github/
!.vscode/settings.json
!.vscode/extensions.json
