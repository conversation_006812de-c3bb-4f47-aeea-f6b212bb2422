describe('Chat Functionality', () => {
  beforeEach(() => {
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        username: 'testuser',
        bio: 'Test bio',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    // Mock conversations API
    cy.intercept('GET', '/api/messages/conversations', {
      statusCode: 200,
      body: [
        {
          _id: 'conv1',
          participants: [
            { _id: '1', username: 'testuser', name: 'Test User' },
            { _id: '2', username: 'john_doe', name: '<PERSON>' }
          ],
          lastMessage: {
            text: 'Hey there!',
            sender: '2',
            createdAt: new Date().toISOString()
          }
        }
      ]
    }).as('getConversations');

    cy.visit('/chat');
    cy.wait('@getConversations');
  });

  describe('Chat Interface', () => {
    it('should display conversations list', () => {
      cy.contains('<PERSON>').should('be.visible');
      cy.contains('Hey there!').should('be.visible');
    });

    it('should show empty state when no conversations exist', () => {
      cy.intercept('GET', '/api/messages/conversations', { body: [] }).as('getEmptyConversations');
      cy.reload();
      cy.wait('@getEmptyConversations');
      
      cy.contains('No conversations yet').should('be.visible');
      cy.contains('Start a new conversation').should('be.visible');
    });

    it('should search for users to start new conversation', () => {
      cy.intercept('GET', '/api/users/search*', {
        statusCode: 200,
        body: [
          { _id: '3', username: 'jane_smith', name: 'Jane Smith', profilePic: '' }
        ]
      }).as('searchUsers');

      cy.get('[data-testid="search-input"]').type('jane');
      cy.wait('@searchUsers');
      
      cy.contains('Jane Smith').should('be.visible');
      cy.contains('@jane_smith').should('be.visible');
    });

    it('should start new conversation with searched user', () => {
      cy.intercept('GET', '/api/users/search*', {
        statusCode: 200,
        body: [
          { _id: '3', username: 'jane_smith', name: 'Jane Smith', profilePic: '' }
        ]
      }).as('searchUsers');

      cy.intercept('POST', '/api/messages/send', {
        statusCode: 200,
        body: {
          _id: 'msg1',
          text: 'Hello Jane!',
          sender: '1',
          conversationId: 'conv2'
        }
      }).as('sendMessage');

      cy.get('[data-testid="search-input"]').type('jane');
      cy.wait('@searchUsers');
      
      cy.contains('Jane Smith').click();
      cy.get('[data-testid="message-input"]').type('Hello Jane!');
      cy.get('[data-testid="send-button"]').click();
      
      cy.wait('@sendMessage');
      cy.contains('Hello Jane!').should('be.visible');
    });
  });

  describe('Message Actions', () => {
    beforeEach(() => {
      // Mock messages for a conversation
      cy.intercept('GET', '/api/messages/conv1', {
        statusCode: 200,
        body: [
          {
            _id: 'msg1',
            text: 'Hey there!',
            sender: { _id: '2', username: 'john_doe', name: 'John Doe' },
            createdAt: new Date(Date.now() - 3600000).toISOString()
          },
          {
            _id: 'msg2',
            text: 'How are you doing?',
            sender: { _id: '1', username: 'testuser', name: 'Test User' },
            createdAt: new Date().toISOString()
          }
        ]
      }).as('getMessages');

      // Click on conversation to open it
      cy.contains('John Doe').click();
      cy.wait('@getMessages');
    });

    it('should display conversation messages', () => {
      cy.contains('Hey there!').should('be.visible');
      cy.contains('How are you doing?').should('be.visible');
    });

    it('should send a new message', () => {
      const messageText = 'This is a test message';
      
      cy.intercept('POST', '/api/messages/send', {
        statusCode: 200,
        body: {
          _id: 'new-msg',
          text: messageText,
          sender: { _id: '1', username: 'testuser', name: 'Test User' },
          conversationId: 'conv1',
          createdAt: new Date().toISOString()
        }
      }).as('sendMessage');

      cy.get('[data-testid="message-input"]').type(messageText);
      cy.get('[data-testid="send-button"]').click();
      
      cy.wait('@sendMessage');
      cy.contains(messageText).should('be.visible');
      cy.get('[data-testid="message-input"]').should('have.value', '');
    });

    it('should send message with Enter key', () => {
      const messageText = 'Message sent with Enter';
      
      cy.intercept('POST', '/api/messages/send', {
        statusCode: 200,
        body: {
          _id: 'enter-msg',
          text: messageText,
          sender: { _id: '1', username: 'testuser', name: 'Test User' },
          conversationId: 'conv1'
        }
      }).as('sendMessageEnter');

      cy.get('[data-testid="message-input"]').type(messageText);
      cy.get('[data-testid="message-input"]').type('{enter}');
      
      cy.wait('@sendMessageEnter');
      cy.contains(messageText).should('be.visible');
    });

    it('should not send empty message', () => {
      cy.get('[data-testid="send-button"]').should('be.disabled');
      
      cy.get('[data-testid="message-input"]').type('   ');
      cy.get('[data-testid="send-button"]').should('be.disabled');
    });

    it('should show typing indicator', () => {
      // Mock socket event for typing
      cy.window().then((win) => {
        if (win.socket) {
          win.socket.emit('typing', { conversationId: 'conv1', userId: '2' });
        }
      });

      cy.contains('John Doe is typing...').should('be.visible');
    });

    it('should show online/offline status', () => {
      // Mock online users
      cy.window().then((win) => {
        if (win.socket) {
          win.socket.emit('userOnline', { userId: '2' });
        }
      });

      cy.get('[data-testid="online-indicator"]').should('be.visible');
    });
  });

  describe('Message Features', () => {
    beforeEach(() => {
      cy.intercept('GET', '/api/messages/conv1', {
        statusCode: 200,
        body: [
          {
            _id: 'msg1',
            text: 'Hey there!',
            sender: { _id: '2', username: 'john_doe', name: 'John Doe' },
            createdAt: new Date().toISOString()
          }
        ]
      }).as('getMessages');

      cy.contains('John Doe').click();
      cy.wait('@getMessages');
    });

    it('should delete own message', () => {
      cy.intercept('DELETE', '/api/messages/msg2', {
        statusCode: 200,
        body: { message: 'Message deleted' }
      }).as('deleteMessage');

      // Send a message first
      cy.intercept('POST', '/api/messages/send', {
        statusCode: 200,
        body: {
          _id: 'msg2',
          text: 'Message to delete',
          sender: { _id: '1', username: 'testuser', name: 'Test User' },
          conversationId: 'conv1'
        }
      }).as('sendMessage');

      cy.get('[data-testid="message-input"]').type('Message to delete');
      cy.get('[data-testid="send-button"]').click();
      cy.wait('@sendMessage');

      // Delete the message
      cy.get('[data-testid="message-menu"]').last().click();
      cy.contains('Delete').click();
      cy.contains('Confirm Delete').click();
      
      cy.wait('@deleteMessage');
      cy.contains('Message to delete').should('not.exist');
    });

    it('should copy message text', () => {
      cy.get('[data-testid="message-menu"]').first().click();
      cy.contains('Copy').click();
      
      // Should show copied confirmation
      cy.contains('Copied to clipboard').should('be.visible');
    });

    it('should show message timestamps', () => {
      // Hover over message to show timestamp
      cy.contains('Hey there!').trigger('mouseover');
      cy.get('[data-testid="message-timestamp"]').should('be.visible');
    });
  });

  describe('Cross-Platform Features', () => {
    beforeEach(() => {
      // Enable cross-platform mode
      cy.intercept('GET', '/api/platforms/status', {
        statusCode: 200,
        body: {
          telegram: true,
          discord: true,
          federation: true
        }
      }).as('getPlatformStatus');

      cy.get('[data-testid="cross-platform-toggle"]').click();
      cy.wait('@getPlatformStatus');
    });

    it('should display federated rooms', () => {
      cy.intercept('GET', '/api/federation/rooms', {
        statusCode: 200,
        body: [
          {
            _id: 'room1',
            name: 'General Chat',
            participants: 5,
            isPublic: true
          }
        ]
      }).as('getFederatedRooms');

      cy.wait('@getFederatedRooms');
      cy.contains('General Chat').should('be.visible');
      cy.contains('5 participants').should('be.visible');
    });

    it('should create new federated room', () => {
      cy.intercept('POST', '/api/federation/rooms', {
        statusCode: 200,
        body: {
          _id: 'new-room',
          name: 'Test Room',
          participants: 1
        }
      }).as('createRoom');

      cy.get('[data-testid="create-room-button"]').click();
      cy.get('[data-testid="room-name-input"]').type('Test Room');
      cy.get('[data-testid="create-room-submit"]').click();
      
      cy.wait('@createRoom');
      cy.contains('Test Room').should('be.visible');
    });

    it('should join existing room', () => {
      cy.intercept('POST', '/api/federation/rooms/*/join', {
        statusCode: 200,
        body: { message: 'Joined room successfully' }
      }).as('joinRoom');

      cy.get('[data-testid="join-room-button"]').click();
      cy.get('[data-testid="room-id-input"]').type('room123');
      cy.get('[data-testid="join-room-submit"]').click();
      
      cy.wait('@joinRoom');
      cy.contains('Joined room successfully').should('be.visible');
    });
  });

  describe('Responsive Chat', () => {
    it('should work on mobile devices', () => {
      cy.viewport(375, 667);
      
      // Should show conversation list in mobile view
      cy.contains('John Doe').should('be.visible');
      
      // Click conversation to open chat
      cy.contains('John Doe').click();
      
      // Should show back button in mobile view
      cy.get('[data-testid="back-button"]').should('be.visible');
      
      // Message input should be visible
      cy.get('[data-testid="message-input"]').should('be.visible');
    });

    it('should show conversation list and chat side by side on desktop', () => {
      cy.viewport(1280, 720);
      
      // Both conversation list and chat should be visible
      cy.contains('John Doe').should('be.visible');
      cy.get('[data-testid="conversation-list"]').should('be.visible');
      cy.get('[data-testid="chat-area"]').should('be.visible');
    });
  });

  describe('Error Handling', () => {
    it('should handle failed message sending', () => {
      cy.intercept('POST', '/api/messages/send', {
        statusCode: 500,
        body: { error: 'Failed to send message' }
      }).as('sendMessageError');

      cy.contains('John Doe').click();
      cy.get('[data-testid="message-input"]').type('This will fail');
      cy.get('[data-testid="send-button"]').click();
      
      cy.wait('@sendMessageError');
      cy.contains('Failed to send message').should('be.visible');
    });

    it('should handle connection errors', () => {
      cy.intercept('GET', '/api/messages/conversations', { forceNetworkError: true }).as('networkError');
      
      cy.reload();
      cy.wait('@networkError');
      
      cy.contains('Unable to load conversations').should('be.visible');
      cy.contains('Retry').should('be.visible');
    });

    it('should retry failed operations', () => {
      cy.intercept('GET', '/api/messages/conversations', { forceNetworkError: true }).as('networkError');
      cy.reload();
      cy.wait('@networkError');
      
      // Mock successful retry
      cy.intercept('GET', '/api/messages/conversations', {
        statusCode: 200,
        body: []
      }).as('retrySuccess');
      
      cy.contains('Retry').click();
      cy.wait('@retrySuccess');
      
      cy.contains('No conversations yet').should('be.visible');
    });
  });
});
