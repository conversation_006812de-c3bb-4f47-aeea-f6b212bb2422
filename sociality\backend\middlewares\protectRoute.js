import User from "../models/userModel.js";
import jwt from "jsonwebtoken";

const protectRoute = async (req, res, next) => {
	try {
		// Check for session-specific cookie first, then fallback to default
		const sessionPath = req.query.session || '';
		const cookieName = sessionPath ? `jwt-sociality${sessionPath.replace(/\//g, '-')}` : 'jwt-sociality';

		let token = req.cookies[cookieName] || req.cookies.jwt || req.cookies['jwt-sociality'];

		console.log('=== PROTECT ROUTE DEBUG ===');
		console.log('Request URL:', req.originalUrl);
		console.log('Session path:', sessionPath);
		console.log('Cookie name:', cookieName);
		console.log('JWT token present:', !!token);
		console.log('All cookies:', Object.keys(req.cookies));
		console.log('Cookie values:', req.cookies);

		if (!token) {
			console.log('❌ protectRoute - No JWT token found');
			return res.status(401).json({ message: "Unauthorized - No token provided" });
		}

		// Validate token format
		if (typeof token !== 'string' || token.split('.').length !== 3) {
			console.log('❌ protectRoute - Invalid JWT token format:', token);
			return res.status(401).json({ message: "Unauthorized - Invalid token format" });
		}

		const decoded = jwt.verify(token, process.env.JWT_SECRET);
		console.log('✅ protectRoute - Token decoded successfully:', { userId: decoded.userId });

		const user = await User.findById(decoded.userId).select("-password");
		console.log('protectRoute - User lookup result:', !!user, user ? { id: user._id, username: user.username, email: user.email } : 'null');

		// Check if user exists after finding by ID
		if (!user) {
			// If user not found (e.g., deleted after token issuance), return unauthorized
			console.error(`❌ Error in protectRoute: User not found for decoded userId ${decoded.userId}`);
			return res.status(401).json({ message: "Unauthorized - User not found" });
		}

		console.log('✅ protectRoute - Authentication successful for user:', user.username);
		req.user = user;
		next();
	} catch (err) {
		console.error("❌ Error in protectRoute:", err.message);
		if (err.name === 'JsonWebTokenError') {
			return res.status(401).json({ message: "Unauthorized - Invalid token" });
		} else if (err.name === 'TokenExpiredError') {
			return res.status(401).json({ message: "Unauthorized - Token expired" });
		}
		res.status(500).json({ message: err.message });
	}
};

export default protectRoute;
