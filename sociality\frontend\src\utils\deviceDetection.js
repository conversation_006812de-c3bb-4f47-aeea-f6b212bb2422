/**
 * Device Detection Utilities
 * Detects device type and capabilities for optimal user experience
 */

/**
 * Check if the current device is mobile
 * @returns {boolean} True if mobile device
 */
export const isMobileDevice = () => {
  // Check user agent for mobile indicators
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  
  // Mobile user agent patterns
  const mobilePatterns = [
    /Android/i,
    /webOS/i,
    /iPhone/i,
    /iPad/i,
    /iPod/i,
    /BlackBerry/i,
    /Windows Phone/i,
    /Mobile/i,
    /Tablet/i
  ];
  
  // Check if any mobile pattern matches
  const isMobileUA = mobilePatterns.some(pattern => pattern.test(userAgent));
  
  // Check screen size (mobile-first approach)
  const isMobileScreen = window.innerWidth <= 768;
  
  // Check for touch capability
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  
  // Combine checks - prioritize user agent, then screen size
  return isMobileUA || (isMobileScreen && isTouchDevice);
};

/**
 * Check if the device is specifically iOS
 * @returns {boolean} True if iOS device
 */
export const isIOS = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  return /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream;
};

/**
 * Check if the device is specifically Android
 * @returns {boolean} True if Android device
 */
export const isAndroid = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  return /Android/i.test(userAgent);
};

/**
 * Check if the browser is Safari (including mobile Safari)
 * @returns {boolean} True if Safari browser
 */
export const isSafari = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  return /Safari/.test(userAgent) && !/Chrome/.test(userAgent) && !/Chromium/.test(userAgent);
};

/**
 * Check if the browser is Chrome (including mobile Chrome)
 * @returns {boolean} True if Chrome browser
 */
export const isChrome = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  return /Chrome/.test(userAgent) && !/Chromium/.test(userAgent);
};

/**
 * Check if popups are likely to be blocked
 * @returns {boolean} True if popups are likely blocked
 */
export const arePopupsLikelyBlocked = () => {
  // Mobile devices often block popups
  if (isMobileDevice()) return true;
  
  // Safari often blocks popups
  if (isSafari()) return true;
  
  // Test if popups are actually blocked
  try {
    const testPopup = window.open('', 'test', 'width=1,height=1');
    if (testPopup) {
      testPopup.close();
      return false;
    }
    return true;
  } catch (e) {
    return true;
  }
};

/**
 * Get the optimal OAuth method for the current device
 * @returns {string} 'redirect' or 'popup'
 */
export const getOptimalOAuthMethod = () => {
  // Always use redirect on mobile devices
  if (isMobileDevice()) return 'redirect';
  
  // Use redirect if popups are likely blocked
  if (arePopupsLikelyBlocked()) return 'redirect';
  
  // Use popup for desktop with popup support
  return 'popup';
};

/**
 * Check if the device supports PWA installation
 * @returns {boolean} True if PWA installation is supported
 */
export const supportsPWAInstall = () => {
  // Check for beforeinstallprompt event support
  if ('onbeforeinstallprompt' in window) return true;
  
  // Check for iOS Safari PWA support
  if (isIOS() && isSafari()) return true;
  
  // Check for Android Chrome PWA support
  if (isAndroid() && isChrome()) return true;
  
  return false;
};

/**
 * Get device information for debugging
 * @returns {Object} Device information object
 */
export const getDeviceInfo = () => {
  return {
    isMobile: isMobileDevice(),
    isIOS: isIOS(),
    isAndroid: isAndroid(),
    isSafari: isSafari(),
    isChrome: isChrome(),
    popupsBlocked: arePopupsLikelyBlocked(),
    optimalOAuth: getOptimalOAuthMethod(),
    supportsPWA: supportsPWAInstall(),
    userAgent: navigator.userAgent,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    },
    screen: {
      width: window.screen.width,
      height: window.screen.height
    },
    touchSupport: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    orientation: window.screen.orientation?.type || 'unknown'
  };
};

/**
 * Log device information to console (for debugging)
 */
export const logDeviceInfo = () => {
  const info = getDeviceInfo();
  console.group('📱 Device Information');
  console.log('Mobile Device:', info.isMobile);
  console.log('iOS:', info.isIOS);
  console.log('Android:', info.isAndroid);
  console.log('Safari:', info.isSafari);
  console.log('Chrome:', info.isChrome);
  console.log('Popups Blocked:', info.popupsBlocked);
  console.log('Optimal OAuth:', info.optimalOAuth);
  console.log('PWA Support:', info.supportsPWA);
  console.log('Touch Support:', info.touchSupport);
  console.log('Viewport:', `${info.viewport.width}x${info.viewport.height}`);
  console.log('Screen:', `${info.screen.width}x${info.screen.height}`);
  console.log('Orientation:', info.orientation);
  console.log('User Agent:', info.userAgent);
  console.groupEnd();
  
  return info;
};

/**
 * Check if the current context is in a mobile browser's in-app browser
 * (like Instagram, Facebook, etc.)
 * @returns {boolean} True if in-app browser
 */
export const isInAppBrowser = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  
  // Common in-app browser patterns
  const inAppPatterns = [
    /FBAN/i,        // Facebook
    /FBAV/i,        // Facebook
    /Instagram/i,   // Instagram
    /Twitter/i,     // Twitter
    /LinkedIn/i,    // LinkedIn
    /WhatsApp/i,    // WhatsApp
    /Snapchat/i,    // Snapchat
    /TikTok/i,      // TikTok
    /WeChat/i,      // WeChat
    /Line/i         // Line
  ];
  
  return inAppPatterns.some(pattern => pattern.test(userAgent));
};

/**
 * Get recommended OAuth strategy based on device and browser
 * @returns {Object} OAuth strategy recommendation
 */
export const getOAuthStrategy = () => {
  const info = getDeviceInfo();
  
  if (info.isMobile) {
    if (isInAppBrowser()) {
      return {
        method: 'redirect',
        reason: 'In-app browser detected - popups not supported',
        openInNewTab: true
      };
    }
    
    return {
      method: 'redirect',
      reason: 'Mobile device - redirect provides better UX',
      openInNewTab: false
    };
  }
  
  if (info.popupsBlocked) {
    return {
      method: 'redirect',
      reason: 'Popup blocker detected',
      openInNewTab: false
    };
  }
  
  return {
    method: 'popup',
    reason: 'Desktop with popup support',
    openInNewTab: false
  };
};

// Auto-log device info in development
if (process.env.NODE_ENV === 'development') {
  // Log device info after page load
  window.addEventListener('load', () => {
    setTimeout(logDeviceInfo, 500);
  });
  
  // Make functions available globally for debugging
  window.deviceInfo = {
    get: getDeviceInfo,
    log: logDeviceInfo,
    isMobile: isMobileDevice,
    oauthStrategy: getOAuthStrategy
  };
}
