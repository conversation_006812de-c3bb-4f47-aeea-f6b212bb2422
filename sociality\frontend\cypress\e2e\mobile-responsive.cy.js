
describe('Mobile and Responsive Tests', () => {
  const viewports = [
    { name: 'iPhone SE', width: 375, height: 667 },
    { name: 'iPhone 12', width: 390, height: 844 },
    { name: 'Samsung Galaxy S21', width: 384, height: 854 },
    { name: 'iPad', width: 768, height: 1024 },
    { name: 'iPad Pro', width: 1024, height: 1366 },
    { name: 'Desktop Small', width: 1280, height: 720 },
    { name: 'Desktop Large', width: 1920, height: 1080 }
  ];

  beforeEach(() => {
    // Mock authenticated user
    cy.window().then((win) => {
      win.localStorage.setItem('user-threads', JSON.stringify({
        _id: 'mobile-test-user',
        name: 'Mobile Test User',
        email: '<EMAIL>',
        username: 'mobiletestuser',
        bio: 'Testing mobile experience',
        profilePic: '',
        isProfileComplete: true
      }));
    });

    // Mock API responses
    cy.intercept('GET', '/api/posts/feed', { fixture: 'posts.json' }).as('getFeed');
    cy.intercept('GET', '/api/users/suggested', { fixture: 'users.json' }).as('getSuggestedUsers');
  });

  describe('Viewport Responsiveness', () => {
    viewports.forEach(viewport => {
      it(`should display correctly on ${viewport.name} (${viewport.width}x${viewport.height})`, () => {
        cy.viewport(viewport.width, viewport.height);
        cy.visit('/');
        cy.wait('@getFeed');

        // Check basic layout elements are visible
        cy.get('header').should('be.visible');
        cy.get('main').should('be.visible');

        // Check navigation is appropriate for viewport
        if (viewport.width < 768) {
          // Mobile: should have hamburger menu or bottom navigation
          cy.get('[data-testid="mobile-menu-button"]').should('be.visible');
        } else {
          // Desktop: should have full navigation
          cy.get('[data-testid="desktop-nav"]').should('be.visible');
        }

        // Check post layout adapts to viewport
        cy.get('[data-testid="post-item"]').should('be.visible');
        cy.get('[data-testid="post-item"]').first().should(($post) => {
          const postWidth = $post.width();
          expect(postWidth).to.be.at.most(viewport.width - 32); // Account for padding
        });

        // Check touch targets are appropriate size on mobile
        if (viewport.width < 768) {
          cy.get('button').should(($buttons) => {
            $buttons.each((index, button) => {
              const rect = button.getBoundingClientRect();
              expect(rect.width).to.be.at.least(44); // Minimum touch target size
              expect(rect.height).to.be.at.least(44);
            });
          });
        }
      });
    });

    it('should handle orientation changes on mobile', () => {
      // Portrait mode
      cy.viewport(375, 667);
      cy.visit('/');
      cy.wait('@getFeed');

      cy.get('[data-testid="post-item"]').should('be.visible');
      
      // Switch to landscape
      cy.viewport(667, 375);
      
      // Content should still be visible and properly laid out
      cy.get('[data-testid="post-item"]').should('be.visible');
      cy.get('header').should('be.visible');
      
      // Check that content doesn't overflow
      cy.get('body').should('not.have.css', 'overflow-x', 'scroll');
    });
  });

  describe('Touch Interactions', () => {
    beforeEach(() => {
      cy.viewport('iphone-x');
    });

    it('should handle touch gestures for liking posts', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      cy.intercept('POST', '/api/posts/like/*', {
        statusCode: 200,
        body: { message: 'Post liked successfully' }
      }).as('likePost');

      // Test tap gesture
      cy.get('[data-testid="like-button"]').first().trigger('touchstart');
      cy.get('[data-testid="like-button"]').first().trigger('touchend');
      cy.wait('@likePost');
    });

    it('should handle swipe gestures for navigation', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Test swipe left to open side menu (if implemented)
      cy.get('body')
        .trigger('touchstart', { clientX: 10, clientY: 100 })
        .trigger('touchmove', { clientX: 200, clientY: 100 })
        .trigger('touchend');

      // Check if side menu opened (adjust selector based on implementation)
      cy.get('[data-testid="side-menu"]').should('be.visible');
    });

    it('should handle pull-to-refresh gesture', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Mock refresh API call
      cy.intercept('GET', '/api/posts/feed', { fixture: 'posts.json' }).as('refreshFeed');

      // Simulate pull-to-refresh
      cy.get('[data-testid="feed-container"]')
        .trigger('touchstart', { clientX: 200, clientY: 50 })
        .trigger('touchmove', { clientX: 200, clientY: 200 })
        .trigger('touchend');

      // Should trigger refresh
      cy.wait('@refreshFeed');
    });

    it('should handle long press for context menus', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Long press on a post
      cy.get('[data-testid="post-item"]').first()
        .trigger('touchstart')
        .wait(800) // Long press duration
        .trigger('touchend');

      // Should show context menu
      cy.get('[data-testid="post-context-menu"]').should('be.visible');
    });
  });

  describe('Mobile Navigation', () => {
    beforeEach(() => {
      cy.viewport('iphone-x');
    });

    it('should provide easy thumb navigation', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Check that primary actions are in thumb-reachable areas
      cy.get('[data-testid="bottom-nav"]').should('be.visible');
      cy.get('[data-testid="bottom-nav"]').should(($nav) => {
        const rect = $nav[0].getBoundingClientRect();
        const viewportHeight = Cypress.config('viewportHeight');
        
        // Bottom nav should be within thumb reach (bottom 25% of screen)
        expect(rect.top).to.be.greaterThan(viewportHeight * 0.75);
      });

      // Test tab navigation
      cy.get('[data-testid="home-tab"]').click();
      cy.url().should('eq', Cypress.config().baseUrl + '/');

      cy.get('[data-testid="search-tab"]').click();
      cy.url().should('include', '/search');

      cy.get('[data-testid="chat-tab"]').click();
      cy.url().should('include', '/chat');
    });

    it('should have accessible mobile menu', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Open mobile menu
      cy.get('[data-testid="mobile-menu-button"]').click();
      cy.get('[data-testid="mobile-menu"]').should('be.visible');

      // Menu should not cover essential content
      cy.get('[data-testid="mobile-menu"]').should(($menu) => {
        const menuRect = $menu[0].getBoundingClientRect();
        expect(menuRect.width).to.be.at.most(Cypress.config('viewportWidth') * 0.8);
      });

      // Should be able to close menu
      cy.get('[data-testid="close-menu-button"]').click();
      cy.get('[data-testid="mobile-menu"]').should('not.be.visible');
    });
  });

  describe('Mobile Forms and Input', () => {
    beforeEach(() => {
      cy.viewport('iphone-x');
    });

    it('should handle virtual keyboard properly', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Focus on post creation input
      cy.get('[data-testid="create-post-input"]').focus();

      // Simulate virtual keyboard appearance
      cy.viewport(375, 375); // Reduced height when keyboard is visible

      // Content should remain accessible
      cy.get('[data-testid="create-post-input"]').should('be.visible');
      cy.get('[data-testid="create-post-button"]').should('be.visible');

      // Should be able to scroll to see hidden content
      cy.scrollTo('bottom');
      cy.get('[data-testid="post-item"]').first().should('be.visible');
    });

    it('should use appropriate input types for mobile', () => {
      cy.clearAppData();
      cy.visit('/auth');

      // Email input should have email type
      cy.get('input[placeholder*="email"]').should('have.attr', 'type', 'email');

      // Should trigger appropriate mobile keyboards
      cy.get('input[type="email"]').focus();
      
      // Username input should have appropriate attributes
      cy.get('input[placeholder*="username"]').should('have.attr', 'autocapitalize', 'none');
    });

    it('should handle form validation on mobile', () => {
      cy.clearAppData();
      cy.viewport('iphone-x');
      cy.visit('/auth');

      // Switch to signup
      cy.contains('Sign up').click();

      // Try to submit empty form
      cy.get('button[type="submit"]').click();

      // Error messages should be visible and not cut off
      cy.get('[data-testid="form-error"]').should('be.visible');
      cy.get('[data-testid="form-error"]').should(($error) => {
        const errorRect = $error[0].getBoundingClientRect();
        expect(errorRect.right).to.be.at.most(Cypress.config('viewportWidth'));
      });
    });
  });

  describe('Mobile Performance', () => {
    beforeEach(() => {
      cy.viewport('iphone-x');
    });

    it('should load quickly on mobile', () => {
      const startTime = Date.now();
      
      cy.visit('/');
      cy.wait('@getFeed');
      
      cy.get('[data-testid="post-item"]').should('be.visible');
      
      cy.then(() => {
        const loadTime = Date.now() - startTime;
        // Mobile should load within 4 seconds (slower than desktop)
        expect(loadTime).to.be.lessThan(4000);
      });
    });

    it('should handle touch scrolling smoothly', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Test smooth scrolling with touch
      for (let i = 0; i < 5; i++) {
        cy.get('body')
          .trigger('touchstart', { clientX: 200, clientY: 300 })
          .trigger('touchmove', { clientX: 200, clientY: 100 })
          .trigger('touchend');
        
        cy.wait(200); // Allow for smooth scrolling
      }

      // Should maintain 60fps (smooth scrolling)
      cy.window().then((win) => {
        if (win.performance.getEntriesByType) {
          const paintEntries = win.performance.getEntriesByType('paint');
          // No specific assertion here, but logging for analysis
          cy.log(`Paint entries: ${paintEntries.length}`);
        }
      });
    });
  });

  describe('Tablet Specific Tests', () => {
    beforeEach(() => {
      cy.viewport('ipad-2');
    });

    it('should utilize tablet screen space effectively', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Should show side panel on tablets
      cy.get('[data-testid="side-panel"]').should('be.visible');
      
      // Should show more content than mobile
      cy.get('[data-testid="post-item"]').should('have.length.greaterThan', 3);

      // Navigation should be optimized for tablet
      cy.get('[data-testid="tablet-nav"]').should('be.visible');
    });

    it('should handle split-screen layouts', () => {
      cy.visit('/');
      cy.wait('@getFeed');

      // Click on a post to open detail view
      cy.get('[data-testid="post-item"]').first().click();

      // Should show split view on tablet
      cy.get('[data-testid="post-detail"]').should('be.visible');
      cy.get('[data-testid="post-list"]').should('be.visible');
    });
  });

  describe('Cross-Platform Consistency', () => {
    it('should maintain feature parity across viewports', () => {
      const features = [
        { selector: '[data-testid="create-post-button"]', name: 'Create Post' },
        { selector: '[data-testid="search-button"]', name: 'Search' },
        { selector: '[data-testid="notifications-button"]', name: 'Notifications' },
        { selector: '[data-testid="chat-button"]', name: 'Chat' }
      ];

      [{ width: 375, height: 667 }, { width: 1280, height: 720 }].forEach(viewport => {
        cy.viewport(viewport.width, viewport.height);
        cy.visit('/');
        cy.wait('@getFeed');

        features.forEach(feature => {
          cy.get(feature.selector).should('exist');
          cy.log(`${feature.name} available on ${viewport.width}x${viewport.height}`);
        });
      });
    });

    it('should provide consistent user experience', () => {
      const actions = [
        () => {
          cy.get('[data-testid="like-button"]').first().click();
        },
        () => {
          cy.get('[data-testid="comment-button"]').first().click();
        },
        () => {
          cy.get('[data-testid="share-button"]').first().click();
        }
      ];

      [375, 768, 1280].forEach(width => {
        cy.viewport(width, 720);
        cy.visit('/');
        cy.wait('@getFeed');

        actions.forEach((action, index) => {
          cy.intercept('POST', '/api/posts/**', { statusCode: 200, body: {} }).as(`action${index}`);
          action();
          cy.wait(`@action${index}`);
        });
      });
    });
  });
});
