/* Prevent scrolling on auth page */
.auth-page-no-scroll {
  overflow: hidden !important;
  height: 100vh !important;
  position: fixed !important;
  width: 100% !important;
  top: 0;
  left: 0;
}

/* Ensure html and body have no scrolling on auth page */
html, body {
  overflow-x: hidden;
}

/* Static gradient background */
.gradient-background {
  position: fixed;
  height: 100vh;
  width: 100vw;
  top: 0;
  left: 0;
  z-index: -1;
  overflow: hidden;
  background: linear-gradient(135deg, #050505 0%, #080808 25%, #060606 50%, #080808 75%, #050505 100%);
}

/* Static accent light */
.accent-light {
  position: absolute;
  width: 700px;
  height: 700px;
  border-radius: 50%;
  background: radial-gradient(circle at center, rgba(0, 204, 133, 0.25) 0%, rgba(0, 121, 185, 0.2) 30%, rgba(0, 0, 0, 0) 70%);
  filter: blur(50px);
  z-index: -1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0.25;
}

.accent-light:nth-child(2) {
  width: 500px;
  height: 500px;
  background: radial-gradient(circle at center, rgba(0, 121, 185, 0.2) 0%, rgba(0, 204, 133, 0.15) 40%, rgba(0, 0, 0, 0) 70%);
  opacity: 0.2;
  top: 45%;
  left: 55%;
}

.accent-light-third {
  width: 300px !important;
  height: 300px !important;
  background: radial-gradient(circle at center, rgba(0, 204, 133, 0.15) 0%, rgba(0, 121, 185, 0.1) 40%, rgba(0, 0, 0, 0) 70%) !important;
  opacity: 0.15 !important;
  top: 65% !important;
  left: 35% !important;
}

/* Static bubbles */
.bubble {
  position: absolute;
  background: linear-gradient(135deg, var(--start-color), var(--end-color));
  border-radius: 50%;
  filter: blur(var(--blur-amount));
  z-index: -1;
  opacity: var(--opacity);
}

/* Glass effect for the auth box */
.glass-effect {
  background: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.05) !important;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3) !important;
}
