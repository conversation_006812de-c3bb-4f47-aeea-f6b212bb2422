/**
 * Debug utility for tracking follow/unfollow state changes
 */

export const debugFollowState = (action, data) => {
    const timestamp = new Date().toISOString();
    console.group(`🔍 FOLLOW DEBUG [${timestamp}] - ${action}`);
    
    if (data.userId) {
        console.log('User ID:', data.userId);
    }
    
    if (data.currentUser) {
        console.log('Current User:', {
            id: data.currentUser._id,
            username: data.currentUser.username,
            followingCount: data.currentUser.following?.length || 0,
            following: data.currentUser.following?.map(f => 
                typeof f === 'string' ? f : f._id
            ) || []
        });
    }
    
    if (data.listState) {
        console.log('List State:', {
            usersCount: data.listState.length,
            users: data.listState.map(u => ({
                id: u._id,
                username: u.username
            }))
        });
    }
    
    if (data.localStorage) {
        const stored = localStorage.getItem('user-threads');
        if (stored) {
            try {
                const parsed = JSON.parse(stored);
                console.log('LocalStorage User:', {
                    id: parsed._id,
                    username: parsed.username,
                    followingCount: parsed.following?.length || 0
                });
            } catch (e) {
                console.log('LocalStorage Error:', e);
            }
        }
    }
    
    console.groupEnd();
};

export const debugModalState = (modalName, isOpen, data = {}) => {
    console.log(`🔄 MODAL DEBUG - ${modalName}:`, {
        isOpen,
        ...data
    });
};
