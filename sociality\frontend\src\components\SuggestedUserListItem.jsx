import { <PERSON><PERSON>, Box, But<PERSON>, Flex, Text, useColorModeValue } from "@chakra-ui/react";
import { Link } from "react-router-dom";
import useFollowUnfollow from "../hooks/useFollowUnfollow";

// Component specifically for list view (e.g., on Search Page)
const SuggestedUserListItem = ({ user }) => {
    const { handleFollowUnfollow, following, updating } = useFollowUnfollow(user);

    // Theme-aware colors
    const bgColor = useColorModeValue("white", "#1E1E1E");
    const hoverBgColor = useColorModeValue("gray.50", "#252525");
    const textColor = useColorModeValue("gray.800", "white");
    const usernameColor = useColorModeValue("gray.600", "gray.400");
    const borderColor = useColorModeValue("gray.200", "rgba(255, 255, 255, 0.08)");

    return (
        <Flex
            gap={5} // Increased gap
            justifyContent={"space-between"}
            alignItems={"center"}
            w="full"
            p={4} // Increased padding
            borderRadius="xl"
            bg={bgColor}
            borderTop={`1px solid ${borderColor}`}
            borderBottom={`1px solid ${borderColor}`}
            _hover={{ bg: hoverBgColor }}
            transition="all 0.2s"
            className="search-user-item"
        >
            {/* Left side: Avatar and User Info */}
            <Flex gap={4} as={Link} to={`/${user.username}`} alignItems="center">
                <Avatar size="lg" src={user.profilePic} name={user.name} /> {/* Larger avatar */}
                <Box>
                    <Text fontSize={"md"} fontWeight={"bold"} color={textColor}> {/* Larger text */}
                        {user.username}
                    </Text>
                    <Text color={usernameColor} fontSize={"sm"}>
                        {user.name}
                    </Text>
                </Box>
            </Flex>

            {/* Right side: Follow/Unfollow Button with improved styling */}
            <Button
                size={"md"} // Larger button
                bg={following ? "transparent" : useColorModeValue("#003838", "#003838")}
                color={useColorModeValue(following ? "gray.700" : "white", "white")}
                borderWidth="1px"
                borderColor={following ? useColorModeValue("gray.300", "gray.600") : useColorModeValue("#003838", "#003838")}
                _hover={{
                    bg: following
                        ? useColorModeValue("gray.100", "rgba(255, 255, 255, 0.1)")
                        : useColorModeValue("#004d4d", "#004d4d"),
                }}
                borderRadius="full"
                fontWeight="medium"
                onClick={handleFollowUnfollow}
                isLoading={updating}
                ml={2}
                px={6} // More horizontal padding
                py={2} // More vertical padding
                fontSize="md" // Larger text
            >
                {following ? "Unfollow" : "Follow"}
            </Button>
        </Flex>
    );
};

export default SuggestedUserListItem;
