import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import {
  Box,
  Flex,
  Text,
  Input,
  InputGroup,
  InputLeftElement,
  Avatar,
  IconButton,
  Switch,
  Button,
  useColorModeValue,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useToast,
} from "@chakra-ui/react";
import {
  SearchIcon,
  ArrowBackIcon,
  PhoneIcon,
  InfoIcon,
} from "@chakra-ui/icons";
import {
  BsChatDots,
  FaGlobe,
  FaTelegram,
  FaDiscord,
  FaSignInAlt,
  FaShare,
  FaTrash,
} from "react-icons/fa";

import MessageContainer from "../components/MessageContainer";
import { useRecoilValue, useRecoilState } from "recoil";
import {
  conversationsAtom,
  selectedConversationAtom,
} from "../atoms/messagesAtom";
import { useSocket } from "../context/SocketContext";
import userAtom from "../atoms/userAtom";

const ChatPage = () => {
  const [selectedConversation, setSelectedConversation] = useRecoilState(selectedConversationAtom);
  const [searchTerm, setSearchTerm] = useState("");
  const [isCrossPlatformMode, setIsCrossPlatformMode] = useState(false);
  const [showCreateRoomModal, setShowCreateRoomModal] = useState(false);
  const [showJoinRoomModal, setShowJoinRoomModal] = useState(false);
  const [showShareRoomModal, setShowShareRoomModal] = useState(false);
  const [showDeleteRoomModal, setShowDeleteRoomModal] = useState(false);
  const [newRoomName, setNewRoomName] = useState("");
  const [joinRoomId, setJoinRoomId] = useState("");
  const [shareRoomId, setShareRoomId] = useState("");
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [federatedRooms, setFederatedRooms] = useState([]);
  const [loadingFederatedRooms, setLoadingFederatedRooms] = useState(false);

  const conversations = useRecoilValue(conversationsAtom);
  const currentUser = useRecoilValue(userAtom);
  const { socket, onlineUsers } = useSocket();
  const toast = useToast();

  // Color mode values
  const bgColor = useColorModeValue("gray.50", "gray.900");
  const sidebarBgColor = useColorModeValue("white", "gray.800");
  const headerBgColor = useColorModeValue("white", "gray.800");
  const textColor = useColorModeValue("gray.800", "white");
  const mutedTextColor = useColorModeValue("gray.600", "gray.400");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  const modalBgColor = useColorModeValue("white", "gray.800");

  // Filter conversations based on search
  const filteredConversations = useMemo(() => {
    if (!searchTerm) return conversations;
    return conversations.filter(conversation =>
      conversation.participants[0]?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      conversation.participants[0]?.name?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [conversations, searchTerm]);

  const handleCrossPlatformToggle = () => {
    setIsCrossPlatformMode(!isCrossPlatformMode);
    setSelectedConversation({});
  };

  const handleShareRoom = (roomId) => {
    setShareRoomId(roomId);
    setShowShareRoomModal(true);
  };

  const handleDeleteRoom = (room) => {
    setSelectedRoom(room);
    setShowDeleteRoomModal(true);
  };

  const formatMessageTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <Flex w="100vw" h="100vh" bg={bgColor}>
      {/* Left Sidebar - Messenger Style */}
      <Flex
        w={{ base: "100%", md: "350px" }}
        h="100%"
        direction="column"
        bg={sidebarBgColor}
        borderRight={{ base: "none", md: `1px solid ${borderColor}` }}
        display={{ base: selectedConversation._id ? "none" : "flex", md: "flex" }}
        overflow="hidden"
      >
        {/* Sidebar Header */}
        <Flex
          p={4}
          alignItems="center"
          justifyContent="space-between"
          borderBottom={`1px solid ${borderColor}`}
          bg={headerBgColor}
        >
          {/* Title */}
          <Flex alignItems="center" gap={3}>
            <Box
              bg="rgba(0, 204, 133, 0.1)"
              p={2}
              borderRadius="lg"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              {isCrossPlatformMode ? <FaGlobe size={18} color="#00CC85" /> : <BsChatDots size={18} color="#00CC85" />}
            </Box>
            <Text fontWeight="bold" fontSize="lg" color={textColor}>
              {isCrossPlatformMode ? "Cross-Platform" : "Messages"}
            </Text>
          </Flex>

          {/* Cross-Platform Toggle */}
          <Switch
            isChecked={isCrossPlatformMode}
            onChange={handleCrossPlatformToggle}
            colorScheme="green"
            size="md"
          />
        </Flex>

        {/* Search Bar */}
        <Box p={4} pt={0}>
          <InputGroup>
            <InputLeftElement pointerEvents="none">
              <SearchIcon color="gray.400" />
            </InputLeftElement>
            <Input
              placeholder="Search user..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              bg={useColorModeValue("gray.50", "gray.700")}
              border="none"
              borderRadius="full"
              _focus={{
                bg: useColorModeValue("white", "gray.600"),
                boxShadow: "0 0 0 1px #00CC85"
              }}
            />
          </InputGroup>
        </Box>

        {/* Recent Contacts Section */}
        <Flex direction="column" flex="1" overflow="hidden">
          <Flex px={4} py={2} justifyContent="space-between" alignItems="center">
            <Text fontSize="sm" color={mutedTextColor} fontWeight="medium">
              {isCrossPlatformMode ? "FEDERATED ROOMS" : "RECENT CONTACTS"}
            </Text>
            {isCrossPlatformMode && (
              <Flex gap={2}>
                <Button
                  size="xs"
                  variant="ghost"
                  color="#00CC85"
                  fontSize="xs"
                  _hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
                  onClick={() => setShowCreateRoomModal(true)}
                >
                  Create
                </Button>
                <Button
                  size="xs"
                  variant="ghost"
                  color="#00A3FF"
                  fontSize="xs"
                  _hover={{ bg: "rgba(0, 163, 255, 0.1)" }}
                  onClick={() => setShowJoinRoomModal(true)}
                >
                  Join
                </Button>
              </Flex>
            )}
          </Flex>

          {/* Contacts/Rooms List */}
          <Box
            flex="1"
            overflowY="auto"
            css={{
              '&::-webkit-scrollbar': { width: '4px' },
              '&::-webkit-scrollbar-track': { background: 'transparent' },
              '&::-webkit-scrollbar-thumb': {
                background: 'rgba(0, 204, 133, 0.2)',
                borderRadius: '10px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: 'rgba(0, 204, 133, 0.3)',
              }
            }}
          >
            {/* Contacts/Rooms List Content */}
            {isCrossPlatformMode ? (
              // Federated Rooms
              federatedRooms.map((room) => (
                <Flex
                  key={room.roomId}
                  p={3}
                  cursor="pointer"
                  bg={selectedConversation?._id === room.roomId ? "rgba(0, 204, 133, 0.1)" : "transparent"}
                  _hover={{ bg: "rgba(0, 204, 133, 0.05)" }}
                  onClick={() => {
                    setSelectedConversation({
                      _id: room.roomId,
                      name: room.name,
                      groupPhoto: room.groupPhoto,
                      isFederated: true,
                      platforms: room.peers || []
                    });
                  }}
                  alignItems="center"
                  gap={3}
                >
                  <Avatar
                    src={room.groupPhoto}
                    name={room.name}
                    size="md"
                    bg="rgba(0, 204, 133, 0.1)"
                  />
                  <Flex direction="column" flex="1" minW="0">
                    <Text fontWeight="medium" fontSize="sm" noOfLines={1}>
                      {room.name}
                    </Text>
                    <Text fontSize="xs" color={mutedTextColor} noOfLines={1}>
                      {room.peers?.length || 0} platforms connected
                    </Text>
                  </Flex>
                </Flex>
              ))
            ) : (
              // Regular Conversations
              filteredConversations.map((conversation) => (
                <Flex
                  key={conversation._id}
                  p={3}
                  cursor="pointer"
                  bg={selectedConversation?._id === conversation._id ? "rgba(0, 204, 133, 0.1)" : "transparent"}
                  _hover={{ bg: "rgba(0, 204, 133, 0.05)" }}
                  onClick={() => setSelectedConversation(conversation)}
                  alignItems="center"
                  gap={3}
                >
                  <Avatar
                    src={conversation.participants[0]?.profilePic}
                    name={conversation.participants[0]?.username}
                    size="md"
                  />
                  <Flex direction="column" flex="1" minW="0">
                    <Text fontWeight="medium" fontSize="sm" noOfLines={1}>
                      {conversation.participants[0]?.name || conversation.participants[0]?.username}
                    </Text>
                    <Text fontSize="xs" color={mutedTextColor} noOfLines={1}>
                      {conversation.lastMessage?.text || "No messages yet"}
                    </Text>
                  </Flex>
                  {conversation.lastMessage && (
                    <Text fontSize="xs" color={mutedTextColor}>
                      {formatMessageTime(conversation.lastMessage.createdAt)}
                    </Text>
                  )}
                </Flex>
              ))
            )}
          </Box>
        </Flex>
      </Flex>

      {/* Right Side - Message Area */}
      <Flex
        flex="1"
        h="100%"
        direction="column"
        bg={bgColor}
        display={{ base: selectedConversation._id ? "flex" : "none", md: "flex" }}
      >
        {selectedConversation._id &&
         // Ensure conversation type matches current mode
         ((isCrossPlatformMode && selectedConversation.isFederated) ||
          (!isCrossPlatformMode && !selectedConversation.isFederated)) ? (
          <>
            {/* Chat Header */}
            <Flex
              p={4}
              alignItems="center"
              gap={3}
              borderBottom={`1px solid ${borderColor}`}
              bg={headerBgColor}
            >
              {/* Back button for mobile */}
              <IconButton
                icon={<ArrowBackIcon />}
                size="sm"
                variant="ghost"
                display={{ base: "flex", md: "none" }}
                onClick={() => setSelectedConversation({})}
              />

              {/* User/Room Info */}
              <Avatar
                src={selectedConversation.isFederated ? selectedConversation.groupPhoto : selectedConversation.userProfilePic}
                name={selectedConversation.name || selectedConversation.username}
                size="md"
              />
              <Flex direction="column" flex="1">
                <Text fontWeight="bold" fontSize="lg">
                  {selectedConversation.name || selectedConversation.username}
                </Text>
                <Text fontSize="sm" color={mutedTextColor}>
                  {selectedConversation.isFederated
                    ? `${selectedConversation.platforms?.length || 0} platforms connected`
                    : "Online"
                  }
                </Text>
              </Flex>

              {/* Header Actions */}
              <Flex gap={2}>
                <IconButton
                  icon={<PhoneIcon />}
                  size="sm"
                  variant="ghost"
                  color={textColor}
                />
                <IconButton
                  icon={<InfoIcon />}
                  size="sm"
                  variant="ghost"
                  color={textColor}
                />
              </Flex>
            </Flex>

            {/* Message Container */}
            <MessageContainer
              onShareRoom={handleShareRoom}
              onDeleteRoom={handleDeleteRoom}
            />
          </>
        ) : (
          <Flex
            direction="column"
            align="center"
            justify="center"
            h="full"
            w="full"
            color={textColor}
            bg={bgColor}
            p={8}
          >
            <Box
              p={6}
              borderRadius="full"
              bg="rgba(0, 204, 133, 0.05)"
              mb={6}
              boxShadow="0 0 30px rgba(0, 204, 133, 0.1)"
            >
              {isCrossPlatformMode ? (
                <FaGlobe size={40} color="#00CC85" />
              ) : (
                <BsChatDots size={40} color="#00CC85" />
              )}
            </Box>
            <Text fontSize="xl" fontWeight="bold" mb={2} textAlign="center">
              {isCrossPlatformMode ? "Select a federated room" : "Select a conversation"}
            </Text>
            <Text color={mutedTextColor} textAlign="center" maxW="400px">
              {isCrossPlatformMode
                ? "Choose a federated room from the sidebar to start cross-platform messaging"
                : "Choose a conversation from the sidebar to start messaging"
              }
            </Text>
          </Flex>
        )}
      </Flex>
    </Flex>

    {/* Create Room Modal */}
    <Modal isOpen={showCreateRoomModal} onClose={() => setShowCreateRoomModal(false)} isCentered>
      <ModalOverlay bg="blackAlpha.800" />
      <ModalContent bg={modalBgColor} color={textColor} border={`1px solid ${borderColor}`}>
        <ModalHeader>
          <Flex alignItems="center" gap={2}>
            <Box
              bg="rgba(0, 204, 133, 0.1)"
              p={2}
              borderRadius="lg"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              <FaGlobe size={16} color="#00CC85" />
            </Box>
            <Text>Create Cross-Platform Room</Text>
          </Flex>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text fontSize="sm" color={mutedTextColor} mb={4}>
            Create a private room that allows messaging across Sociality, Telegram, and Discord platforms.
          </Text>
          <Input
            placeholder="Enter room name..."
            value={newRoomName}
            onChange={(e) => setNewRoomName(e.target.value)}
            bg={useColorModeValue("gray.50", "gray.700")}
            border="none"
            _focus={{
              bg: useColorModeValue("white", "gray.600"),
              boxShadow: "0 0 0 1px #00CC85"
            }}
          />
        </ModalBody>
        <ModalFooter>
          <Button
            variant="ghost"
            mr={3}
            onClick={() => setShowCreateRoomModal(false)}
            color={mutedTextColor}
          >
            Cancel
          </Button>
          <Button
            bg="#00CC85"
            color="white"
            _hover={{ bg: "#00B377" }}
            onClick={() => {
              // Handle room creation
              toast({
                title: "Room Created",
                description: `Room "${newRoomName}" created successfully!`,
                status: "success",
                duration: 3000,
                isClosable: true,
              });
              setNewRoomName("");
              setShowCreateRoomModal(false);
            }}
            isDisabled={!newRoomName.trim()}
          >
            Create Room
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>

    {/* Join Room Modal */}
    <Modal isOpen={showJoinRoomModal} onClose={() => setShowJoinRoomModal(false)} isCentered>
      <ModalOverlay bg="blackAlpha.800" />
      <ModalContent bg={modalBgColor} color={textColor} border={`1px solid ${borderColor}`}>
        <ModalHeader>
          <Flex alignItems="center" gap={2}>
            <Box
              bg="rgba(0, 163, 255, 0.1)"
              p={2}
              borderRadius="lg"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              <FaSignInAlt size={16} color="#00A3FF" />
            </Box>
            <Text>Join Cross-Platform Room</Text>
          </Flex>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text fontSize="sm" color={mutedTextColor} mb={4}>
            Enter a Room ID to join a private cross-platform room.
          </Text>
          <Input
            placeholder="Enter Room ID..."
            value={joinRoomId}
            onChange={(e) => setJoinRoomId(e.target.value)}
            bg={useColorModeValue("gray.50", "gray.700")}
            border="none"
            _focus={{
              bg: useColorModeValue("white", "gray.600"),
              boxShadow: "0 0 0 1px #00A3FF"
            }}
          />
        </ModalBody>
        <ModalFooter>
          <Button
            variant="ghost"
            mr={3}
            onClick={() => setShowJoinRoomModal(false)}
            color={mutedTextColor}
          >
            Cancel
          </Button>
          <Button
            bg="#00A3FF"
            color="white"
            _hover={{ bg: "#0092E6" }}
            onClick={() => {
              // Handle room joining
              toast({
                title: "Joined Room",
                description: `Successfully joined room!`,
                status: "success",
                duration: 3000,
                isClosable: true,
              });
              setJoinRoomId("");
              setShowJoinRoomModal(false);
            }}
            isDisabled={!joinRoomId.trim()}
          >
            Join Room
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ChatPage;
