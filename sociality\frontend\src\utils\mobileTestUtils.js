/**
 * Mobile Test Utilities
 * Helper functions to test mobile responsiveness
 */

/**
 * Check if the current viewport is mobile
 */
export const isMobileViewport = () => {
  return window.innerWidth <= 767;
};

/**
 * Check if touch is supported
 */
export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

/**
 * Get current viewport size category
 */
export const getViewportCategory = () => {
  const width = window.innerWidth;
  
  if (width < 320) return 'xs';
  if (width < 375) return 'sm';
  if (width < 414) return 'md';
  if (width < 480) return 'lg';
  if (width < 768) return 'xl';
  if (width < 1024) return '2xl';
  return '3xl';
};

/**
 * Test if elements have proper touch targets (minimum 44px)
 */
export const checkTouchTargets = () => {
  const buttons = document.querySelectorAll('button, [role="button"], a');
  const issues = [];
  
  buttons.forEach((button, index) => {
    const rect = button.getBoundingClientRect();
    if (rect.width < 44 || rect.height < 44) {
      issues.push({
        element: button,
        index,
        width: rect.width,
        height: rect.height,
        selector: button.tagName + (button.className ? '.' + button.className.split(' ')[0] : '')
      });
    }
  });
  
  return issues;
};

/**
 * Test if navigation is properly positioned on mobile
 */
export const checkMobileNavigation = () => {
  const navbar = document.querySelector('.glass-navbar');
  if (!navbar) return { error: 'Navigation not found' };
  
  const rect = navbar.getBoundingClientRect();
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  };
  
  return {
    position: {
      bottom: viewport.height - rect.bottom,
      left: rect.left,
      width: rect.width,
      height: rect.height
    },
    isCentered: Math.abs(rect.left + rect.width / 2 - viewport.width / 2) < 10,
    isAtBottom: rect.bottom > viewport.height - 50,
    viewport
  };
};

/**
 * Test modal responsiveness
 */
export const checkModalResponsiveness = () => {
  const modals = document.querySelectorAll('.chakra-modal__content');
  const results = [];
  
  modals.forEach((modal, index) => {
    const rect = modal.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };
    
    results.push({
      index,
      fits: rect.width <= viewport.width - 32 && rect.height <= viewport.height - 32,
      size: {
        width: rect.width,
        height: rect.height
      },
      margins: {
        left: rect.left,
        right: viewport.width - rect.right,
        top: rect.top,
        bottom: viewport.height - rect.bottom
      }
    });
  });
  
  return results;
};

/**
 * Run comprehensive mobile responsiveness test
 */
export const runMobileResponsivenessTest = () => {
  const results = {
    timestamp: new Date().toISOString(),
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight,
      category: getViewportCategory(),
      isMobile: isMobileViewport(),
      isTouch: isTouchDevice()
    },
    touchTargets: checkTouchTargets(),
    navigation: checkMobileNavigation(),
    modals: checkModalResponsiveness(),
    features: {
      hasViewportMeta: !!document.querySelector('meta[name="viewport"]'),
      hasManifest: !!document.querySelector('link[rel="manifest"]'),
      hasAppleTouchIcon: !!document.querySelector('link[rel="apple-touch-icon"]'),
      hasThemeColor: !!document.querySelector('meta[name="theme-color"]')
    }
  };
  
  // Calculate score
  let score = 100;
  
  // Deduct points for touch target issues
  score -= results.touchTargets.length * 5;
  
  // Deduct points if navigation is not properly positioned
  if (!results.navigation.isCentered) score -= 10;
  if (!results.navigation.isAtBottom) score -= 10;
  
  // Deduct points for missing PWA features
  if (!results.features.hasViewportMeta) score -= 20;
  if (!results.features.hasManifest) score -= 10;
  if (!results.features.hasThemeColor) score -= 5;
  
  results.score = Math.max(0, score);
  results.grade = score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F';
  
  return results;
};

/**
 * Log mobile test results to console
 */
export const logMobileTestResults = () => {
  const results = runMobileResponsivenessTest();
  
  console.group('🔍 Mobile Responsiveness Test Results');
  console.log('📱 Viewport:', results.viewport);
  console.log('📊 Score:', results.score, '| Grade:', results.grade);
  
  if (results.touchTargets.length > 0) {
    console.warn('⚠️ Touch Target Issues:', results.touchTargets);
  } else {
    console.log('✅ All touch targets are properly sized');
  }
  
  console.log('🧭 Navigation:', results.navigation);
  console.log('🪟 Modals:', results.modals);
  console.log('🔧 PWA Features:', results.features);
  
  console.groupEnd();
  
  return results;
};

// Auto-run test in development mode
if (process.env.NODE_ENV === 'development') {
  // Run test after page load
  window.addEventListener('load', () => {
    setTimeout(logMobileTestResults, 1000);
  });
  
  // Make functions available globally for manual testing
  window.mobileTest = {
    run: logMobileTestResults,
    check: runMobileResponsivenessTest,
    touchTargets: checkTouchTargets,
    navigation: checkMobileNavigation,
    modals: checkModalResponsiveness
  };
}
