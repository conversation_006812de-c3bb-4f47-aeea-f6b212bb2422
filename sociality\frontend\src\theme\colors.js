/**
 * Application color palette
 * Contains all color definitions used throughout the application
 * Supports both light and dark modes
 */
export const colors = {
  gray: {
    light: "#616161",
    dark: "#1e1e1e",
  },
  brand: {
    // Primary colors from logo
    primary: {
      100: "#e6fff5", // Light tint
      200: "#b3ffe4",
      300: "#80ffcf",
      400: "#4dffb8",
      500: "#00cc85", // Main green from logo
      600: "#009985", // Middle green from logo
      700: "#006785", // Dark green-blue from logo
      800: "#004d63",
      900: "#003342",
    },
    secondary: {
      100: "#e6f4ff", // Light tint
      200: "#b3dfff",
      300: "#80c9ff",
      400: "#4db3ff",
      500: "#0079b9", // Main blue from logo
      600: "#00608a",
      700: "#003d5b", // Dark blue from logo
      800: "#002e44",
      900: "#001f2e",
    },
    dark: {
      100: "#1a1a1a",
      200: "#171717",
      300: "#141414",
      400: "#121212",
      500: "#101010", // Main dark background
      600: "#0d0d0d",
      700: "#0a0a0a",
      800: "#080808",
      900: "#050505",
    },
    light: {
      100: "#ffffff", // Pure white
      200: "#f7fafc", // Very light gray
      300: "#edf2f7", // Light gray
      400: "#e2e8f0", // Medium light gray
      500: "#cbd5e0", // Main light background
      600: "#a0aec0", // Medium gray
      700: "#718096", // Dark gray
      800: "#4a5568", // Darker gray
      900: "#2d3748", // Very dark gray
    },
  },
  // Theme-specific semantic colors
  semantic: {
    bg: {
      primary: {
        light: "#ffffff",
        dark: "#101010",
      },
      secondary: {
        light: "#f7fafc",
        dark: "#1a1a1a",
      },
      card: {
        light: "#ffffff",
        dark: "#1a1a1a",
      },
      hover: {
        light: "#f7fafc",
        dark: "#1e1e1e",
      },
    },
    text: {
      primary: {
        light: "#2d3748",
        dark: "#ffffff",
      },
      secondary: {
        light: "#4a5568",
        dark: "#a0aec0",
      },
      muted: {
        light: "#718096",
        dark: "#616161",
      },
    },
    border: {
      primary: {
        light: "rgba(0, 0, 0, 0.08)",
        dark: "rgba(255, 255, 255, 0.08)",
      },
      hover: {
        light: "rgba(0, 0, 0, 0.12)",
        dark: "rgba(255, 255, 255, 0.12)",
      },
    },
  },
};
