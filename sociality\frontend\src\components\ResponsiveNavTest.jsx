/**
 * Responsive Navigation Test Component
 * Test component to verify navigation positioning on mobile vs desktop
 */

import React from 'react';
import { 
  Box, 
  VStack, 
  HStack, 
  Text, 
  Badge, 
  Button,
  Grid,
  GridItem,
  Divider
} from '@chakra-ui/react';
import { useDynamicHeader } from '../hooks/useDynamicHeader';

const ResponsiveNavTest = () => {
  const headerData = useDynamicHeader();
  
  const deviceInfo = {
    width: window.innerWidth,
    height: window.innerHeight,
    category: headerData.deviceCategory,
    isMobile: headerData.isMobile,
    orientation: headerData.orientation,
    userAgent: navigator.userAgent.substring(0, 60) + '...'
  };

  const navPosition = headerData.responsive.navPosition.base;
  const navDirection = headerData.responsive.navDirection.base;

  const getDeviceType = () => {
    if (deviceInfo.width < 768) return 'Mobile';
    if (deviceInfo.width < 1024) return 'Tablet';
    return 'Desktop';
  };

  const getExpectedNavPosition = () => {
    if (deviceInfo.width < 768) {
      return 'Bottom (Mobile)';
    } else {
      return 'Left Side (Desktop/Tablet)';
    }
  };

  return (
    <Box p={6} maxW="900px" mx="auto" bg="gray.50" borderRadius="lg">
      <VStack spacing={6} align="stretch">
        <Text fontSize="2xl" fontWeight="bold" textAlign="center">
          📱💻 Responsive Navigation Test
        </Text>
        
        {/* Device Detection */}
        <Box p={4} bg="white" borderRadius="md" shadow="sm">
          <Text fontWeight="bold" mb={3}>Device Detection:</Text>
          <Grid templateColumns="repeat(2, 1fr)" gap={3}>
            <GridItem>
              <Text fontSize="sm">
                Type: <Badge colorScheme="blue">{getDeviceType()}</Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Category: <Badge colorScheme="purple">{deviceInfo.category}</Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Mobile: <Badge colorScheme={deviceInfo.isMobile ? 'green' : 'red'}>
                  {deviceInfo.isMobile ? 'Yes' : 'No'}
                </Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Viewport: <Badge colorScheme="gray">{deviceInfo.width}×{deviceInfo.height}</Badge>
              </Text>
            </GridItem>
          </Grid>
        </Box>

        {/* Navigation Configuration */}
        <Box p={4} bg="white" borderRadius="md" shadow="sm">
          <Text fontWeight="bold" mb={3}>Navigation Configuration:</Text>
          <Grid templateColumns="repeat(2, 1fr)" gap={3}>
            <GridItem>
              <Text fontSize="sm">
                Expected Position: <Badge colorScheme="orange">{getExpectedNavPosition()}</Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Direction: <Badge colorScheme="teal">{navDirection}</Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Left: <Badge variant="outline">{navPosition.left || 'auto'}</Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Bottom: <Badge variant="outline">{navPosition.bottom || 'auto'}</Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Top: <Badge variant="outline">{navPosition.top || 'auto'}</Badge>
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="sm">
                Transform: <Badge variant="outline">{navPosition.transform || 'none'}</Badge>
              </Text>
            </GridItem>
          </Grid>
        </Box>

        {/* Position Analysis */}
        <Box p={4} bg={deviceInfo.width < 768 ? "blue.50" : "green.50"} borderRadius="md">
          <Text fontWeight="bold" mb={3}>Position Analysis:</Text>
          <VStack align="start" spacing={2}>
            {deviceInfo.width < 768 ? (
              <>
                <Text fontSize="sm" color="blue.700">
                  ✅ <strong>Mobile Mode Active</strong> - Navigation should be at bottom
                </Text>
                <Text fontSize="sm">
                  • Direction: {navDirection} (should be 'row')
                </Text>
                <Text fontSize="sm">
                  • Position: bottom: {navPosition.bottom}, left: {navPosition.left}
                </Text>
                <Text fontSize="sm">
                  • Width: {navPosition.width} (should be '100%')
                </Text>
                <Text fontSize="sm">
                  • Border Radius: {navPosition.borderRadius} (should be '0px')
                </Text>
              </>
            ) : (
              <>
                <Text fontSize="sm" color="green.700">
                  ✅ <strong>Desktop Mode Active</strong> - Navigation should be on left side
                </Text>
                <Text fontSize="sm">
                  • Direction: {navDirection} (should be 'column')
                </Text>
                <Text fontSize="sm">
                  • Position: left: {navPosition.left}, top: {navPosition.top}
                </Text>
                <Text fontSize="sm">
                  • Transform: {navPosition.transform} (should include 'translateY(-50%)')
                </Text>
                <Text fontSize="sm">
                  • Border Radius: {navPosition.borderRadius} (should be '12px')
                </Text>
              </>
            )}
          </VStack>
        </Box>

        {/* Validation Results */}
        <Box p={4} bg="white" borderRadius="md" shadow="sm" border="2px solid" borderColor={
          (deviceInfo.width < 768 && navDirection === 'row' && navPosition.bottom === '0px') ||
          (deviceInfo.width >= 768 && navDirection === 'column' && navPosition.left === '16px')
            ? 'green.200' : 'red.200'
        }>
          <Text fontWeight="bold" mb={3}>Validation Results:</Text>
          <VStack align="start" spacing={2}>
            {deviceInfo.width < 768 ? (
              <>
                <Text fontSize="sm" color={navDirection === 'row' ? 'green.600' : 'red.600'}>
                  {navDirection === 'row' ? '✅' : '❌'} Direction: {navDirection} (expected: row)
                </Text>
                <Text fontSize="sm" color={navPosition.bottom === '0px' ? 'green.600' : 'red.600'}>
                  {navPosition.bottom === '0px' ? '✅' : '❌'} Bottom Position: {navPosition.bottom} (expected: 0px)
                </Text>
                <Text fontSize="sm" color={navPosition.width === '100%' ? 'green.600' : 'red.600'}>
                  {navPosition.width === '100%' ? '✅' : '❌'} Width: {navPosition.width} (expected: 100%)
                </Text>
                <Text fontSize="sm" color={navPosition.borderRadius === '0px' ? 'green.600' : 'red.600'}>
                  {navPosition.borderRadius === '0px' ? '✅' : '❌'} Border Radius: {navPosition.borderRadius} (expected: 0px)
                </Text>
              </>
            ) : (
              <>
                <Text fontSize="sm" color={navDirection === 'column' ? 'green.600' : 'red.600'}>
                  {navDirection === 'column' ? '✅' : '❌'} Direction: {navDirection} (expected: column)
                </Text>
                <Text fontSize="sm" color={navPosition.left === '16px' ? 'green.600' : 'red.600'}>
                  {navPosition.left === '16px' ? '✅' : '❌'} Left Position: {navPosition.left} (expected: 16px)
                </Text>
                <Text fontSize="sm" color={navPosition.top === '50%' ? 'green.600' : 'red.600'}>
                  {navPosition.top === '50%' ? '✅' : '❌'} Top Position: {navPosition.top} (expected: 50%)
                </Text>
                <Text fontSize="sm" color={navPosition.borderRadius === '12px' ? 'green.600' : 'red.600'}>
                  {navPosition.borderRadius === '12px' ? '✅' : '❌'} Border Radius: {navPosition.borderRadius} (expected: 12px)
                </Text>
              </>
            )}
          </VStack>
        </Box>

        <Divider />

        {/* Test Actions */}
        <VStack spacing={3}>
          <Button
            colorScheme="blue"
            onClick={() => {
              console.log('📱💻 Responsive Navigation Test Results:');
              console.log('Device Info:', deviceInfo);
              console.log('Navigation Position:', navPosition);
              console.log('Navigation Direction:', navDirection);
              console.log('Header Data:', headerData);
            }}
          >
            Log Test Results
          </Button>
          
          <Text fontSize="sm" color="gray.600" textAlign="center">
            Resize your browser window to test responsive behavior.
            Mobile (&lt;768px) = Bottom navigation | Desktop (≥768px) = Left navigation
          </Text>
        </VStack>
      </VStack>
    </Box>
  );
};

export default ResponsiveNavTest;
