/**
 * Mobile-Friendly OAuth Utilities
 * Provides optimal OAuth experience across all devices
 */

import { getOAuthStrategy, isMobileDevice, isInAppBrowser } from './deviceDetection';
import { googleOAuthPopup } from './oauthPopup';

/**
 * Store OAuth state before redirect
 * @param {Object} state - State to store
 */
const storeOAuthState = (state) => {
  const stateKey = 'oauth_state_' + Date.now();
  sessionStorage.setItem(stateKey, JSON.stringify({
    ...state,
    timestamp: Date.now(),
    returnUrl: window.location.pathname + window.location.search
  }));
  sessionStorage.setItem('oauth_state_key', stateKey);
};

/**
 * Retrieve and clear OAuth state after redirect
 * @returns {Object|null} Stored state or null
 */
const retrieveOAuthState = () => {
  const stateKey = sessionStorage.getItem('oauth_state_key');
  if (!stateKey) return null;
  
  const stateData = sessionStorage.getItem(stateKey);
  if (!stateData) return null;
  
  // Clean up
  sessionStorage.removeItem(stateKey);
  sessionStorage.removeItem('oauth_state_key');
  
  try {
    const state = JSON.parse(stateData);
    // Check if state is not too old (5 minutes max)
    if (Date.now() - state.timestamp > 5 * 60 * 1000) {
      return null;
    }
    return state;
  } catch (e) {
    return null;
  }
};

/**
 * Initiate Google OAuth with mobile-optimized flow
 * @param {Object} options - OAuth options
 * @returns {Promise} Promise that resolves with user data
 */
export const initiateGoogleOAuth = (options = {}) => {
  return new Promise((resolve, reject) => {
    const strategy = getOAuthStrategy();
    
    console.log('🔐 OAuth Strategy:', strategy);
    
    if (strategy.method === 'redirect') {
      // Store state for redirect flow
      storeOAuthState({
        provider: 'google',
        ...options
      });
      
      // Use redirect flow for mobile devices
      const redirectUrl = '/api/auth/google';
      
      if (strategy.openInNewTab && !isInAppBrowser()) {
        // Open in new tab for in-app browsers (if not actually in-app)
        const newTab = window.open(redirectUrl, '_blank');
        if (!newTab) {
          reject(new Error('Please allow popups and try again'));
          return;
        }
        
        // Monitor the new tab
        const checkClosed = setInterval(() => {
          if (newTab.closed) {
            clearInterval(checkClosed);
            // Check if OAuth was successful
            setTimeout(() => {
              const urlParams = new URLSearchParams(window.location.search);
              if (urlParams.get('oauth') === 'success') {
                resolve({ redirected: true });
              } else {
                reject(new Error('OAuth was cancelled or failed'));
              }
            }, 1000);
          }
        }, 1000);
      } else {
        // Direct redirect
        window.location.href = redirectUrl;
        // This won't resolve immediately, but that's expected for redirects
      }
    } else {
      // Use popup flow for desktop
      googleOAuthPopup(false)
        .then(resolve)
        .catch(error => {
          console.error('Popup OAuth failed, falling back to redirect:', error);
          
          // Fallback to redirect if popup fails
          storeOAuthState({
            provider: 'google',
            fallback: true,
            ...options
          });
          
          window.location.href = '/api/auth/google';
        });
    }
  });
};

/**
 * Handle OAuth callback after redirect
 * Should be called on app initialization
 * @returns {Promise} Promise that resolves with user data or null
 */
export const handleOAuthCallback = async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const oauthSuccess = urlParams.get('oauth');
  const oauthError = urlParams.get('error');
  const setupRequired = urlParams.get('setup');
  const sessionPath = urlParams.get('session');
  
  if (oauthSuccess === 'success') {
    try {
      // Fetch user data
      const response = await fetch(`/api/auth/oauth/user?session=${sessionPath || ''}`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const userData = await response.json();
        userData.setupRequired = setupRequired === 'required';
        userData.sessionPath = sessionPath;
        
        // Clean up URL
        const cleanUrl = window.location.pathname;
        window.history.replaceState({}, document.title, cleanUrl);
        
        return userData;
      } else {
        throw new Error('Failed to fetch user data');
      }
    } catch (error) {
      console.error('OAuth callback error:', error);
      throw error;
    }
  } else if (oauthError) {
    // Clean up URL
    const cleanUrl = window.location.pathname;
    window.history.replaceState({}, document.title, cleanUrl);
    
    throw new Error(oauthError === 'oauth_failed' ? 'Google login failed' : oauthError);
  }
  
  return null;
};

/**
 * Check if current page load is from OAuth redirect
 * @returns {boolean} True if OAuth redirect
 */
export const isOAuthCallback = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.has('oauth') || urlParams.has('error');
};

/**
 * Mobile-optimized OAuth button handler
 * @param {Function} onSuccess - Success callback
 * @param {Function} onError - Error callback
 * @param {Function} setLoading - Loading state setter
 */
export const handleMobileOAuth = async (onSuccess, onError, setLoading) => {
  try {
    setLoading(true);
    
    const strategy = getOAuthStrategy();
    console.log('🔐 Using OAuth strategy:', strategy.method, '-', strategy.reason);
    
    if (strategy.method === 'redirect') {
      // For redirect flow, we store state and redirect
      storeOAuthState({
        provider: 'google',
        timestamp: Date.now()
      });
      
      // Show loading message for mobile users
      if (isMobileDevice()) {
        // Brief delay to show loading state
        setTimeout(() => {
          window.location.href = '/api/auth/google';
        }, 500);
      } else {
        window.location.href = '/api/auth/google';
      }
    } else {
      // Use popup flow
      const userData = await googleOAuthPopup(false);
      onSuccess(userData);
    }
  } catch (error) {
    console.error('OAuth error:', error);
    setLoading(false);
    
    if (error.message.includes('Popup blocked')) {
      // Fallback to redirect
      console.log('Popup blocked, falling back to redirect');
      storeOAuthState({
        provider: 'google',
        fallback: true,
        timestamp: Date.now()
      });
      window.location.href = '/api/auth/google';
    } else {
      onError(error);
    }
  }
};

/**
 * Get user-friendly OAuth instructions based on device
 * @returns {Object} Instructions object
 */
export const getOAuthInstructions = () => {
  const strategy = getOAuthStrategy();
  
  if (strategy.method === 'redirect') {
    if (isMobileDevice()) {
      return {
        title: 'Continue with Google',
        description: 'You\'ll be redirected to Google to sign in',
        buttonText: 'Continue with Google',
        icon: '🔄'
      };
    } else {
      return {
        title: 'Sign in with Google',
        description: 'You\'ll be redirected to Google to sign in',
        buttonText: 'Sign in with Google',
        icon: '🔄'
      };
    }
  } else {
    return {
      title: 'Sign in with Google',
      description: 'A popup will open for you to sign in',
      buttonText: 'Sign in with Google',
      icon: '🪟'
    };
  }
};

/**
 * Initialize mobile OAuth handling
 * Should be called on app startup
 */
export const initializeMobileOAuth = () => {
  // Log OAuth strategy for debugging
  if (process.env.NODE_ENV === 'development') {
    const strategy = getOAuthStrategy();
    console.log('🔐 OAuth initialized with strategy:', strategy);
  }
  
  // Make functions available globally for debugging
  if (process.env.NODE_ENV === 'development') {
    window.mobileOAuth = {
      strategy: getOAuthStrategy,
      initiate: initiateGoogleOAuth,
      handleCallback: handleOAuthCallback,
      isCallback: isOAuthCallback,
      instructions: getOAuthInstructions
    };
  }
};

// Auto-initialize in development
if (process.env.NODE_ENV === 'development') {
  window.addEventListener('load', () => {
    setTimeout(initializeMobileOAuth, 100);
  });
}
