/**
 * Mobile Modal Enhancements
 * Improves modal behavior and appearance on mobile devices
 */

/* ===== MOBILE MODAL BASE STYLES ===== */

/* Ensure modals work well on mobile */
@media (max-width: 767px) {
  /* Modal overlay optimizations */
  .chakra-modal__overlay {
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
  }
  
  /* Modal content mobile optimizations */
  .chakra-modal__content {
    margin: 16px !important;
    max-height: calc(100vh - 32px) !important;
    max-width: calc(100vw - 32px) !important;
    border-radius: 16px !important;
    overflow: hidden;
  }
  
  /* Modal header mobile optimizations */
  .chakra-modal__header {
    padding: 16px 20px 12px 20px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
  }
  
  /* Modal body mobile optimizations */
  .chakra-modal__body {
    padding: 0 20px 16px 20px !important;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Modal footer mobile optimizations */
  .chakra-modal__footer {
    padding: 12px 20px 20px 20px !important;
    gap: 12px !important;
  }
  
  /* Close button mobile optimizations */
  .chakra-modal__close-btn {
    top: 12px !important;
    right: 16px !important;
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
  }
}

/* ===== MOBILE MODAL SIZES ===== */

/* Small mobile screens (320px - 374px) */
@media (max-width: 374px) {
  .chakra-modal__content {
    margin: 12px !important;
    max-height: calc(100vh - 24px) !important;
    max-width: calc(100vw - 24px) !important;
    border-radius: 12px !important;
  }
  
  .chakra-modal__header {
    padding: 12px 16px 8px 16px !important;
    font-size: 16px !important;
  }
  
  .chakra-modal__body {
    padding: 0 16px 12px 16px !important;
  }
  
  .chakra-modal__footer {
    padding: 8px 16px 16px 16px !important;
    gap: 8px !important;
  }
}

/* Large mobile screens (414px - 767px) */
@media (min-width: 414px) and (max-width: 767px) {
  .chakra-modal__content {
    margin: 20px !important;
    max-height: calc(100vh - 40px) !important;
    max-width: calc(100vw - 40px) !important;
    border-radius: 20px !important;
  }
  
  .chakra-modal__header {
    padding: 20px 24px 16px 24px !important;
    font-size: 20px !important;
  }
  
  .chakra-modal__body {
    padding: 0 24px 20px 24px !important;
  }
  
  .chakra-modal__footer {
    padding: 16px 24px 24px 24px !important;
    gap: 16px !important;
  }
}

/* ===== MOBILE MODAL ANIMATIONS ===== */

/* Optimize modal animations for mobile */
@media (max-width: 767px) {
  .chakra-modal__content {
    animation-duration: 0.2s !important;
    transition: all 0.2s ease-out !important;
  }
  
  /* Reduce motion for better performance */
  @media (prefers-reduced-motion: reduce) {
    .chakra-modal__content {
      animation: none !important;
      transition: none !important;
    }
  }
}

/* ===== MOBILE FORM IMPROVEMENTS ===== */

/* Improve form elements in modals on mobile */
@media (max-width: 767px) {
  .chakra-modal__body .chakra-input,
  .chakra-modal__body .chakra-textarea {
    font-size: 16px !important; /* Prevent zoom on iOS */
    padding: 12px 16px !important;
    border-radius: 12px !important;
    min-height: 44px !important;
  }
  
  .chakra-modal__body .chakra-textarea {
    min-height: 80px !important;
    resize: none !important;
  }
  
  .chakra-modal__body .chakra-button {
    min-height: 44px !important;
    padding: 12px 20px !important;
    border-radius: 12px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
  }
}

/* ===== MOBILE MODAL SPECIFIC CLASSES ===== */

/* Create Post Modal mobile optimizations */
@media (max-width: 767px) {
  .create-post-modal .chakra-modal__content {
    max-height: calc(100vh - 20px) !important;
  }
  
  .create-post-modal .chakra-textarea {
    min-height: 120px !important;
    max-height: 200px !important;
  }
  
  .create-post-modal .image-preview {
    max-height: 200px !important;
    border-radius: 12px !important;
  }
}

/* Chat Modal mobile optimizations */
@media (max-width: 767px) {
  .chat-modal .chakra-modal__content {
    height: calc(100vh - 20px) !important;
    max-height: calc(100vh - 20px) !important;
  }
  
  .chat-modal .chakra-modal__body {
    padding: 0 !important;
    display: flex;
    flex-direction: column;
  }
}

/* Follow Modal mobile optimizations */
@media (max-width: 767px) {
  .follow-modal .chakra-modal__content {
    max-height: calc(100vh - 40px) !important;
  }
  
  .follow-modal .user-list {
    max-height: calc(60vh) !important;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* ===== MOBILE SAFE AREAS (iPhone X+) ===== */

/* Handle safe areas on devices with notches */
@supports (padding: max(0px)) {
  @media (max-width: 767px) {
    .chakra-modal__content {
      padding-top: max(16px, env(safe-area-inset-top));
      padding-bottom: max(16px, env(safe-area-inset-bottom));
      padding-left: max(16px, env(safe-area-inset-left));
      padding-right: max(16px, env(safe-area-inset-right));
    }
    
    .glass-navbar {
      padding-bottom: max(16px, calc(16px + env(safe-area-inset-bottom))) !important;
    }
  }
}

/* ===== MOBILE MODAL BACKDROP ===== */

/* Improve backdrop behavior on mobile */
@media (max-width: 767px) {
  .chakra-modal__overlay {
    /* Prevent scrolling behind modal */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    overflow: hidden !important;
  }
}

/* ===== MOBILE KEYBOARD ADJUSTMENTS ===== */

/* Handle virtual keyboard appearance */
@media (max-width: 767px) {
  /* Adjust modal when keyboard is visible */
  .chakra-modal__content:has(.chakra-input:focus),
  .chakra-modal__content:has(.chakra-textarea:focus) {
    transform: translateY(-10vh) !important;
    transition: transform 0.3s ease !important;
  }
  
  /* Ensure send buttons remain accessible */
  .message-input-container {
    position: sticky !important;
    bottom: 0 !important;
    background: inherit !important;
    z-index: 10 !important;
  }
}
