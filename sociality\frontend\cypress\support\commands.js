// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Custom command for login (adjust based on your authentication system)
Cypress.Commands.add('login', (email = '<EMAIL>', password = 'password123') => {
  cy.visit('/auth');
  cy.get('input[placeholder="Enter your username"]').type(email);
  cy.get('input[placeholder="Enter your password"]').type(password);
  cy.get('button').contains('Login').click();
  cy.url().should('not.include', '/auth');
});

// Enhanced login command with API mocking
Cypress.Commands.add('loginWithMock', (userData = {}) => {
  const defaultUser = {
    _id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    username: 'testuser',
    bio: 'Test bio',
    profilePic: '',
    isProfileComplete: true
  };

  const user = { ...defaultUser, ...userData };

  cy.intercept('POST', '/api/users/login', {
    statusCode: 200,
    body: user
  }).as('loginRequest');

  cy.visit('/auth');
  cy.get('input[placeholder="Enter your username"]').type(user.username);
  cy.get('input[placeholder="Enter your password"]').type('password123');
  cy.contains('button', 'Login').click();
  
  cy.wait('@loginRequest');
  cy.url().should('not.include', '/auth');
});

// Custom command for logout
Cypress.Commands.add('logout', () => {
  cy.get('[data-testid="user-menu"]').click();
  cy.contains('Logout').click();
  cy.url().should('include', '/auth');
});

// Custom command to setup authenticated user without login flow
Cypress.Commands.add('setupAuthenticatedUser', (userData = {}) => {
  const defaultUser = {
    _id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    username: 'testuser',
    bio: 'Test bio',
    profilePic: '',
    isProfileComplete: true
  };

  const user = { ...defaultUser, ...userData };

  cy.window().then((win) => {
    // Set tab ID if not exists
    const tabId = win.sessionStorage.getItem('tabId') || '1';
    win.sessionStorage.setItem('tabId', tabId);
    
    // Set user data with tab-specific key
    const userKey = `user-threads-${tabId}`;
    win.localStorage.setItem(userKey, JSON.stringify(user));
  });
});

// Custom command to wait for API calls with proper error handling
Cypress.Commands.add('waitForApi', (alias, expectedStatus = [200, 201, 204]) => {
  cy.wait(alias).then((interception) => {
    const statuses = Array.isArray(expectedStatus) ? expectedStatus : [expectedStatus];
    expect(statuses).to.include(interception.response.statusCode);
  });
});

// Custom command to create a test post
Cypress.Commands.add('createPost', (content = 'Test post content', shouldWait = true) => {
  if (shouldWait) {
    cy.intercept('POST', '/api/posts/create', {
      statusCode: 201,
      body: {
        _id: 'new-post-id',
        text: content,
        postedBy: {
          _id: 'test-user-id',
          username: 'testuser',
          name: 'Test User'
        },
        likes: [],
        replies: [],
        createdAt: new Date().toISOString()
      }
    }).as('createPost');
  }

  cy.get('[data-testid="create-post-input"]').type(content);
  cy.get('[data-testid="create-post-button"]').click();
  
  if (shouldWait) {
    cy.wait('@createPost');
  }
  
  cy.contains(content).should('be.visible');
});

// Custom command to clear local storage and cookies
Cypress.Commands.add('clearAppData', () => {
  cy.clearLocalStorage();
  cy.clearCookies();
  cy.window().then((win) => {
    win.sessionStorage.clear();
    // Clear any custom storage
    if (win.indexedDB && win.indexedDB.deleteDatabase) {
      win.indexedDB.deleteDatabase('sociality-app');
    }
  });
});

// Custom command to mock common API endpoints
Cypress.Commands.add('mockCommonAPIs', () => {
  cy.intercept('GET', '/api/posts/feed', { fixture: 'posts.json' }).as('getFeed');
  cy.intercept('GET', '/api/users/suggested', { fixture: 'users.json' }).as('getSuggestedUsers');
  cy.intercept('GET', '/api/messages/conversations', { body: [] }).as('getConversations');
  cy.intercept('GET', '/api/notifications', { body: [] }).as('getNotifications');
});

// Custom command to simulate typing with realistic delays
Cypress.Commands.add('typeNaturally', { prevSubject: 'element' }, (subject, text, options = {}) => {
  const { delay = 100, variation = 50 } = options;
  
  cy.wrap(subject).focus();
  
  for (let i = 0; i < text.length; i++) {
    const randomDelay = delay + (Math.random() * variation * 2 - variation);
    cy.wrap(subject).type(text[i], { delay: randomDelay });
  }
});

// Custom command to check accessibility
Cypress.Commands.add('checkA11y', (context = null, options = {}) => {
  const defaultOptions = {
    runOnly: {
      type: 'tag',
      values: ['wcag2a', 'wcag2aa']
    }
  };
  
  cy.injectAxe();
  cy.checkA11y(context, { ...defaultOptions, ...options });
});

// Custom command to test responsive behavior
Cypress.Commands.add('testResponsive', (testCallback) => {
  const viewports = [
    { width: 375, height: 667, name: 'iPhone SE' },
    { width: 768, height: 1024, name: 'iPad' },
    { width: 1280, height: 720, name: 'Desktop' }
  ];

  viewports.forEach(viewport => {
    cy.viewport(viewport.width, viewport.height);
    cy.log(`Testing on ${viewport.name} (${viewport.width}x${viewport.height})`);
    testCallback(viewport);
  });
});

// Custom command to simulate network conditions
Cypress.Commands.add('simulateSlowNetwork', (delay = 2000) => {
  cy.intercept('**', (req) => {
    req.reply((res) => {
      res.setDelay(delay);
      res.send();
    });
  });
});

// Custom command to check console errors
Cypress.Commands.add('checkConsoleErrors', () => {
  cy.window().then((win) => {
    const originalError = win.console.error;
    const errors = [];
    
    win.console.error = (...args) => {
      errors.push(args);
      originalError.apply(win.console, args);
    };
    
    cy.wrap(errors).as('consoleErrors');
  });
});

// Custom command to verify no console errors occurred
Cypress.Commands.add('verifyNoConsoleErrors', () => {
  cy.get('@consoleErrors').then((errors) => {
    expect(errors).to.be.empty;
  });
});

// Custom command to simulate user interactions
Cypress.Commands.add('simulateUserBehavior', () => {
  // Simulate realistic user behavior patterns
  cy.get('body').trigger('mousemove', { clientX: 100, clientY: 100 });
  cy.wait(Math.random() * 1000 + 500);
  
  // Random scroll
  if (Math.random() > 0.5) {
    cy.scrollTo(0, Math.random() * 500);
    cy.wait(Math.random() * 2000 + 1000);
  }
});

// Custom command to test keyboard navigation
Cypress.Commands.add('testKeyboardNavigation', (startElement) => {
  cy.get(startElement).focus();
  
  // Test Tab navigation
  cy.focused().tab();
  cy.focused().should('not.equal', startElement);
  
  // Test Shift+Tab navigation
  cy.focused().tab({ shift: true });
  cy.focused().should('equal', startElement);
});

// Custom command to test performance metrics
Cypress.Commands.add('measurePerformance', (operationName) => {
  cy.window().then((win) => {
    win.performance.mark(`${operationName}-start`);
  });
  
  return {
    end: () => {
      cy.window().then((win) => {
        win.performance.mark(`${operationName}-end`);
        win.performance.measure(operationName, `${operationName}-start`, `${operationName}-end`);
        
        const measures = win.performance.getEntriesByName(operationName);
        const duration = measures[measures.length - 1].duration;
        
        cy.log(`${operationName} took ${duration.toFixed(2)}ms`);
        return cy.wrap(duration);
      });
    }
  };
});

// Custom command to test form validation
Cypress.Commands.add('testFormValidation', (formSelector, fields) => {
  fields.forEach(field => {
    cy.get(`${formSelector} ${field.selector}`).clear();
    if (field.invalidValue) {
      cy.get(`${formSelector} ${field.selector}`).type(field.invalidValue);
    }
    cy.get(`${formSelector} button[type="submit"]`).click();
    
    if (field.errorMessage) {
      cy.contains(field.errorMessage).should('be.visible');
    }
  });
});

// Custom command to test infinite scroll
Cypress.Commands.add('testInfiniteScroll', (containerSelector = 'body') => {
  let previousItemCount = 0;
  let currentItemCount = 0;
  
  cy.get('[data-testid="post-item"]').then($items => {
    previousItemCount = $items.length;
  });
  
  cy.get(containerSelector).scrollTo('bottom');
  cy.wait(2000); // Wait for new items to load
  
  cy.get('[data-testid="post-item"]').then($items => {
    currentItemCount = $items.length;
    expect(currentItemCount).to.be.greaterThan(previousItemCount);
  });
});

// Custom command to test drag and drop
Cypress.Commands.add('dragAndDrop', { prevSubject: 'element' }, (subject, targetSelector) => {
  cy.wrap(subject)
    .trigger('mousedown', { button: 0 })
    .wait(100);
    
  cy.get(targetSelector)
    .trigger('mousemove')
    .trigger('mouseup');
});

// Custom command for visual regression testing
Cypress.Commands.add('visualSnapshot', (name, options = {}) => {
  cy.matchImageSnapshot(name, {
    threshold: 0.1,
    thresholdType: 'percent',
    ...options
  });
});

// Custom command to mock API responses
Cypress.Commands.add('mockApiResponse', (method, url, response, statusCode = 200) => {
  cy.intercept(method, url, {
    statusCode,
    body: response,
  });
});

// Custom command to check responsive design
Cypress.Commands.add('checkResponsive', () => {
  // Test mobile view
  cy.viewport(375, 667);
  cy.wait(500);
  
  // Test tablet view
  cy.viewport(768, 1024);
  cy.wait(500);
  
  // Test desktop view
  cy.viewport(1280, 720);
  cy.wait(500);
});

// Custom command to setup authenticated user
Cypress.Commands.add('setupAuthenticatedUser', (user = {}) => {
  const defaultUser = {
    _id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    username: 'testuser',
    bio: 'Test bio',
    profilePic: '',
    isProfileComplete: true,
    ...user
  };

  cy.window().then((win) => {
    win.localStorage.setItem('user-threads', JSON.stringify(defaultUser));
  });
});

// Custom command to mock socket connection
Cypress.Commands.add('mockSocket', () => {
  cy.window().then((win) => {
    win.socket = {
      emit: cy.stub(),
      on: cy.stub(),
      off: cy.stub(),
      disconnect: cy.stub(),
      connected: true
    };
  });
});

// Custom command to simulate typing with realistic delays
Cypress.Commands.add('typeRealistically', { prevSubject: 'element' }, (subject, text, options = {}) => {
  const { delay = 100, ...typeOptions } = options;
  
  cy.wrap(subject).type(text, {
    delay,
    ...typeOptions
  });
});

// Custom command to check accessibility
Cypress.Commands.add('checkA11y', (context, options) => {
  cy.injectAxe();
  cy.checkA11y(context, options);
});

// Custom command to take visual regression screenshots
Cypress.Commands.add('visualSnapshot', (name) => {
  if (Cypress.env('VISUAL_TESTING')) {
    cy.percySnapshot(name);
  }
});

// Custom command to simulate network conditions
Cypress.Commands.add('simulateSlowNetwork', () => {
  cy.intercept('**/*', (req) => {
    req.reply((res) => {
      res.delay(2000); // 2 second delay
    });
  });
});

// Custom command to test error boundaries
Cypress.Commands.add('triggerError', (errorType = 'generic') => {
  cy.window().then((win) => {
    if (errorType === 'network') {
      // Simulate network error
      win.fetch = cy.stub().rejects(new Error('Network error'));
    } else if (errorType === 'javascript') {
      // Trigger JavaScript error
      win.eval('throw new Error("Test error")');
    }
  });
});

// Custom command for keyboard navigation testing
Cypress.Commands.add('tabForward', { prevSubject: 'optional' }, (subject) => {
  if (subject) {
    cy.wrap(subject).type('{tab}');
  } else {
    cy.get('body').type('{tab}');
  }
});

Cypress.Commands.add('tabBackward', { prevSubject: 'optional' }, (subject) => {
  if (subject) {
    cy.wrap(subject).type('{shift}{tab}');
  } else {
    cy.get('body').type('{shift}{tab}');
  }
});

// Custom command to wait for animations to complete
Cypress.Commands.add('waitForAnimations', () => {
  cy.get('[data-testid*="loading"]').should('not.exist');
  cy.get('.loading').should('not.exist');
  cy.wait(300); // Wait for CSS transitions
});

// Custom command to simulate different user preferences
Cypress.Commands.add('setUserPreferences', (preferences = {}) => {
  const {
    theme = 'light',
    reducedMotion = false,
    language = 'en',
    ...otherPrefs
  } = preferences;

  cy.window().then((win) => {
    // Set theme
    if (theme === 'dark') {
      win.document.body.classList.add('chakra-ui-dark');
    }

    // Set reduced motion
    if (reducedMotion) {
      win.document.body.classList.add('reduced-motion');
    }

    // Set language
    win.document.documentElement.lang = language;

    // Store preferences
    win.localStorage.setItem('user-preferences', JSON.stringify({
      theme,
      reducedMotion,
      language,
      ...otherPrefs
    }));
  });
});

// Custom command to test with different data sets
Cypress.Commands.add('useFixture', (fixtureName) => {
  cy.fixture(fixtureName).as('testData');
});

// Custom command to simulate real user interactions
Cypress.Commands.add('simulateUserInteraction', (action, target, options = {}) => {
  switch (action) {
    case 'hover':
      cy.get(target).trigger('mouseover', options);
      break;
    case 'doubleClick':
      cy.get(target).dblclick(options);
      break;
    case 'rightClick':
      cy.get(target).rightclick(options);
      break;
    case 'drag':
      cy.get(target).trigger('mousedown', { which: 1 });
      cy.get(options.dropTarget).trigger('mousemove').trigger('mouseup');
      break;
    case 'swipe':
      cy.get(target)
        .trigger('touchstart', { touches: [{ clientX: 100, clientY: 100 }] })
        .trigger('touchmove', { touches: [{ clientX: 200, clientY: 100 }] })
        .trigger('touchend');
      break;
    default:
      cy.get(target).click(options);
  }
});

// Custom command to validate form fields
Cypress.Commands.add('validateFormField', (selector, validationRules) => {
  const { required, minLength, maxLength, pattern, type } = validationRules;

  if (required) {
    cy.get(selector).should('have.attr', 'required');
  }

  if (minLength) {
    cy.get(selector).should('have.attr', 'minlength', minLength.toString());
  }

  if (maxLength) {
    cy.get(selector).should('have.attr', 'maxlength', maxLength.toString());
  }

  if (pattern) {
    cy.get(selector).should('have.attr', 'pattern', pattern);
  }

  if (type) {
    cy.get(selector).should('have.attr', 'type', type);
  }
});

// Custom command to test loading states
Cypress.Commands.add('testLoadingState', (triggerAction, loadingSelector = '[data-testid="loading"]') => {
  // Execute action that triggers loading
  triggerAction();
  
  // Verify loading state appears
  cy.get(loadingSelector).should('be.visible');
  
  // Wait for loading to complete
  cy.get(loadingSelector).should('not.exist');
});

// Custom command to handle file uploads
Cypress.Commands.add('uploadFile', (selector, fileName, fileType = '') => {
  cy.get(selector).selectFile(`cypress/fixtures/${fileName}`, {
    force: true,
    action: 'select'
  });
});

// Custom command to test infinite scroll
Cypress.Commands.add('testInfiniteScroll', (containerSelector = 'body') => {
  cy.get(containerSelector).scrollTo('bottom');
  cy.wait(1000);
  cy.get('[data-testid="load-more"], [data-testid="loading"]').should('be.visible');
});

// Custom command to compare screenshots
Cypress.Commands.add('compareSnapshot', (name, options = {}) => {
  if (Cypress.env('VISUAL_TESTING')) {
    cy.percySnapshot(name, options);
  } else {
    cy.screenshot(name, options);
  }
});
